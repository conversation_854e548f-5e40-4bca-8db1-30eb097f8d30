{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9fe2d7fc-5aae-474d-89e5-b6d4a7b9cbb9", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.482047Z", "iopub.status.busy": "2025-06-18T02:01:05.481911Z", "iopub.status.idle": "2025-06-18T02:01:05.536614Z", "shell.execute_reply": "2025-06-18T02:01:05.536306Z"}, "tags": []}, "outputs": [], "source": ["import os\n", "import datetime\n", "import configparser\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "9c2b9cbb-425e-4d44-bb97-f58149959be8", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.538402Z", "iopub.status.busy": "2025-06-18T02:01:05.538298Z", "iopub.status.idle": "2025-06-18T02:01:05.910977Z", "shell.execute_reply": "2025-06-18T02:01:05.910590Z"}, "tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "8c7ef434-a451-4106-bbf5-86df5ca6577b", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.913097Z", "iopub.status.busy": "2025-06-18T02:01:05.912917Z", "iopub.status.idle": "2025-06-18T02:01:05.915684Z", "shell.execute_reply": "2025-06-18T02:01:05.915295Z"}, "tags": []}, "outputs": [], "source": ["from acquire_mapping import Mapping"]}, {"cell_type": "code", "execution_count": 4, "id": "1012df55-d7a4-4175-a52b-f1ee872255da", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.917692Z", "iopub.status.busy": "2025-06-18T02:01:05.917456Z", "iopub.status.idle": "2025-06-18T02:01:05.919847Z", "shell.execute_reply": "2025-06-18T02:01:05.919598Z"}, "tags": []}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 5, "id": "a42ea5ba-da56-417e-afd6-c8d0265233dc", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.921226Z", "iopub.status.busy": "2025-06-18T02:01:05.921062Z", "iopub.status.idle": "2025-06-18T02:01:05.944038Z", "shell.execute_reply": "2025-06-18T02:01:05.943671Z"}, "tags": []}, "outputs": [], "source": ["fiscaldate_mapping = Mapping.fiscaldate\n", "fiscaldate_mapping2 = fiscaldate_mapping.copy()[['Date','week_in_fiscal_quarter','fiscal_qtr_year_name']]\n", "fiscaldate_mapping2['week_in_fiscal_quarter'] = fiscaldate_mapping2['week_in_fiscal_quarter'].str.slice(1)\n", "fiscaldate_mapping2.rename(columns={'Date':'fiscal_dt'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "3f7351c0-697b-48ff-97d3-db4c5099b867", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.945710Z", "iopub.status.busy": "2025-06-18T02:01:05.945603Z", "iopub.status.idle": "2025-06-18T02:01:05.947644Z", "shell.execute_reply": "2025-06-18T02:01:05.947361Z"}, "tags": []}, "outputs": [], "source": ["now = datetime.datetime.now().strftime('%Y-%m-%d')\n", "now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"]}, {"cell_type": "code", "execution_count": 7, "id": "3065d066-fdd1-4f7f-9fb1-e22b2c249e27", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.949434Z", "iopub.status.busy": "2025-06-18T02:01:05.949322Z", "iopub.status.idle": "2025-06-18T02:01:05.959473Z", "shell.execute_reply": "2025-06-18T02:01:05.959198Z"}, "tags": []}, "outputs": [], "source": ["fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_qtr_year_name'].unique().tolist()[0]\n", "week_in_fiscal_quarter = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['week_in_fiscal_quarter'].unique().tolist()[0][1:]\n", "fiscal_week_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_week_year_name'].unique().tolist()[0]\n", "# 如果是单个数字，则在前面添加 '0'\n", "if week_in_fiscal_quarter.isdigit() and len(week_in_fiscal_quarter) == 1:\n", "    week_in_fiscal_quarter2 = '0' + week_in_fiscal_quarter\n", "else:\n", "    week_in_fiscal_quarter2 = week_in_fiscal_quarter\n", "\n", "yqw_addzero = fiscal_qtr_year_name + 'W' + week_in_fiscal_quarter2\n", "next_fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date'] >= now]['fiscal_qtr_year_name'].unique()[1]"]}, {"cell_type": "code", "execution_count": 8, "id": "2e0d6e78-559f-4fbf-82fd-c37a3f8134a3", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.961387Z", "iopub.status.busy": "2025-06-18T02:01:05.961222Z", "iopub.status.idle": "2025-06-18T02:01:05.965257Z", "shell.execute_reply": "2025-06-18T02:01:05.964581Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY25Q3', 'FY25Q2', 'FY25Q1', 'FY24Q4', 'FY24Q3', 'FY24Q2', 'FY24Q1', 'FY23Q4', 'FY23Q3', 'FY23Q2', 'FY23Q1', 'FY22Q4', 'FY22Q3', 'FY22Q2']\n"]}], "source": ["def generate_fiscal_quarters(start_fiscal, end_fiscal):\n", "    start_year = int(start_fiscal[2:4])\n", "    start_qtr = int(start_fiscal[5])\n", "    end_year = int(end_fiscal[2:4])\n", "    end_qtr = int(end_fiscal[5])\n", "\n", "    fiscal_quarters = []\n", "    \n", "    current_year = start_year\n", "    current_qtr = start_qtr\n", "\n", "    while (current_year > end_year) or (current_year == end_year and current_qtr >= end_qtr):\n", "        fiscal_quarters.append(f\"FY{current_year:02d}Q{current_qtr}\")\n", "        if current_qtr == 1:\n", "            current_qtr = 4\n", "            current_year -= 1\n", "        else:\n", "            current_qtr -= 1\n", "\n", "    return fiscal_quarters\n", "\n", "# Example usage\n", "# fiscal_qtr_year_name = \"FY24Q4\"\n", "end_fiscal = \"FY22Q2\"\n", "\n", "fiscal_quarters_list = generate_fiscal_quarters(fiscal_qtr_year_name, end_fiscal)\n", "print(fiscal_quarters_list)"]}, {"cell_type": "code", "execution_count": 9, "id": "ef5a4e2d-12e1-42df-a0f2-0f4401119e66", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.967189Z", "iopub.status.busy": "2025-06-18T02:01:05.967096Z", "iopub.status.idle": "2025-06-18T02:01:05.969449Z", "shell.execute_reply": "2025-06-18T02:01:05.969152Z"}, "tags": []}, "outputs": [], "source": ["# Generate fiscal_quarters_list_ta ae aem Identifier\n", "fiscal_quarters_list_ta = [f\"Account.{fq}_Top_Account__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_ae = [f\"Account.{fq}_AE__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_aem = [f\"Account.{fq}_AEM__c\" for fq in fiscal_quarters_list]\n", "# fiscal_quarters_list_Identifier = [f\"Account.{fq}_Identifier__c\" for fq in fiscal_quarters_list]"]}, {"cell_type": "code", "execution_count": 10, "id": "73d6a4f3-89b5-4cf2-9c3a-d00753db7b00", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.970965Z", "iopub.status.busy": "2025-06-18T02:01:05.970866Z", "iopub.status.idle": "2025-06-18T02:01:05.972784Z", "shell.execute_reply": "2025-06-18T02:01:05.972316Z"}, "tags": []}, "outputs": [], "source": ["# Combine all the lists into one\n", "# all_fiscal_quarters_columns = fiscal_quarters_list_ta + fiscal_quarters_list_ae + fiscal_quarters_list_aem + fiscal_quarters_list_Identifier\n", "all_fiscal_quarters_columns = fiscal_quarters_list_ta + fiscal_quarters_list_ae + fiscal_quarters_list_aem"]}, {"cell_type": "code", "execution_count": 11, "id": "14da3bbd-dd89-4ed9-a158-53b1d557162e", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.974433Z", "iopub.status.busy": "2025-06-18T02:01:05.974302Z", "iopub.status.idle": "2025-06-18T02:01:05.978596Z", "shell.execute_reply": "2025-06-18T02:01:05.978328Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQEAQC3GKIm9QHo8c5FeUfI3vvXEQzLTDleoQt8awFsj00MUBWWiE9yEGd3fdyO.d2j95bqzBrFbK8u7qapKb1kGbdj1GmEA'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 12, "id": "1dbb7d8c-2856-48f1-8f66-a804b92432ad", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.980028Z", "iopub.status.busy": "2025-06-18T02:01:05.979929Z", "iopub.status.idle": "2025-06-18T02:01:05.982116Z", "shell.execute_reply": "2025-06-18T02:01:05.981856Z"}, "tags": []}, "outputs": [], "source": ["\n", "\n", "# def get_report_metadata(base_url, report_id, access_token):\n", "#     url = f\"{base_url}/services/data/v59.0/analytics/reports/{report_id}/describe\"\n", "#     headers = {\n", "#         'Authorization': f'<PERSON><PERSON> {access_token}'\n", "#     }\n", "\n", "#     response = requests.get(url, headers=headers)\n", "#     if response.status_code == 200:\n", "#         report_metadata = response.json()\n", "#         return report_metadata\n", "#     else:\n", "#         print(f\"Failed to get report metadata. Status code: {response.status_code}\")\n", "#         return None\n", "\n", "# def extract_objects_and_fields(report_metadata):\n", "#     objects = set()\n", "#     fields = []\n", "\n", "#     # Extract detail columns\n", "#     detail_columns = report_metadata.get('reportMetadata', {}).get('detailColumns', [])\n", "#     for column in detail_columns:\n", "#         # Split the field name to get the object and field\n", "#         parts = column.split('.')\n", "#         if len(parts) == 2:\n", "#             objects.add(parts[0])\n", "#             fields.append(column)\n", "\n", "#     return objects, fields\n", "\n", "# # Replace these values with your actual Salesforce instance URL, report ID, and access token\n", "# base_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "# report_id = \"00OIS000001PrpR\"\n", "# # access_token = access_token\n", "\n", "# report_metadata = get_report_metadata(base_url, report_id, access_token)\n", "# if report_metadata:\n", "#     objects, fields = extract_objects_and_fields(report_metadata)\n", "#     print(f\"Objects used in the report: {objects}\")\n", "#     print(f\"Fields used in the report: {fields}\")\n", "# else:\n", "#     print(\"Failed to get report metadata.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "dc63d2af-304c-433d-870e-f95c1669fe51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 13, "id": "d54f3e43-40d3-452f-9280-3d1fe43698e9", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.983547Z", "iopub.status.busy": "2025-06-18T02:01:05.983455Z", "iopub.status.idle": "2025-06-18T02:01:05.986443Z", "shell.execute_reply": "2025-06-18T02:01:05.986193Z"}, "tags": []}, "outputs": [], "source": ["\n", "\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"YOUR_ACCESS_TOKEN\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "#241014 增加替换逻辑,按当前Q进行替换\n", "# SOQL query\n", "sql_statement = f\"\"\"\n", "SELECT Account.FY21Q2_Identifier__c, \n", "Account.{fiscal_qtr_year_name}_Identifier__c,\n", "Account.FY22Q1__c,\n", "Account.FY21Q3_Large_Account__c, \n", "Account.FY21Q4_Large_Account__c, \n", "Account.<PERSON>_as_Choice__c, \n", "Account.<PERSON>_as_Choice_start_time__c, \n", "Account.SEI_maC__c, \n", "ACCOUNT.NAME,\n", "Account.SEI_Account_ID__c, \n", "Account.SEI_Cluster_ID__c, \n", "Account.Account_Cluster__c,\n", "Account.Is_Group__c, \n", "Account_ID__c,\n", "Account.Account_Group__c, \n", "Account.SEI_Group_ID__c, \n", "Account.Account_Group_ID__c,\n", "Account.Group_Sub_Segment__c, \n", "Account.Group_Vertical_Industry__c, \n", "Account.Province__c,\n", "Account.City__c, \n", "Account.Group_Province__c, \n", "Account.Group_City__c, \n", "Owner.Name, \n", "Account.Sub_Segment__c,\n", "Account.Vertical_Industry__c, \n", "Account.Top_Account_Deep_Dive__c,\n", "Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c\n", "FROM Account \n", "\"\"\"\n", "\n", "# Insert the fiscal quarters columns after 'Account.FY22Q1__c,'\n", "insert_point = sql_statement.find(\"Account.FY22Q1__c,\") + len(\"Account.FY22Q1__c,\")\n", "sql_statement = sql_statement[:insert_point] + \"\\n    \" + \",\\n    \".join(all_fiscal_quarters_columns) + \",\" + sql_statement[insert_point:]\n", "\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")"]}, {"cell_type": "code", "execution_count": 14, "id": "2067e2c3-95b5-4d36-af30-04e62596cb7c", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.987952Z", "iopub.status.busy": "2025-06-18T02:01:05.987875Z", "iopub.status.idle": "2025-06-18T02:01:05.989707Z", "shell.execute_reply": "2025-06-18T02:01:05.989364Z"}, "tags": []}, "outputs": [], "source": ["\n", "\n", "# # Construct the full URL\n", "# url = f\"{base_url}{sql_statement_single_line}\"\n", "# url"]}, {"cell_type": "code", "execution_count": 15, "id": "1903cc3e-c943-4320-8efe-269c94ef7043", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.991112Z", "iopub.status.busy": "2025-06-18T02:01:05.991010Z", "iopub.status.idle": "2025-06-18T02:01:05.992826Z", "shell.execute_reply": "2025-06-18T02:01:05.992574Z"}, "tags": []}, "outputs": [], "source": ["# url"]}, {"cell_type": "code", "execution_count": null, "id": "6767306a-0a8d-4dc8-9866-cfab4ac253b5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "id": "0f1fb587-0aa6-4561-8c15-31bff44c5729", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.994467Z", "iopub.status.busy": "2025-06-18T02:01:05.994359Z", "iopub.status.idle": "2025-06-18T02:01:05.996744Z", "shell.execute_reply": "2025-06-18T02:01:05.996509Z"}, "tags": []}, "outputs": [], "source": ["# # SOQL query\n", "# sql_statement = \"\"\"\n", "# SELECT Account.FY21Q2_Identifier__c, \n", "# Account.FY22Q1__c, \n", "# Account.FY24Q4_Top_Account__c, \n", "# Account.FY24Q3_Top_Account__c, \n", "# Account.FY24Q2_Top_Account__c, \n", "# Account.FY24Q1_Top_Account__c, \n", "# Account.FY23Q4_Top_Account__c, \n", "# Account.FY23Q3_Top_Account__c, \n", "# Account.FY23Q2_Top_Account__c, \n", "# Account.FY23Q1_Top_Account__c, \n", "# Account.FY22Q4_Top_Account__c, \n", "# Account.FY22Q3_Top_Account__c, \n", "# Account.FY22Q2_Top_Account__c, \n", "# Account.FY24Q4_AE__c, \n", "# Account.FY24Q3_AE__c, \n", "# Account.FY24Q2_AE__c, \n", "# Account.FY24Q1_AE__c, \n", "# Account.FY23Q4_AE__c, \n", "# Account.FY23Q3_AE__c, \n", "# Account.FY23Q2_AE__c, \n", "# Account.FY23Q1_AE__c, \n", "# Account.FY22Q4_AE__c, \n", "# Account.FY22Q3_AE__c, \n", "# Account.FY22Q2_AE__c, \n", "# Account.FY24Q4_AEM__c, \n", "# Account.FY24Q3_AEM__c, \n", "# Account.FY24Q2_AEM__c, \n", "# Account.FY24Q1_AEM__c, \n", "# Account.FY23Q4_AEM__c, \n", "# Account.FY23Q3_AEM__c, \n", "# Account.FY23Q2_AEM__c, \n", "# Account.FY23Q1_AEM__c, \n", "# Account.FY22Q4_AEM__c, \n", "# Account.FY22Q3_AEM__c, \n", "# Account.FY22Q2_AEM__c, \n", "\n", "# Account.FY24Q3_Identifier__c, \n", "# Account.FY24Q2_Identifier__c, \n", "# Account.FY24Q1_Identifier__c, \n", "# Account.FY23Q4_Identifier__c, \n", "# Account.FY23Q3_Identifier__c, \n", "# Account.FY23Q2_Identifier__c, \n", "# Account.FY23Q1_Identifier__c, \n", "# Account.FY22Q4_Identifier__c, \n", "# Account.FY22Q3_Identifier__c, \n", "# Account.FY22Q2_Identifier__c, \n", "# Account.FY21Q3_Large_Account__c, \n", "# Account.FY21Q4_Large_Account__c, \n", "# Account.<PERSON>_as_Choice__c, \n", "# Account.<PERSON>_as_Choice_start_time__c, \n", "# Account.SEI_maC__c, \n", "# ACCOUNT.NAME, \n", "# Account.SEI_Account_ID__c, \n", "# Account.SEI_Cluster_ID__c, \n", "# Account.Account_Cluster__c, \n", "# Account.Is_Group__c, \n", "# Account_ID__c, \n", "# Account.Account_Group__c, \n", "# Account.SEI_Group_ID__c, \n", "# Account.Account_Group_ID__c, \n", "# Account.Group_Sub_Segment__c, \n", "# Account.Group_Vertical_Industry__c, \n", "# Account.Province__c, \n", "# Account.City__c, \n", "# Account.Group_Province__c, \n", "# Account.Group_City__c, \n", "# Owner.Name, \n", "# Account.Sub_Segment__c, \n", "# Account.Vertical_Industry__c, \n", "# Account.Top_Account_Deep_Dive__c,\n", "# Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c\n", "# FROM Account\n", "# limit 100\n", "# \"\"\"\n", "\n", "# # Remove newlines and extra spaces from SQL statement\n", "# sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")"]}, {"cell_type": "code", "execution_count": null, "id": "a85dab9b-c26f-438d-aee1-95af8c324e10", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "87e21d29-77f3-4ae0-9cfd-06ef0cfc04f3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "063e834d-369d-49c0-a22e-906e8807dc73", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:01:05.998116Z", "iopub.status.busy": "2025-06-18T02:01:05.998027Z", "iopub.status.idle": "2025-06-18T02:03:08.173443Z", "shell.execute_reply": "2025-06-18T02:03:08.172690Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-2000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-4000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-6000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-8000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-10000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-12000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-14000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-16000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-18000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-20000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-22000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-24000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-26000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-28000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-30000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-32000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-34000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-36000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-38000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-40000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-42000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-44000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-46000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-48000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-50000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-52000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-54000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-56000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-58000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-60000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-62000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-64000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-66000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-68000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-70000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-72000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-74000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-76000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-78000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-80000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-82000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-84000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-86000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-88000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-90000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-92000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-94000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-96000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-98000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-100000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-102000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-104000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-106000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-108000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-110000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-112000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-114000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-116000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-118000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-120000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-122000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-124000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-126000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-128000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-130000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-132000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-134000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-136000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-138000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-140000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-142000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-144000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-146000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-148000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-150000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-152000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-154000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-156000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-158000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-160000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-162000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-164000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-166000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-168000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-170000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-172000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-174000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-176000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-178000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-180000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-182000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-184000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-186000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-188000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-190000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-192000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-194000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-196000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-198000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-200000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-202000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-204000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-206000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-208000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-210000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4KY8PqnJ0oAWE-212000\n"]}], "source": ["\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "bcf01baf-7d5c-4fa2-9642-c3295dd02081", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:08.179224Z", "iopub.status.busy": "2025-06-18T02:03:08.179023Z", "iopub.status.idle": "2025-06-18T02:03:14.370057Z", "shell.execute_reply": "2025-06-18T02:03:14.369016Z"}, "tags": []}, "outputs": [], "source": ["# Convert to DataFrame\n", "df = pd.json_normalize(all_records)"]}, {"cell_type": "code", "execution_count": 19, "id": "c298688f-7094-46ca-af89-5b7027500f6b", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.374714Z", "iopub.status.busy": "2025-06-18T02:03:14.374519Z", "iopub.status.idle": "2025-06-18T02:03:14.518784Z", "shell.execute_reply": "2025-06-18T02:03:14.518443Z"}, "tags": []}, "outputs": [], "source": ["df.drop(['attributes.type',\n", "       'attributes.url', 'Owner.attributes.type', 'Owner.attributes.url'],axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": 20, "id": "8bd9690e-5a1f-4e4f-8ced-c333becc79d7", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.520479Z", "iopub.status.busy": "2025-06-18T02:03:14.520373Z", "iopub.status.idle": "2025-06-18T02:03:14.523400Z", "shell.execute_reply": "2025-06-18T02:03:14.523171Z"}, "tags": []}, "outputs": [], "source": ["# 删除列名中的 '__c' 并替换 '_' 为 ' '\n", "df.columns = df.columns.str.replace('__c$', '', regex=True).str.replace('_', ' ')"]}, {"cell_type": "code", "execution_count": 21, "id": "33ed42e8-40fc-4012-9594-8d1deec28426", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.524843Z", "iopub.status.busy": "2025-06-18T02:03:14.524751Z", "iopub.status.idle": "2025-06-18T02:03:14.527006Z", "shell.execute_reply": "2025-06-18T02:03:14.526774Z"}, "tags": []}, "outputs": [], "source": ["df.rename(columns={'Name':'Account Name','Owner.Name':'Account Owner','Province':'Province/Region','FY22Q1':'FY22Q1 Large Account','Apple HQ ID':'Apple ID',\n", "                   'zhanbaoshijian':'Mac as Choice加入时间','Mac as Choice start time':'Mac as Choice start time','SEI maC':'SEI MaC'\n", "                }, inplace=True)"]}, {"cell_type": "code", "execution_count": 22, "id": "02da4f2e-8731-4387-b61f-1e1ce4fbb57e", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.528320Z", "iopub.status.busy": "2025-06-18T02:03:14.528225Z", "iopub.status.idle": "2025-06-18T02:03:14.530309Z", "shell.execute_reply": "2025-06-18T02:03:14.530076Z"}}, "outputs": [], "source": ["# df = df[['FY21Q2_Identifier__c', 'FY24Q3_Identifier__c', 'FY22Q1__c',\n", "#        'FY21Q3_Large_Account__c', 'FY21Q4_Large_Account__c',\n", "#        'FY24Q2_Top_Account__c', 'FY22Q2_Top_Account__c',\n", "#        'FY22Q3_Top_Account__c', 'FY22Q4_Top_Account__c',\n", "#        'FY23Q1_Top_Account__c', 'FY23Q2_Top_Account__c',\n", "#        'FY23Q3_Top_Account__c', 'FY23Q4_Top_Account__c',\n", "#        'FY24Q1_Top_Account__c', 'FY24Q3_Top_Account__c', 'FY24Q2_AE__c',\n", "#        'FY24Q2_AEM__c', 'FY24Q3_AE__c', 'FY24Q3_AEM__c', 'Mac_as_Choice__c',\n", "#        'Mac_as_Choice_start_time__c', 'SEI_maC__c', 'Name',\n", "#        'SEI_Account_ID__c', 'SEI_Cluster_ID__c', 'Account_Cluster__c',\n", "#        'Is_Group__c', 'Account_ID__c', 'Account_Group__c', 'SEI_Group_ID__c',\n", "#        'Account_Group_ID__c', 'Group_Sub_Segment__c',\n", "#        'Group_Vertical_Industry__c', 'Province__c', 'City__c',\n", "#        'Group_Province__c', 'Group_City__c', 'Owner.Name', 'Sub_Segment__c',\n", "#        'Vertical_Industry__c', 'Top_Account_Deep_Dive__c']]"]}, {"cell_type": "code", "execution_count": 23, "id": "22800b04-464b-4df7-9293-1704ee9f31a9", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.531490Z", "iopub.status.busy": "2025-06-18T02:03:14.531407Z", "iopub.status.idle": "2025-06-18T02:03:14.532793Z", "shell.execute_reply": "2025-06-18T02:03:14.532580Z"}, "tags": []}, "outputs": [], "source": ["# # 利用旧的数据检查表头是否有缺失\n", "# df2 = pd.read_csv('top_account_all_header.csv')\n", "\n", "# # 检查是否有缺失的列\n", "# missing_elements = set(df2) - set(df)\n", "# print(missing_elements)"]}, {"cell_type": "code", "execution_count": 24, "id": "5a7afe19-45fc-4965-aff6-f75c32cf61f2", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.534020Z", "iopub.status.busy": "2025-06-18T02:03:14.533935Z", "iopub.status.idle": "2025-06-18T02:03:14.535853Z", "shell.execute_reply": "2025-06-18T02:03:14.535626Z"}, "tags": []}, "outputs": [], "source": ["# df.columns = df2.columns"]}, {"cell_type": "code", "execution_count": 25, "id": "02186f27-65a3-4630-9b22-370cab62d0e3", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.537586Z", "iopub.status.busy": "2025-06-18T02:03:14.537431Z", "iopub.status.idle": "2025-06-18T02:03:14.546608Z", "shell.execute_reply": "2025-06-18T02:03:14.546371Z"}, "tags": []}, "outputs": [], "source": ["# 查找布尔类型的列并转换为 1 和 0\n", "for col in df.select_dtypes(include='bool').columns:\n", "    df[col] = df[col].astype(int)\n"]}, {"cell_type": "code", "execution_count": 26, "id": "0e62dc76-7f74-4956-8ea4-03c2866ae5cb", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.548026Z", "iopub.status.busy": "2025-06-18T02:03:14.547934Z", "iopub.status.idle": "2025-06-18T02:03:14.549613Z", "shell.execute_reply": "2025-06-18T02:03:14.549400Z"}, "tags": []}, "outputs": [], "source": ["# df[df['Account Group']=='天津航空有限责任公司']['FY24Q3 Top Account']"]}, {"cell_type": "code", "execution_count": 27, "id": "ee67dbb3-cf71-44db-b234-d9954edbb0f4", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:14.550804Z", "iopub.status.busy": "2025-06-18T02:03:14.550719Z", "iopub.status.idle": "2025-06-18T02:03:15.975630Z", "shell.execute_reply": "2025-06-18T02:03:15.975254Z"}, "tags": []}, "outputs": [], "source": ["df.to_csv(f'{homepath}/Library/CloudStorage/Box-Box/Planning Team/Tableau Auto-Refresh Raw Data/19 SalesForce PPL/00 All Account List/All Account List_api.csv')"]}, {"cell_type": "code", "execution_count": 28, "id": "0c1fe850-a4ff-4c95-ab79-ca55fe53a938", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:15.977664Z", "iopub.status.busy": "2025-06-18T02:03:15.977537Z", "iopub.status.idle": "2025-06-18T02:03:17.506856Z", "shell.execute_reply": "2025-06-18T02:03:17.505241Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data has been saved to All_Top_List.csv\n"]}], "source": ["\n", "\n", "# Save to CSV\n", "#250618 删除输出\n", "# df.to_csv(f'{homepath}/Documents/ML/started/ying_support/MacTopAccount/All_Top_List.csv', index=False)\n", "\n", "# print(\"Data has been saved to All_Top_List.csv\")"]}, {"cell_type": "code", "execution_count": 29, "id": "ac5f8803-f780-4a0b-8150-267ee017fef0", "metadata": {"execution": {"iopub.execute_input": "2025-06-18T02:03:17.513623Z", "iopub.status.busy": "2025-06-18T02:03:17.513463Z", "iopub.status.idle": "2025-06-18T02:03:17.516043Z", "shell.execute_reply": "2025-06-18T02:03:17.515790Z"}}, "outputs": [], "source": ["# df2 = pd.read_csv('report1716360761066.csv')\n", "\n", "\n", "# df11 = df[['Name','Owner.Name']].drop_duplicates()\n", "# df11.columns=['Account Name','Account Owner']\n", "\n", "# df11\n", "\n", "# df21 = df2[['Account Name','Account Owner']].drop_duplicates()\n", "\n", "# df21 = df21[~df21['Account Name'].isna()]\n", "# df21['Flag'] = 'Yes'\n", "\n", "# df12 = df11.merge(df21, on=['Account Name','Account Owner'], how='left')\n", "# # df12['flag']=np.where(df12['Account Owner_x']!=df12['Account Owner_y'], <PERSON><PERSON><PERSON>, True)\n", "\n", "\n", "# df12[df12['Flag']=='Yes']\n", "\n", "# df12[df12['Flag'].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "1c471ad4-c9d5-4e69-b7de-8df851f7d838", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}