{"cells": [{"cell_type": "code", "execution_count": 1, "id": "260e42ad-9925-40e5-945b-80adfc50ac87", "metadata": {}, "outputs": [], "source": ["import json\n", "import csv"]}, {"cell_type": "code", "execution_count": 58, "id": "03f5663c-328c-4a17-ad7d-baaa0e8ff71d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "7f3d0793-db25-4d3a-92e8-ed3b07c2e268", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["IOPub data rate exceeded.\n", "The Jupyter server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--ServerApp.iopub_data_rate_limit`.\n", "\n", "Current values:\n", "ServerApp.iopub_data_rate_limit=1000000.0 (bytes/sec)\n", "ServerApp.rate_limit_window=3.0 (secs)\n", "\n"]}], "source": ["# 指定 JSON 文件路径\n", "file_path = '/Users/<USER>/Documents/response.json'\n", "\n", "# 读取并解析 JSON 文件\n", "with open(file_path, 'r') as file:\n", "    data = json.load(file)\n", "\n", "# 以漂亮的格式打印 JSON 数据\n", "pretty_json = json.dumps(data, indent=4)\n", "print(pretty_json)"]}, {"cell_type": "code", "execution_count": 6, "id": "49e00363-c7bb-46a1-90f1-178099a91aac", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'reportName'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 28\u001b[0m\n\u001b[1;32m     25\u001b[0m     response_json \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mload(file)\n\u001b[1;32m     27\u001b[0m \u001b[38;5;66;03m# 将JSON转换为CSV\u001b[39;00m\n\u001b[0;32m---> 28\u001b[0m \u001b[43mjson_to_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse_json\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     30\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConversion complete. CSV file generated.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[6], line 7\u001b[0m, in \u001b[0;36mjson_to_csv\u001b[0;34m(json_data)\u001b[0m\n\u001b[1;32m      4\u001b[0m csv_writer \u001b[38;5;241m=\u001b[39m csv\u001b[38;5;241m.\u001b[39mwriter(csvfile)\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# 获取报告名称\u001b[39;00m\n\u001b[0;32m----> 7\u001b[0m report_name \u001b[38;5;241m=\u001b[39m \u001b[43mjson_data\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mreportName\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[1;32m      8\u001b[0m csv_writer\u001b[38;5;241m.\u001b[39mwriterow([report_name])\n\u001b[1;32m     10\u001b[0m \u001b[38;5;66;03m# 提取列标题\u001b[39;00m\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'reportName'"]}], "source": ["\n", "def json_to_csv(json_data):\n", "    # 打开 CSV 文件以写入\n", "    with open('report_data.csv', 'w', newline='', encoding='utf-8') as csvfile:\n", "        csv_writer = csv.writer(csvfile)\n", "\n", "        # 获取报告名称\n", "        report_name = json_data['reportName']\n", "        csv_writer.writerow([report_name])\n", "        \n", "        # 提取列标题\n", "        columns = []\n", "        for cell in json_data['factMap']['T!T']['rows'][0]['dataCells']:\n", "            columns.append(cell['label'])\n", "        csv_writer.writerow(columns)\n", "\n", "        # 提取行数据并写入CSV文件\n", "        for row in json_data['factMap']['T!T']['rows']:\n", "            row_data = []\n", "            for cell in row['dataCells']:\n", "                row_data.append(cell['label'])\n", "            csv_writer.writerow(row_data)\n", "\n", "# 从文件中读取JSON数据\n", "with open('/Users/<USER>/Documents/response.json', 'r', encoding='utf-8') as file:\n", "    response_json = json.load(file)\n", "\n", "# 将JSON转换为CSV\n", "json_to_csv(response_json)\n", "\n", "print(\"Conversion complete. CSV file generated.\")\n"]}, {"cell_type": "code", "execution_count": 24, "id": "3475d61a-7964-4ecc-9b58-2d5d41ee2b43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Conversion complete. CSV file generated.\n"]}], "source": ["import csv\n", "import json\n", "\n", "def json_to_csv(json_data):\n", "    # 打开 CSV 文件以写入\n", "    with open('report_data.csv', 'w', newline='', encoding='utf-8') as csvfile:\n", "        csv_writer = csv.writer(csvfile)\n", "\n", "        # 获取报告名称，如果不存在则默认为 'Report Name'\n", "        report_name = json_data['attributes']['reportName']\n", "        csv_writer.writerow([report_name])\n", "        \n", "        # 提取列标题\n", "        columns = []\n", "        for cell in json_data['factMap']['T!T']['rows'][0]['dataCells']:\n", "            columns.append(cell['label'])\n", "        csv_writer.writerow(columns)\n", "\n", "        # 提取行数据并写入CSV文件\n", "        for row in json_data['factMap']['T!T']['rows']:\n", "            row_data = []\n", "            for cell in row['dataCells']:\n", "                row_data.append(cell['label'])\n", "            csv_writer.writerow(row_data)\n", "\n", "# 从文件中读取JSON数据\n", "with open('/Users/<USER>/Documents/response.json', 'r', encoding='utf-8') as file:\n", "    response_json = json.load(file)\n", "\n", "# 将JSON转换为CSV\n", "json_to_csv(response_json)\n", "\n", "print(\"Conversion complete. CSV file generated.\")\n"]}, {"cell_type": "code", "execution_count": 22, "id": "29377ea5-dc45-4919-bd2f-17c7cbdfac07", "metadata": {}, "outputs": [], "source": ["# 从文件中读取JSON数据\n", "with open('/Users/<USER>/Documents/response.json', 'r', encoding='utf-8') as file:\n", "    response_json = json.load(file)"]}, {"cell_type": "code", "execution_count": 13, "id": "e02d17f9-8fcf-46b1-8340-db09021f4868", "metadata": {}, "outputs": [], "source": ["# 将JSON转换为CSV\n", "response_json.get('reportName')"]}, {"cell_type": "code", "execution_count": 23, "id": "1542bfc9-80f2-43f5-9ad6-0c2c993ed4c9", "metadata": {}, "outputs": [{"data": {"text/plain": ["'FY24Q3 Top List'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["response_json['attributes']['reportName']"]}, {"cell_type": "code", "execution_count": 15, "id": "4e37d74a-acb1-49a0-90c2-cf6459087fb1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12345\n"]}], "source": ["\n", "# 假设这是你的JSON响应字符串\n", "response_json = '''\n", "{\n", "    \"status\": \"success\",\n", "    \"data\": {\n", "        \"reportId\": \"12345\",\n", "        \"name\": \"Test Report\"\n", "    }\n", "}\n", "'''\n", "\n", "# 解析JSON响应\n", "response_dict = json.loads(response_json)\n", "\n", "# 提取reportId的值\n", "report_id = response_dict.get('data', {}).get('reportId')\n", "\n", "print(report_id)  # 输出: 12345"]}, {"cell_type": "code", "execution_count": 17, "id": "461e1cbf-68ce-4073-b1cb-8ceb0294c6c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["'success'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["response_dict.get('status')"]}, {"cell_type": "code", "execution_count": 25, "id": "50fecfc0-a66c-479e-9465-d73604d342ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total rows written: 2000\n", "Conversion complete. CSV file generated.\n"]}], "source": ["import csv\n", "import json\n", "\n", "def json_to_csv(json_data):\n", "    try:\n", "        # 打开 CSV 文件以写入\n", "        with open('report_data.csv', 'w', newline='', encoding='utf-8') as csvfile:\n", "            csv_writer = csv.writer(csvfile)\n", "\n", "            # 获取报告名称，如果不存在则默认为 'Report Name'\n", "            report_name = json_data.get('attributes', {}).get('reportName', 'Report Name')\n", "            csv_writer.writerow([report_name])\n", "\n", "            # 提取列标题\n", "            columns = []\n", "            first_row = json_data['factMap']['T!T']['rows'][0]['dataCells']\n", "            for cell in first_row:\n", "                columns.append(cell['label'])\n", "            csv_writer.writerow(columns)\n", "\n", "            # 提取行数据并写入CSV文件\n", "            row_count = 0\n", "            for row in json_data['factMap']['T!T']['rows']:\n", "                row_data = []\n", "                for cell in row['dataCells']:\n", "                    row_data.append(cell['label'])\n", "                csv_writer.writerow(row_data)\n", "                row_count += 1\n", "\n", "            print(f\"Total rows written: {row_count}\")\n", "    except Exception as e:\n", "        print(f\"An error occurred: {e}\")\n", "\n", "# 从文件中读取JSON数据\n", "try:\n", "    with open('/Users/<USER>/Documents/response.json', 'r', encoding='utf-8') as file:\n", "        response_json = json.load(file)\n", "except FileNotFoundError:\n", "    print(\"The file was not found.\")\n", "except json.JSONDecodeError:\n", "    print(\"Error decoding JSON.\")\n", "\n", "# 将JSON转换为CSV\n", "json_to_csv(response_json)\n", "\n", "print(\"Conversion complete. CSV file generated.\")\n"]}, {"cell_type": "code", "execution_count": 26, "id": "7f25825c-7714-4fdb-b716-43a4b158b4a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total rows in JSON: 2000\n", "Total rows written: 2000\n", "Conversion complete. CSV file generated.\n"]}], "source": ["import csv\n", "import json\n", "\n", "def json_to_csv(json_data):\n", "    try:\n", "        # 打开 CSV 文件以写入\n", "        with open('report_data.csv', 'w', newline='', encoding='utf-8') as csvfile:\n", "            csv_writer = csv.writer(csvfile)\n", "\n", "            # 获取报告名称，如果不存在则默认为 'Report Name'\n", "            report_name = json_data.get('attributes', {}).get('reportName', 'Report Name')\n", "            csv_writer.writerow([report_name])\n", "\n", "            # 提取列标题\n", "            columns = []\n", "            first_row = json_data['factMap']['T!T']['rows'][0]['dataCells']\n", "            for cell in first_row:\n", "                columns.append(cell['label'])\n", "            csv_writer.writerow(columns)\n", "\n", "            # 提取行数据并写入CSV文件\n", "            row_count = 0\n", "            for row in json_data['factMap']['T!T']['rows']:\n", "                row_data = []\n", "                for cell in row['dataCells']:\n", "                    row_data.append(cell['label'])\n", "                csv_writer.writerow(row_data)\n", "                row_count += 1\n", "\n", "            print(f\"Total rows written: {row_count}\")\n", "    except Exception as e:\n", "        print(f\"An error occurred: {e}\")\n", "\n", "# 从文件中读取JSON数据\n", "try:\n", "    with open('/Users/<USER>/Documents/response.json', 'r', encoding='utf-8') as file:\n", "        response_json = json.load(file)\n", "        print(f\"Total rows in JSON: {len(response_json['factMap']['T!T']['rows'])}\")\n", "except FileNotFoundError:\n", "    print(\"The file was not found.\")\n", "except json.JSONDecodeError:\n", "    print(\"Error decoding JSON.\")\n", "\n", "# 将JSON转换为CSV\n", "json_to_csv(response_json)\n", "\n", "print(\"Conversion complete. CSV file generated.\")\n"]}, {"cell_type": "code", "execution_count": 27, "id": "3283b6eb-775f-45ca-85e3-368901c63146", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total rows in JSON: 2000\n"]}], "source": ["print(f\"Total rows in JSON: {len(response_json['factMap']['T!T']['rows'])}\")"]}, {"cell_type": "code", "execution_count": 51, "id": "25d0c064-6b64-4a52-9da4-649cc3de7227", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total rows in JSON: 2000\n", "Total rows written: 2000\n", "Conversion complete. CSV file generated.\n"]}], "source": ["import csv\n", "import json\n", "\n", "def json_to_csv(json_data):\n", "    try:\n", "        # 打开 CSV 文件以写入\n", "        with open('report_data.csv', 'w', newline='', encoding='utf-8') as csvfile:\n", "            csv_writer = csv.writer(csvfile)\n", "\n", "            # 获取报告名称，如果不存在则默认为 'Report Name'\n", "            report_name = json_data.get('attributes', {}).get('reportName', 'Report Name')\n", "            csv_writer.writerow([report_name])\n", "\n", "            # 提取列标题\n", "            columns = []\n", "            first_row = json_data['factMap']['T!T']['rows'][0]['dataCells']\n", "            for cell in first_row:\n", "                columns.append(cell['value'])\n", "            csv_writer.writerow(columns)\n", "\n", "            # 提取行数据并写入CSV文件\n", "            row_count = 0\n", "            for row in json_data['factMap']['T!T']['rows']:\n", "                row_data = []\n", "                for cell in row['dataCells']:\n", "                    row_data.append(cell['value'])\n", "                csv_writer.writerow(row_data)\n", "                row_count += 1\n", "\n", "            print(f\"Total rows written: {row_count}\")\n", "    except Exception as e:\n", "        print(f\"An error occurred: {e}\")\n", "\n", "# 从文件中读取JSON数据\n", "try:\n", "    with open('/Users/<USER>/Documents/response1.json', 'r', encoding='utf-8') as file:\n", "        response_json = json.load(file)\n", "        total_rows = len(response_json['factMap']['T!T']['rows'])\n", "        print(f\"Total rows in JSON: {total_rows}\")\n", "except FileNotFoundError:\n", "    print(\"The file was not found.\")\n", "    response_json = None\n", "except json.JSONDecodeError:\n", "    print(\"Error decoding JSON.\")\n", "    response_json = None\n", "\n", "# 将JSON转换为CSV\n", "if response_json:\n", "    json_to_csv(response_json)\n", "    print(\"Conversion complete. CSV file generated.\")\n", "else:\n", "    print(\"No JSON data to process.\")\n"]}, {"cell_type": "code", "execution_count": 56, "id": "a5597446-e30e-4893-ac08-14070bbd64d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['attributes', 'allData', 'factMap', 'groupingsAcross', 'groupingsDown', 'hasDetailRows', 'picklistColors', 'reportExtendedMetadata', 'reportMetadata'])"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["response_json.keys()"]}, {"cell_type": "code", "execution_count": 54, "id": "ad86102b-317a-44ba-bf01-a9da63e52621", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["2000"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["len(response_json['factMap']['T!T']['rows'])"]}, {"cell_type": "code", "execution_count": 65, "id": "e5722736-1bff-48d6-a60c-02af045e525f", "metadata": {"scrolled": true}, "outputs": [], "source": ["# response_json['factMap']['T!T']['rows']"]}, {"cell_type": "code", "execution_count": 38, "id": "3ebc8bf5-109d-4852-bdeb-6a505e60bac5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["IOPub data rate exceeded.\n", "The Jupyter server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--ServerApp.iopub_data_rate_limit`.\n", "\n", "Current values:\n", "ServerApp.iopub_data_rate_limit=1000000.0 (bytes/sec)\n", "ServerApp.rate_limit_window=3.0 (secs)\n", "\n"]}], "source": ["import requests\n", "\n", "url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/analytics/reports/00OIS000001PrpR\"\n", "\n", "payload = {}\n", "headers = {\n", "  'Authorization': 'Bearer 00D20000000JPf6!AQ4AQM0ayAwkXjfBeDzmXrxjAYkVUjFwBQ_ejP.5ACFzGzWmJIqnhUQQb3gA0YNk6m4IxETM_Vzos3rReUbOXX1xViwJgoRG',\n", "  'Cookie': 'BrowserId=pVbaWozREe624-FNmz4TnA; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1'\n", "}\n", "\n", "response = requests.request(\"GET\", url, headers=headers, data=payload)\n", "\n", "print(response.text)\n"]}, {"cell_type": "code", "execution_count": 40, "id": "e6baf523-0371-49e1-a2b4-d0fea6fef12b", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 将响应内容解析为 JSON\n", "json_data = response.json()"]}, {"cell_type": "code", "execution_count": 41, "id": "9d42ab71-e098-4807-81e1-567a6920ab82", "metadata": {}, "outputs": [{"data": {"text/plain": ["2000"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["len(json_data['factMap']['T!T']['rows'])"]}, {"cell_type": "code", "execution_count": 42, "id": "6ab254cd-87c1-4125-9ff9-ce22356d3e88", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total records fetched: 2000\n"]}], "source": ["import requests\n", "\n", "def fetch_all_report_data(base_url, report_id, access_token):\n", "    url = f\"{base_url}/services/data/v59.0/analytics/reports/{report_id}\"\n", "    headers = {\n", "        'Authorization': f'<PERSON><PERSON> {access_token}',\n", "        'Cookie': 'BrowserId=pVbaWozREe624-FNmz4TnA; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1'\n", "    }\n", "\n", "    all_data = []\n", "    while url:\n", "        response = requests.get(url, headers=headers)\n", "        response_data = response.json()\n", "\n", "        # Assuming data is in 'factMap' -> 'T!T' -> 'rows'\n", "        rows = response_data.get('factMap', {}).get('T!T', {}).get('rows', [])\n", "        all_data.extend(rows)\n", "\n", "        # Check for nextRecordsUrl\n", "        next_records_url = response_data.get('nextRecordsUrl')\n", "        if next_records_url:\n", "            url = base_url + next_records_url\n", "        else:\n", "            url = None\n", "\n", "    return all_data\n", "\n", "# Replace these values with your actual endpoint, report ID, and access token\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "report_id = \"00OIS000001PrpR\"\n", "access_token = \"00D20000000JPf6!AQ4AQM0ayAwkXjfBeDzmXrxjAYkVUjFwBQ_ejP.5ACFzGzWmJIqnhUQQb3gA0YNk6m4IxETM_Vzos3rReUbOXX1xViwJgoRG\"\n", "\n", "all_report_data = fetch_all_report_data(base_url, report_id, access_token)\n", "print(f\"Total records fetched: {len(all_report_data)}\")\n"]}, {"cell_type": "code", "execution_count": 59, "id": "d6600684-f68e-43a2-b0c5-e6a99f3c3480", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FY21Q2 Identifier</th>\n", "      <th>FY24Q3 Identifier</th>\n", "      <th>FY22Q1 Large Account</th>\n", "      <th>FY21Q3 Large Account</th>\n", "      <th>FY21Q4 Large Account</th>\n", "      <th>FY24Q2 Top Account</th>\n", "      <th>FY22Q2 Top Account</th>\n", "      <th>FY22Q3 Top Account</th>\n", "      <th>FY22Q4 Top Account</th>\n", "      <th>FY23Q1 Top Account</th>\n", "      <th>...</th>\n", "      <th>Group Sub Segment</th>\n", "      <th>Group Vertical Industry</th>\n", "      <th>Province/Region</th>\n", "      <th>City</th>\n", "      <th>Group Province</th>\n", "      <th>Group City</th>\n", "      <th>Account Owner</th>\n", "      <th>Sub Segment</th>\n", "      <th>Vertical Industry</th>\n", "      <th>Top Account Deep Dive</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>Others</td>\n", "      <td>Others</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td>General Owner</td>\n", "      <td>Others</td>\n", "      <td>Utilities</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>Sichuan</td>\n", "      <td>Chengdu</td>\n", "      <td>Sichuan</td>\n", "      <td>Chengdu</td>\n", "      <td>General Owner</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>Others</td>\n", "      <td>Others</td>\n", "      <td>Hainan</td>\n", "      <td>Hai<PERSON><PERSON></td>\n", "      <td>Hainan</td>\n", "      <td>Hai<PERSON><PERSON></td>\n", "      <td>General Owner</td>\n", "      <td>Others</td>\n", "      <td>Others</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td><PERSON><PERSON> Zhu</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td>Beijing</td>\n", "      <td>General Owner</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87822</th>\n", "      <td>FY24Q3 Top List</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87823</th>\n", "      <td>Copyright (c) 2000-2024 salesforce.com, inc. A...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87824</th>\n", "      <td>Confidential Information - Do Not Distribute</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87825</th>\n", "      <td>Generated By:  <PERSON><PERSON>  2024-5-16 PM3:04</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87826</th>\n", "      <td>Apple China</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>87827 rows × 41 columns</p>\n", "</div>"], "text/plain": ["                                       FY21Q2 Identifier FY24Q3 Identifier  \\\n", "0                                                    NaN               NaN   \n", "1                                                    NaN               NaN   \n", "2                                                    NaN               NaN   \n", "3                                                    NaN               NaN   \n", "4                                                    NaN               NaN   \n", "...                                                  ...               ...   \n", "87822                                    FY24Q3 Top List               NaN   \n", "87823  Copyright (c) 2000-2024 salesforce.com, inc. A...               NaN   \n", "87824       Confidential Information - Do Not Distribute               NaN   \n", "87825       Generated By:  <PERSON><PERSON>  2024-5-16 PM3:04               NaN   \n", "87826                                        Apple China               NaN   \n", "\n", "       FY22Q1 Large Account  FY21Q3 Large Account  FY21Q4 Large Account  \\\n", "0                       0.0                   0.0                   0.0   \n", "1                       0.0                   0.0                   0.0   \n", "2                       0.0                   0.0                   0.0   \n", "3                       0.0                   0.0                   0.0   \n", "4                       0.0                   0.0                   0.0   \n", "...                     ...                   ...                   ...   \n", "87822                   NaN                   NaN                   NaN   \n", "87823                   NaN                   NaN                   NaN   \n", "87824                   NaN                   NaN                   NaN   \n", "87825                   NaN                   NaN                   NaN   \n", "87826                   NaN                   NaN                   NaN   \n", "\n", "       FY24Q2 Top Account  FY22Q2 Top Account  FY22Q3 Top Account  \\\n", "0                     0.0                 0.0                 0.0   \n", "1                     0.0                 0.0                 0.0   \n", "2                     0.0                 0.0                 0.0   \n", "3                     0.0                 0.0                 0.0   \n", "4                     0.0                 0.0                 0.0   \n", "...                   ...                 ...                 ...   \n", "87822                 NaN                 NaN                 NaN   \n", "87823                 NaN                 NaN                 NaN   \n", "87824                 NaN                 NaN                 NaN   \n", "87825                 NaN                 NaN                 NaN   \n", "87826                 NaN                 NaN                 NaN   \n", "\n", "       FY22Q4 Top Account  FY23Q1 Top Account  ...  Group Sub Segment  \\\n", "0                     0.0                 0.0  ...             Others   \n", "1                     0.0                 0.0  ...         Technology   \n", "2                     0.0                 0.0  ...             Others   \n", "3                     0.0                 0.0  ...         Technology   \n", "4                     0.0                 0.0  ...         Technology   \n", "...                   ...                 ...  ...                ...   \n", "87822                 NaN                 NaN  ...                NaN   \n", "87823                 NaN                 NaN  ...                NaN   \n", "87824                 NaN                 NaN  ...                NaN   \n", "87825                 NaN                 NaN  ...                NaN   \n", "87826                 NaN                 NaN  ...                NaN   \n", "\n", "       Group Vertical Industry  Province/Region     City  Group Province  \\\n", "0                       Others          Beijing  Beijing         Beijing   \n", "1                   Technology          Sichuan  Chengdu         Sichuan   \n", "2                       Others           <PERSON><PERSON>   Hai<PERSON>   \n", "3                   Technology          Beijing  Beijing         Beijing   \n", "4                   Technology          Beijing  Beijing         Beijing   \n", "...                        ...              ...      ...             ...   \n", "87822                      NaN              NaN      NaN             NaN   \n", "87823                      NaN              NaN      NaN             NaN   \n", "87824                      NaN              NaN      NaN             NaN   \n", "87825                      NaN              NaN      NaN             NaN   \n", "87826                      NaN              NaN      NaN             NaN   \n", "\n", "      Group City  Account Owner Sub Segment Vertical Industry  \\\n", "0        Beijing  General Owner      Others         Utilities   \n", "1        Chengdu  General Owner  Technology        Technology   \n", "2         Haikou  General Owner      Others            Others   \n", "3        Beijing    Jinfeng Zhu  Technology        Technology   \n", "4        Beijing  General Owner  Technology        Technology   \n", "...          ...            ...         ...               ...   \n", "87822        NaN            NaN         NaN               NaN   \n", "87823        NaN            NaN         NaN               NaN   \n", "87824        NaN            NaN         NaN               NaN   \n", "87825        NaN            NaN         NaN               NaN   \n", "87826        NaN            NaN         NaN               NaN   \n", "\n", "       Top Account Deep Dive  \n", "0                        0.0  \n", "1                        0.0  \n", "2                        0.0  \n", "3                        0.0  \n", "4                        0.0  \n", "...                      ...  \n", "87822                    NaN  \n", "87823                    NaN  \n", "87824                    NaN  \n", "87825                    NaN  \n", "87826                    NaN  \n", "\n", "[87827 rows x 41 columns]"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["df_All_Top_List = pd.read_csv('/Users/<USER>/Documents/ML/started/ying_support/MacTopAccount/All_Top_List.csv')\n", "df_All_Top_List"]}, {"cell_type": "code", "execution_count": 60, "id": "db2e0ca8-4943-4419-a3b2-b9c5a9907f6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY21Q2 Identifier', 'FY24Q3 Identifier', 'FY22Q1 Large Account', 'FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY24Q2 Top Account', 'FY22Q2 Top Account', 'FY22Q3 Top Account', 'FY22Q4 Top Account', 'FY23Q1 Top Account', 'FY23Q2 Top Account', 'FY23Q3 Top Account', 'FY23Q4 Top Account', 'FY24Q1 Top Account', 'FY24Q3 Top Account', 'FY24Q2 AE', 'FY24Q2 AEM', 'FY24Q3 AE', 'FY24Q3 AEM', 'Mac as Choice', 'Mac as Choice start time', 'SEI MaC', 'Account Name', 'SEI Account ID', 'SEI Cluster ID', 'Account Cluster', 'Is Group', 'Account ID', 'Account Group', 'SEI Group ID', 'Account Group ID', 'Group Sub Segment', 'Group Vertical Industry', 'Province/Region', 'City', 'Group Province', 'Group City', 'Account Owner', 'Sub Segment', 'Vertical Industry', 'Top Account Deep Dive']\n"]}], "source": ["print(list(df_All_Top_List))"]}, {"cell_type": "code", "execution_count": 62, "id": "9af44fcb-b791-4c20-8525-2f34326c864d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fields used in the report: ['Account.FY21Q2_Identifier__c', 'Account.FY24Q3_Identifier__c', 'Account.FY22Q1__c', 'Account.FY21Q3_Large_Account__c', 'Account.FY21Q4_Large_Account__c', 'Account.FY24Q2_Top_Account__c', 'Account.FY22Q2_Top_Account__c', 'Account.FY22Q3_Top_Account__c', 'Account.FY22Q4_Top_Account__c', 'Account.FY23Q1_Top_Account__c', 'Account.FY23Q2_Top_Account__c', 'Account.FY23Q3_Top_Account__c', 'Account.FY23Q4_Top_Account__c', 'Account.FY24Q1_Top_Account__c', 'Account.FY24Q3_Top_Account__c', 'Account.FY24Q2_AE__c', 'Account.FY24Q2_AEM__c', 'Account.FY24Q3_AE__c', 'Account.FY24Q3_AEM__c', 'Account.Mac_as_Choice__c', 'Account.Mac_as_Choice_start_time__c', 'Account.SEI_maC__c', 'ACCOUNT.NAME', 'Account.SEI_Account_ID__c', 'Account.SEI_Cluster_ID__c', 'Account.Account_Cluster__c', 'Account.Is_Group__c', 'ACCOUNT_ID', 'Account.Account_Group__c', 'Account.SEI_Group_ID__c', 'Account.Account_Group_ID__c', 'Account.Group_Sub_Segment__c', 'Account.Group_Vertical_Industry__c', 'Account.Province__c', 'Account.City__c', 'Account.Group_Province__c', 'Account.Group_City__c', 'USERS.NAME', 'Account.Sub_Segment__c', 'Account.Vertical_Industry__c', 'Account.Top_Account_Deep_Dive__c']\n"]}], "source": ["import requests\n", "\n", "def get_report_metadata(base_url, report_id, access_token):\n", "    url = f\"{base_url}/services/data/v59.0/analytics/reports/{report_id}/describe\"\n", "    headers = {\n", "        'Authorization': f'<PERSON><PERSON> {access_token}'\n", "    }\n", "\n", "    response = requests.get(url, headers=headers)\n", "    if response.status_code == 200:\n", "        report_metadata = response.json()\n", "        return report_metadata\n", "    else:\n", "        print(f\"Failed to get report metadata. Status code: {response.status_code}\")\n", "        return None\n", "\n", "# Replace these values with your actual Salesforce instance URL, report ID, and access token\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "report_id = \"00OIS000001PrpR\"\n", "access_token = \"00D20000000JPf6!AQ4AQM0ayAwkXjfBeDzmXrxjAYkVUjFwBQ_ejP.5ACFzGzWmJIqnhUQQb3gA0YNk6m4IxETM_Vzos3rReUbOXX1xViwJgoRG\"\n", "\n", "\n", "report_metadata = get_report_metadata(base_url, report_id, access_token)\n", "if report_metadata:\n", "    # print(f\"Report metadata: {report_metadata}\")\n", "\n", "    # Extract fields used in the report\n", "    fields = []\n", "    for detail_column in report_metadata.get('reportMetadata', {}).get('detailColumns', []):\n", "        fields.append(detail_column)\n", "\n", "    print(f\"Fields used in the report: {fields}\")\n", "else:\n", "    print(\"Failed to get report metadata.\")\n"]}, {"cell_type": "code", "execution_count": 67, "id": "c5235125-bd97-4564-9c6b-618b546ca648", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed to get report metadata. Status code: 401\n", "Failed to get report metadata.\n"]}], "source": ["import requests\n", "\n", "def get_report_metadata(base_url, report_id, access_token):\n", "    url = f\"{base_url}/services/data/v59.0/analytics/reports/{report_id}/describe\"\n", "    headers = {\n", "        'Authorization': f'<PERSON><PERSON> {access_token}'\n", "    }\n", "\n", "    response = requests.get(url, headers=headers)\n", "    if response.status_code == 200:\n", "        report_metadata = response.json()\n", "        return report_metadata\n", "    else:\n", "        print(f\"Failed to get report metadata. Status code: {response.status_code}\")\n", "        return None\n", "\n", "def extract_objects_and_fields(report_metadata):\n", "    objects = set()\n", "    fields = []\n", "\n", "    # Extract detail columns\n", "    detail_columns = report_metadata.get('reportMetadata', {}).get('detailColumns', [])\n", "    for column in detail_columns:\n", "        # Split the field name to get the object and field\n", "        parts = column.split('.')\n", "        if len(parts) == 2:\n", "            objects.add(parts[0])\n", "            fields.append(column)\n", "\n", "    return objects, fields\n", "\n", "# Replace these values with your actual Salesforce instance URL, report ID, and access token\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "report_id = \"00OIS000001PrpR\"\n", "access_token = \"00D20000000JPf6!AQ4AQM0ayAwkXjfBeDzmXrxjAYkVUjFwBQ_ejP.5ACFzGzWmJIqnhUQQb3gA0YNk6m4IxETM_Vzos3rReUbOXX1xViwJgoRG\"\n", "\n", "report_metadata = get_report_metadata(base_url, report_id, access_token)\n", "if report_metadata:\n", "    objects, fields = extract_objects_and_fields(report_metadata)\n", "    print(f\"Objects used in the report: {objects}\")\n", "    print(f\"Fields used in the report: {fields}\")\n", "else:\n", "    print(\"Failed to get report metadata.\")\n"]}, {"cell_type": "code", "execution_count": 68, "id": "b244ddd4-c2c9-4119-b5d1-d9013c5410ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"error\":\"invalid_client\",\"error_description\":\"invalid client credentials\"}\n"]}], "source": ["import requests\n", "\n", "url = \"https://login.salesforce.com/services/oauth2/token\"\n", "\n", "payload = 'grant_type=refresh_token&client_id=3MVG98_Psg5cppyYCmk1gZNC25o00SXpgpodlS29IZ6pXiHkt3xuPa5qIjBTtEgdsiMuIWVN_8F0jnwEtbDh4&client_secret=&refresh_token=%7B%7B_refreshToken%7D%7D'\n", "headers = {\n", "  'Content-Type': 'application/x-www-form-urlencoded',\n", "  'Authorization': 'Bearer 00D20000000JPf6!AQ4AQJ75FWJeQtOJIiViPbVI.6nWJz01qe1ZGjnNEnNBRbxQlIjmB_c1VjatdQpW7nKHjoT73hYKV97D3BfjVL2M6FGRRmZz',\n", "  'Cookie': 'BrowserId=pVbaWozREe624-FNmz4TnA; CookieConsentPolicy=0:0; LSKey-c$CookieConsentPolicy=0:0'\n", "}\n", "\n", "response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "\n", "print(response.text)\n"]}, {"cell_type": "code", "execution_count": 71, "id": "bf33084e-a8ea-4b55-a191-922da869df40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'error': 'invalid_grant', 'error_description': 'invalid authorization code'}\n"]}], "source": ["import requests\n", "\n", "url = \"https://login.salesforce.com/services/oauth2/token\"\n", "\n", "payload = {\n", "    'grant_type': 'authorization_code',\n", "    'client_id': '3MVG9WtWSKUDG.x6N_g.w4D.yziaRFuKKWII6jtcREuA9gWq6ITT0QpkPSxnV1PtVcEVXSLvmkC6lUcsfkvbA',\n", "    'client_secret': '****************************************************************',\n", "    'code': '6Cel800D20000000JPf6888IS000000fxSLfzZYp5LEjlTPcDiK1lAF7ovjQao6osvZMKBUxY3OyTuOpdGx9F4UK9gCbqq88vCmE3uLZqDC',\n", "    'redirect_uri': 'https://d20000000jpf6eag.my.salesforce.com/home/<USER>'\n", "}\n", "headers = {\n", "    'Content-Type': 'application/x-www-form-urlencoded'\n", "}\n", "\n", "response = requests.post(url, headers=headers, data=payload)\n", "\n", "print(response.json())\n"]}, {"cell_type": "code", "execution_count": 82, "id": "1ea77cd6-15e1-4161-a504-4547f3057641", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Code Verifier: YWmx7ZxrfjSIwZZjkfuYDgpzXGErkq7UPYV-MZO8xr5s8xWAPnmc8JGsQ33J0xHF2VItfJUOEkWKgmqfauz8tSo-5H8luv4XziBU2L3DhSkKHwhmeClcm_AAWgMVE0kPXDxke-Ry8X54lEnpdTPp30KVPZE8j7IR5sHn81wiXgU\n", "Code Challenge: yo3KfrGL1Ig3o4NNox9WwMiKn1jmcBHbDnePOBqsfxM\n"]}], "source": ["import base64\n", "import hashlib\n", "import os\n", "\n", "def generate_code_verifier(length=128):\n", "    return base64.urlsafe_b64encode(os.urandom(length)).rstrip(b'=').decode('utf-8')\n", "\n", "def generate_code_challenge(code_verifier):\n", "    code_challenge = hashlib.sha256(code_verifier.encode('utf-8')).digest()\n", "    return base64.urlsafe_b64encode(code_challenge).rstrip(b'=').decode('utf-8')\n", "\n", "code_verifier = generate_code_verifier()\n", "code_challenge = generate_code_challenge(code_verifier)\n", "\n", "print(f\"Code Verifier: {code_verifier}\")\n", "print(f\"Code Challenge: {code_challenge}\")\n"]}, {"cell_type": "code", "execution_count": 91, "id": "9d4e9b08-96d7-43d4-b6e6-f81b69f1d7cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'access_token': '00D20000000JPf6!AQ4AQG2m_M1lHlEQfEdEdrz0MTAGKs.VCjI8lEj8eN2WNuKwN_osMsxTi2svwibPcBy5VsOUHxoetFiIWRFAoWmxRyXTtiis', 'refresh_token': '***************************************************************************************', 'signature': 'EZ9wtgbhz9J/lqQF3Lgus6+cBzCNGo+UaINxMnOmsxs=', 'scope': 'cdp_ingest_api custom_permissions cdp_segment_api content cdp_api cdp_identityresolution_api chatbot_api interaction_api wave_api cdp_calculated_insight_api einstein_gpt_api web api id eclair_api pardot_api lightning visualforce sfap_api cdp_query_api openid cdp_profile_api refresh_token pwdless_login_api user_registration_api chatter_api full forgot_password', 'id_token': '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'instance_url': 'https://d20000000jpf6eag.my.salesforce.com', 'id': 'https://login.salesforce.com/id/00D20000000JPf6EAG/005IS000000YL4HYAW', 'token_type': 'Bearer', 'issued_at': '1716287779772', 'api_instance_url': 'https://api.salesforce.com'}\n"]}], "source": ["import requests\n", "\n", "url = \"https://login.salesforce.com/services/oauth2/token\"\n", "\n", "payload = {\n", "    'grant_type': 'authorization_code',\n", "    'client_id': '3MVG9WtWSKUDG.x6N_g.w4D.yzjTMKXgLFyB3CgOrZYqvP5MW.yI80efvT9Hg0aCTfzh1IkP_Yc47OXwS4Phg',\n", "    'client_secret': '****************************************************************',\n", "    'code': 'aPrxDtC6R2ZDACGz2zCCiXjDNuXAhNm.SZRkpBzLwOx.sbcKoarlMcuYjO0f498Zw9madD9sDA==',\n", "    'redirect_uri': 'https://d20000000jpf6eag.my.salesforce.com/home/<USER>',\n", "    'code_verifier': code_verifier  # 如果使用了PKCE\n", "}\n", "headers = {\n", "    'Content-Type': 'application/x-www-form-urlencoded'\n", "}\n", "\n", "response = requests.post(url, headers=headers, data=payload)\n", "\n", "print(response.json())\n"]}, {"cell_type": "code", "execution_count": 98, "id": "43b3f991-5c9e-465a-bcec-4ed1a7fd39d0", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "# 已经获取到的访问令牌\n", "access_token = \"00D20000000JPf6!AQ4AQL1Y_26BKg0zrkr1HQJL5wvHU2pQrWb6lglzl5NONiyVblbGYwi0SKD3ynLkpjt0ttztCTaPuB550n7nrfuL48geu8KP\"\n", "\n", "# Salesforce 实例 URL\n", "instance_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "\n", "# 请求 URL\n", "url = f\"{instance_url}/services/data/v59.0/sobjects/Account/describe/\"\n", "\n", "# 设置请求头，包括 Authorization 头\n", "headers = {\n", "    \"Authorization\": f\"Bearer {access_token}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# 发送 GET 请求\n", "response = requests.get(url, headers=headers)\n", "\n", "# 打印响应\n", "print(response.json())\n"]}, {"cell_type": "code", "execution_count": 96, "id": "259ab523-9516-40bf-a30a-4b6aed664c8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'access_token': '00D20000000JPf6!AQ4AQL1Y_26BKg0zrkr1HQJL5wvHU2pQrWb6lglzl5NONiyVblbGYwi0SKD3ynLkpjt0ttztCTaPuB550n7nrfuL48geu8KP', 'signature': 'NyQccZWNDAUDCklInOoa6V7JwqoEZWUsfw3aGP4IcSQ=', 'scope': 'cdp_ingest_api custom_permissions cdp_segment_api cdp_api content chatbot_api interaction_api cdp_identityresolution_api wave_api web einstein_gpt_api cdp_calculated_insight_api id api eclair_api pardot_api lightning visualforce cdp_query_api sfap_api openid cdp_profile_api refresh_token pwdless_login_api user_registration_api chatter_api forgot_password full', 'instance_url': 'https://d20000000jpf6eag.my.salesforce.com', 'id': 'https://login.salesforce.com/id/00D20000000JPf6EAG/005IS000000YL4HYAW', 'token_type': 'Bearer', 'issued_at': '1716341812311', 'api_instance_url': 'https://api.salesforce.com'}\n"]}], "source": ["import requests\n", "\n", "# Salesforce 实例的令牌 URL\n", "url = \"https://login.salesforce.com/services/oauth2/token\"\n", "\n", "# 使用你的刷新令牌、客户端 ID 和客户端密钥构建请求数据\n", "payload = {\n", "    'grant_type': 'refresh_token',\n", "    'client_id': '3MVG9WtWSKUDG.x6N_g.w4D.yzjTMKXgLFyB3CgOrZYqvP5MW.yI80efvT9Hg0aCTfzh1IkP_Yc47OXwS4Phg',\n", "    'client_secret': '****************************************************************',\n", "    'refresh_token': '***************************************************************************************'\n", "}\n", "headers = {\n", "    'Content-Type': 'application/x-www-form-urlencoded'\n", "}\n", "\n", "# 发送 POST 请求以获取新的访问令牌\n", "response = requests.post(url, headers=headers, data=payload)\n", "\n", "# 打印响应\n", "print(response.json())\n"]}, {"cell_type": "code", "execution_count": 107, "id": "752a8bd8-1e17-4dbe-894e-c556dbd237f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'access_token': '00D20000000JPf6!AQ4AQL1Y_26BKg0zrkr1HQJL5wvHU2pQrWb6lglzl5NONiyVblbGYwi0SKD3ynLkpjt0ttztCTaPuB550n7nrfuL48geu8KP', 'signature': 'RPd8FTQmrol750tBX8QLX+vgPDE4u9pxs6vu4fx7db0=', 'scope': 'cdp_ingest_api custom_permissions cdp_segment_api content cdp_api chatbot_api interaction_api cdp_identityresolution_api wave_api web einstein_gpt_api cdp_calculated_insight_api id api eclair_api pardot_api lightning visualforce cdp_query_api sfap_api openid cdp_profile_api refresh_token user_registration_api pwdless_login_api chatter_api forgot_password full', 'instance_url': 'https://d20000000jpf6eag.my.salesforce.com', 'id': 'https://login.salesforce.com/id/00D20000000JPf6EAG/005IS000000YL4HYAW', 'token_type': 'Bearer', 'issued_at': '1716342888979', 'api_instance_url': 'https://api.salesforce.com'}\n"]}], "source": ["import configparser\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read('/Users/<USER>/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "client_id = config['salesforce']['CLIENT_ID']\n", "client_secret = config['salesforce']['CLIENT_SECRET']\n", "refresh_token = config['salesforce']['REFRESH_TOKEN']\n", "\n", "# Salesforce 实例的令牌 URL\n", "url = \"https://login.salesforce.com/services/oauth2/token\"\n", "\n", "# 使用你的刷新令牌、客户端 ID 和客户端密钥构建请求数据\n", "payload = {\n", "    'grant_type': 'refresh_token',\n", "    'client_id': client_id,\n", "    'client_secret': client_secret,\n", "    'refresh_token': refresh_token\n", "}\n", "headers = {\n", "    'Content-Type': 'application/x-www-form-urlencoded'\n", "}\n", "\n", "# 发送 POST 请求以获取新的访问令牌\n", "response = requests.post(url, headers=headers, data=payload)\n", "\n", "# 打印响应\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": 108, "id": "6c07c2cd-2f86-487e-942a-ba67eb615724", "metadata": {}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQ4AQL1Y_26BKg0zrkr1HQJL5wvHU2pQrWb6lglzl5NONiyVblbGYwi0SKD3ynLkpjt0ttztCTaPuB550n7nrfuL48geu8KP'"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["response.json()['access_token']"]}, {"cell_type": "code", "execution_count": null, "id": "28266a80-8d72-4ebf-8a11-68012f6c3ef4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}