{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bffc08c3-8517-491e-b2ee-97d97a688962", "metadata": {}, "outputs": [], "source": ["import os\n", "import datetime\n", "import configparser\n", "import requests\n", "import json\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "id": "bd75d82a-d8e0-453d-a822-aff1c3addcf0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "b00c9ef0-1f6d-4e7a-bf35-fe84bd6a9211", "metadata": {}, "outputs": [], "source": ["from acquire_mapping import Mapping"]}, {"cell_type": "code", "execution_count": 4, "id": "0257f3a9-3d08-44ca-bb34-94ad1e5ab353", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 5, "id": "ff3bfce2-8b17-4965-a068-f6be08355b51", "metadata": {}, "outputs": [], "source": ["fiscaldate_mapping = Mapping.fiscaldate\n", "fiscaldate_mapping2 = fiscaldate_mapping.copy()[['Date','week_in_fiscal_quarter','fiscal_qtr_year_name']]\n", "fiscaldate_mapping2['week_in_fiscal_quarter'] = fiscaldate_mapping2['week_in_fiscal_quarter'].str.slice(1)\n", "fiscaldate_mapping2.rename(columns={'Date':'fiscal_dt'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "56e1853b-0edf-474f-9860-3cb40474ff23", "metadata": {}, "outputs": [], "source": ["now = datetime.datetime.now().strftime('%Y-%m-%d')\n", "now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"]}, {"cell_type": "code", "execution_count": 7, "id": "2a5750fc-96d8-4091-afd5-d302a21c4e78", "metadata": {}, "outputs": [], "source": ["fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_qtr_year_name'].unique().tolist()[0]\n", "week_in_fiscal_quarter = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['week_in_fiscal_quarter'].unique().tolist()[0][1:]\n", "fiscal_week_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_week_year_name'].unique().tolist()[0]\n", "# 如果是单个数字，则在前面添加 '0'\n", "if week_in_fiscal_quarter.isdigit() and len(week_in_fiscal_quarter) == 1:\n", "    week_in_fiscal_quarter2 = '0' + week_in_fiscal_quarter\n", "else:\n", "    week_in_fiscal_quarter2 = week_in_fiscal_quarter\n", "\n", "yqw_addzero = fiscal_qtr_year_name + 'W' + week_in_fiscal_quarter2\n", "next_fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date'] >= now]['fiscal_qtr_year_name'].unique()[1]"]}, {"cell_type": "code", "execution_count": 8, "id": "330d730e-de9d-418a-8730-8017b8c19ca1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY25Q1', 'FY24Q4', 'FY24Q3', 'FY24Q2', 'FY24Q1', 'FY23Q4', 'FY23Q3', 'FY23Q2', 'FY23Q1', 'FY22Q4', 'FY22Q3', 'FY22Q2']\n"]}], "source": ["def generate_fiscal_quarters(start_fiscal, end_fiscal):\n", "    start_year = int(start_fiscal[2:4])\n", "    start_qtr = int(start_fiscal[5])\n", "    end_year = int(end_fiscal[2:4])\n", "    end_qtr = int(end_fiscal[5])\n", "\n", "    fiscal_quarters = []\n", "    \n", "    current_year = start_year\n", "    current_qtr = start_qtr\n", "\n", "    while (current_year > end_year) or (current_year == end_year and current_qtr >= end_qtr):\n", "        fiscal_quarters.append(f\"FY{current_year:02d}Q{current_qtr}\")\n", "        if current_qtr == 1:\n", "            current_qtr = 4\n", "            current_year -= 1\n", "        else:\n", "            current_qtr -= 1\n", "\n", "    return fiscal_quarters\n", "\n", "# Example usage\n", "# fiscal_qtr_year_name = \"FY24Q4\"\n", "end_fiscal = \"FY22Q2\"\n", "\n", "fiscal_quarters_list = generate_fiscal_quarters(fiscal_qtr_year_name, end_fiscal)\n", "print(fiscal_quarters_list)"]}, {"cell_type": "code", "execution_count": 9, "id": "16949f44-14c1-4fe6-8777-8dee59ae5fe6", "metadata": {}, "outputs": [], "source": ["# Generate fiscal_quarters_list_ta ae aem\n", "fiscal_quarters_list_ta = [f\"Account.{fq}_Top_Account__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_ae = [f\"Account.{fq}_AE__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_aem = [f\"Account.{fq}_AEM__c\" for fq in fiscal_quarters_list]"]}, {"cell_type": "code", "execution_count": 10, "id": "14a241dd-de3c-4f66-a9c7-1d22af5e0331", "metadata": {}, "outputs": [], "source": ["# Combine all the lists into one\n", "all_fiscal_quarters_columns = fiscal_quarters_list_ta + fiscal_quarters_list_ae + fiscal_quarters_list_aem"]}, {"cell_type": "code", "execution_count": 11, "id": "fbebb1de-4648-4531-bec3-752b2ddc5ff7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQ4AQFAA4TeOagpCwcMO050hYCNF4JvJke79tw.6GxD3d8wk3ZCW3SZ4JsYVijbZmTOVoa90wR01qwAqgMNreQDld2Rdj3Om'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 12, "id": "371c91af-eb1e-48ed-ba9d-06f07d1548d2", "metadata": {}, "outputs": [], "source": ["sql_statement = \"\"\"\n", "SELECT \n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "\n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__r.Name, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "\n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "\n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c,\n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__c, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Opportunity.Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.OPPORTUNITY_ID__c,\n", "    Opportunity.Name,\n", "    Opportunity.Probability,\n", "    Opportunity.JD_Appended__c,\n", "    Opportunity.Account.Mac_as_Choice__c,\n", "    Opportunity.Account.Top_Account_Deep_Dive__c,\n", "    Opportunity.Apple_Reseller__r.Name,\n", "    \n", "    Account.Enroll_Date__c,\n", "    Account.Acquisition_Group__c,\n", "    Account.NCR_Program__c,\n", "    Account.Reseller_for_acquisition__c,\n", "    Account.Status__c,\n", "    \n", "    (SELECT \n", "        Revenue__c, \n", "        FY__c, \n", "        ST_FYQuarter__c, \n", "        Product_Family__c,\n", "        Quarter__c, \n", "        Sell_Through_Week__c, \n", "        Oppty_Line_Item_ID__c, \n", "        Marketing_Part_Number_MPN__c,\n", "        Quantity,\n", "        Line_of_business2__c\n", "     FROM OpportunityLineItems)\n", "FROM Opportunity\n", "WHERE  Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT'\n", "and Id IN (\n", "        SELECT OpportunityId \n", "        FROM OpportunityLineItem \n", "        WHERE FY__c > 'FY18' \n", "    )\n", "    \n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "ffcfbc02-4cc9-4b1b-8091-b4a2a1119e7e", "metadata": {}, "outputs": [], "source": ["# Insert the fiscal quarters columns after 'Account.FY22Q1__c,'\n", "insert_point = sql_statement.find(\"Account.FY22Q1__c,\") + len(\"Account.FY22Q1__c,\")\n", "sql_statement = sql_statement[:insert_point] + \"\\n    \" + \",\\n    \".join(all_fiscal_quarters_columns) + \",\" + sql_statement[insert_point:]"]}, {"cell_type": "code", "execution_count": 14, "id": "9dadc1c4-2006-49f0-995e-18c65bffdc8b", "metadata": {}, "outputs": [], "source": ["\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")"]}, {"cell_type": "code", "execution_count": 15, "id": "58f58cec-f923-4c69-a5d4-f840a6ae25c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"SELECT Account.Account_Group__c, Account.Group_Province__c, Account.Group_City__c, Account.Province__c, Account.City__c, Opportunity.Project_Type__c, Opportunity.Sold_To_ID__c, Opportunity.Opportunity_Reseller_Apple_ID__c, Opportunity.T2_Reseller__r.Name, Opportunity.Apple_HQ_ID__c, Opportunity.Opportunity_Reseller_Track__c, Opportunity.ESC_Store__c, Account.Sub_Segment__c, Account.Vertical_Industry__c, Account.zhanbaoshijian__c, Account.Mac_as_Choice_start_time__c, Account.FY21Q3_Large_Account__c, Account.FY21Q4_Large_Account__c, Account.FY22Q1__c, Account.FY25Q1_Top_Account__c, Account.FY24Q4_Top_Account__c, Account.FY24Q3_Top_Account__c, Account.FY24Q2_Top_Account__c, Account.FY24Q1_Top_Account__c, Account.FY23Q4_Top_Account__c, Account.FY23Q3_Top_Account__c, Account.FY23Q2_Top_Account__c, Account.FY23Q1_Top_Account__c, Account.FY22Q4_Top_Account__c, Account.FY22Q3_Top_Account__c, Account.FY22Q2_Top_Account__c, Account.FY25Q1_AE__c, Account.FY24Q4_AE__c, Account.FY24Q3_AE__c, Account.FY24Q2_AE__c, Account.FY24Q1_AE__c, Account.FY23Q4_AE__c, Account.FY23Q3_AE__c, Account.FY23Q2_AE__c, Account.FY23Q1_AE__c, Account.FY22Q4_AE__c, Account.FY22Q3_AE__c, Account.FY22Q2_AE__c, Account.FY25Q1_AEM__c, Account.FY24Q4_AEM__c, Account.FY24Q3_AEM__c, Account.FY24Q2_AEM__c, Account.FY24Q1_AEM__c, Account.FY23Q4_AEM__c, Account.FY23Q3_AEM__c, Account.FY23Q2_AEM__c, Account.FY23Q1_AEM__c, Account.FY22Q4_AEM__c, Account.FY22Q3_AEM__c, Account.FY22Q2_AEM__c, Account.FY21Q2_AE__c, Account.FY21Q2_AEM__c, Account.FY21Q3_AE__c, Account.FY21Q3_AEM__c, Account.FY21Q4_AE__c, Account.FY21Q4_AEM__c, Account.FY22Q1_AE__c, Account.FY22Q1_AEM__c, Opportunity.Opportunity_Type__c, Account.Segment__c, Account.Large_Account__c, Account.Total_Mac_Demand__c, Account.PC_Install_Base__c, Account.FY22_Fcst__c, Account.FY23_Fcst__c, Opportunity.leasingornot__c, Account.Industry_Target_Account__c, Opportunity.Penetrated_Account__c, Account.Source_Detail__c, Account.Sales_Region__c, Account.Name, Opportunity.Account.Owner.Name, Account.Account_ID__c, Opportunity.OPPORTUNITY_ID__c, Opportunity.Name, Opportunity.Probability, Opportunity.JD_Appended__c, Opportunity.Account.Mac_as_Choice__c, Opportunity.Account.Top_Account_Deep_Dive__c, Opportunity.Apple_Reseller__r.Name, Account.Enroll_Date__c, Account.Acquisition_Group__c, Account.NCR_Program__c, Account.Reseller_for_acquisition__c, Account.Status__c, (SELECT Revenue__c, FY__c, ST_FYQuarter__c, Product_Family__c, Quarter__c, Sell_Through_Week__c, Oppty_Line_Item_ID__c, Marketing_Part_Number_MPN__c, Quantity, Line_of_business2__c FROM OpportunityLineItems) FROM Opportunity WHERE Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT' and Id IN ( SELECT OpportunityId FROM OpportunityLineItem WHERE FY__c > 'FY18' )\""]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_statement_single_line"]}, {"cell_type": "code", "execution_count": 16, "id": "cd52a5b1-40f5-4b20-8507-506dba08b0b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-495\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-736\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-1094\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-1459\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-1680\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-2145\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-2517\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-2855\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-3143\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-3435\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-3649\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-3971\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-4215\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-4532\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-4915\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-5250\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-5283\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-5285\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-5388\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-5720\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-6166\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-6321\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-6613\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-6980\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-7139\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-7392\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-7835\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-8177\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-8629\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-8846\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-9076\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-9349\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-9641\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-9948\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-10277\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-10443\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-10594\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-10769\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-11023\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-11566\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-11898\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-12286\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-12665\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-13080\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-13469\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-13591\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-13779\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-14194\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-14625\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-14959\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-15195\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-15381\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-15604\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-15827\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-16188\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-16528\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-16859\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-16974\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-17418\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-17666\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-18076\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-18509\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-18841\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-19233\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-19557\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-19847\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-20186\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-20382\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-20525\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-20725\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-20904\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-20914\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-21343\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-21703\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-21878\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-22010\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-22260\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-22489\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-22675\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-23005\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-23339\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-23806\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-24096\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-24251\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-24533\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-24816\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-25161\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-25460\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-25780\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-26321\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-26602\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-26825\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-27206\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-27378\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-27791\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-28053\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-28313\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-28790\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29182\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29587\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29652\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29660\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29663\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29665\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29667\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29672\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29675\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29678\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29680\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29683\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-29934\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-30254\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-30306\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-30510\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-30798\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-31202\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-31568\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-31904\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-32176\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-32382\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-32644\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-33005\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-33376\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-33658\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-34033\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-34461\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-34584\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-34864\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-35241\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-35593\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-35973\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-36230\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-36559\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-36879\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-36987\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-37234\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-37504\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-37839\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-38083\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-38288\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-38681\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-38909\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-39339\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-39485\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-39714\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-39910\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-40278\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-40549\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-40724\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-41096\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-41313\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-41649\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-41816\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-42132\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-42643\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-43223\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3THjM8PttkAWC-43822\n"]}], "source": ["\n", "# for check keyong\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"00D20000000JPf6!AQ4AQDJU3_dhyicWbI4oh7DhiRhiAGomTWxU5SPWEc.RW2p1LaiZUlKCMx68rVWu1gSvVcFfbH3gEPUBrFLup0rQw576pYml\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "\n", "\n", "\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data_fordebug.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "77b2d27f-a102-487e-baad-e9cd24c3406b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f384cda-79bd-4c8b-ab51-7efefce2b707", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "28cd0594-e510-4874-9091-dc7cdb4dbe90", "metadata": {}, "outputs": [], "source": ["\n", "# # 将 JSON 数据导出并美化\n", "# with open('ppl.json', 'w', encoding='utf-8') as json_file:\n", "#     json.dump(all_records, json_file, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": 18, "id": "5c640dcf-a3d9-41d5-bbc9-fa4cb4661e8c", "metadata": {}, "outputs": [{"data": {"text/plain": ["['attributes',\n", " 'Account',\n", " 'Project_Type__c',\n", " 'Sold_To_ID__c',\n", " 'Opportunity_Reseller_Apple_ID__c',\n", " 'T2_Reseller__r',\n", " 'Apple_HQ_ID__c',\n", " 'Opportunity_Reseller_Track__c',\n", " 'ESC_Store__c',\n", " 'Opportunity_Type__c',\n", " 'leasingornot__c',\n", " 'Penetrated_Account__c',\n", " 'Opportunity_ID__c',\n", " 'Name',\n", " 'Probability',\n", " 'JD_Appended__c',\n", " 'Apple_Reseller__r']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["meta = list(all_records[0].keys())\n", "# 移除 'OpportunityLineItems' 元素\n", "if 'OpportunityLineItems' in meta:\n", "    meta.remove('OpportunityLineItems')\n", "\n", "# 打印结果\n", "meta"]}, {"cell_type": "code", "execution_count": 19, "id": "c36d5345-d8a9-4302-ae28-b15d33dd1679", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Opportunity_Reseller_Track__c</th>\n", "      <th>ESC_Store__c</th>\n", "      <th>Opportunity_Type__c</th>\n", "      <th>leasingornot__c</th>\n", "      <th>Penetrated_Account__c</th>\n", "      <th>Opportunity_ID__c</th>\n", "      <th>Name</th>\n", "      <th>Probability</th>\n", "      <th>JD_Appended__c</th>\n", "      <th>Apple_Reseller__r</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F00000rM4Kc</td>\n", "      <td>采购80台MacBook Pro办公</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F00000rM4Kc</td>\n", "      <td>采购80台MacBook Pro办公</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F00000rM4Kc</td>\n", "      <td>采购80台MacBook Pro办公</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F00000rM4Kc</td>\n", "      <td>采购80台MacBook Pro办公</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F00000rM4Kc</td>\n", "      <td>采购80台MacBook Pro办公</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F00004Fk41zQAB</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003giIP</td>\n", "      <td>办公自用</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F00004A7QLBQA3</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003giI0</td>\n", "      <td>办公用机</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F00004A7QLBQA3</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000046Ct4</td>\n", "      <td>办公用机</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F000048n9GDQAY</td>\n", "      <td>Gifting</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000044cJh</td>\n", "      <td>零售</td>\n", "      <td>25.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F00004Fk41zQAB</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003gqRq</td>\n", "      <td>办公自用</td>\n", "      <td>25.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 29 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c Product_Family__c Quarter__c  \\\n", "0          3191.75  FY19          FY19Q2       MacBook Pro         Q2   \n", "1          1082.00  FY19          FY19Q3       MacBook Air         Q3   \n", "2         16296.00  FY19          FY19Q2       MacBook Pro         Q2   \n", "3          2059.52  FY19          FY19Q2       MacBook Pro         Q2   \n", "4          2712.73  FY19          FY19Q2       MacBook Pro         Q2   \n", "...            ...   ...             ...               ...        ...   \n", "264218     1291.00  FY24          FY24Q3       MacBook Air         Q3   \n", "264219     1291.00  FY24          FY24Q3       MacBook Air         Q3   \n", "264220     9676.00  FY24          FY24Q4       MacBook Pro         Q4   \n", "264221     3661.00  FY24          FY24Q4        Vision Pro         Q4   \n", "264222     2403.00  FY24          FY24Q4              iMac         Q4   \n", "\n", "       Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0                       W08       00k6F0000168pJf   \n", "1                       W03       00k6F0000169QKA   \n", "2                       W12       00k6F0000169aqO   \n", "3                       W13       00k6F0000169l19   \n", "4                       W05       00k6F000015xT5H   \n", "...                     ...                   ...   \n", "264218                  W10       00kIS00000757ZB   \n", "264219                  W09       00kIS0000074ual   \n", "264220                  W07       00kIS000007lKUp   \n", "264221                  W02       00kIS0000077qYX   \n", "264222                  W12       00kIS00000757Un   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                              Z0V1       1.0                  CPU  ...   \n", "1                         MREA2CH/A       1.0                  CPU  ...   \n", "2                              Z0UK      12.0                  CPU  ...   \n", "3                              Z0V7       1.0                  CPU  ...   \n", "4                              Z0V0       1.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "264218                         Z1BB       1.0                  CPU  ...   \n", "264219                         Z1BP       1.0                  CPU  ...   \n", "264220                         Z1AY       4.0                  CPU  ...   \n", "264221                    MW8X3CH/A       1.0               Vision  ...   \n", "264222                         Z19Q       1.0                  CPU  ...   \n", "\n", "       Opportunity_Reseller_Track__c        ESC_Store__c Opportunity_Type__c  \\\n", "0                                ENT  0016F0000237ljIQAQ            Solution   \n", "1                                ENT  0016F0000237ljIQAQ            Solution   \n", "2                                ENT  0016F0000237ljIQAQ            Solution   \n", "3                                ENT  0016F0000237ljIQAQ            Solution   \n", "4                                ENT  0016F0000237ljIQAQ            Solution   \n", "...                              ...                 ...                 ...   \n", "264218                           ENT  0016F00004Fk41zQAB            Solution   \n", "264219                           ENT  0016F00004A7QLBQA3            Solution   \n", "264220                           ENT  0016F00004A7QLBQA3            Solution   \n", "264221                           ENT  0016F000048n9GDQAY             Gifting   \n", "264222                           ENT  0016F00004Fk41zQAB            Solution   \n", "\n", "       leasingornot__c Penetrated_Account__c Opportunity_ID__c  \\\n", "0                 None                  None   0066F00000rM4Kc   \n", "1                 None                  None   0066F00000rM4Kc   \n", "2                 None                  None   0066F00000rM4Kc   \n", "3                 None                  None   0066F00000rM4Kc   \n", "4                 None                  None   0066F00000rM4Kc   \n", "...                ...                   ...               ...   \n", "264218              No                  None   006IS000003giIP   \n", "264219              No                  None   006IS000003giI0   \n", "264220              No                  None   006IS0000046Ct4   \n", "264221              No                  None   006IS0000044cJh   \n", "264222              No                  None   006IS000003gqRq   \n", "\n", "                      Name Probability JD_Appended__c  \\\n", "0       采购80台MacBook Pro办公       100.0          False   \n", "1       采购80台MacBook Pro办公       100.0          False   \n", "2       采购80台MacBook Pro办公       100.0          False   \n", "3       采购80台MacBook Pro办公       100.0          False   \n", "4       采购80台MacBook Pro办公       100.0          False   \n", "...                    ...         ...            ...   \n", "264218                办公自用        75.0          False   \n", "264219                办公用机        75.0          False   \n", "264220                办公用机        75.0          False   \n", "264221                  零售        25.0          False   \n", "264222                办公自用        25.0          False   \n", "\n", "                                        Apple_Reseller__r  \n", "0       {'attributes': {'type': 'User', 'url': '/servi...  \n", "1       {'attributes': {'type': 'User', 'url': '/servi...  \n", "2       {'attributes': {'type': 'User', 'url': '/servi...  \n", "3       {'attributes': {'type': 'User', 'url': '/servi...  \n", "4       {'attributes': {'type': 'User', 'url': '/servi...  \n", "...                                                   ...  \n", "264218  {'attributes': {'type': 'User', 'url': '/servi...  \n", "264219  {'attributes': {'type': 'User', 'url': '/servi...  \n", "264220  {'attributes': {'type': 'User', 'url': '/servi...  \n", "264221  {'attributes': {'type': 'User', 'url': '/servi...  \n", "264222  {'attributes': {'type': 'User', 'url': '/servi...  \n", "\n", "[264223 rows x 29 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 将嵌套的 OpportunityLineItems 展开到 DataFrame\n", "df_opportunity_line_items = pd.json_normalize(\n", "    all_records,\n", "    record_path=['OpportunityLineItems', 'records'],\n", "    meta=meta\n", "    \n", ")\n", "\n", "# 打印或保存 DataFrame\n", "df_opportunity_line_items\n", "# df_opportunity_line_items.to_csv('salesforce_data.csv', index=False)  # 可选：保存到 CSV 文件"]}, {"cell_type": "code", "execution_count": 20, "id": "895932f2-2be4-4168-ba88-229794eed97e", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Enroll_Date__c</th>\n", "      <th>Acquisition_Group__c</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 107 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c Product_Family__c Quarter__c  \\\n", "0          3191.75  FY19          FY19Q2       MacBook Pro         Q2   \n", "1          1082.00  FY19          FY19Q3       MacBook Air         Q3   \n", "2         16296.00  FY19          FY19Q2       MacBook Pro         Q2   \n", "3          2059.52  FY19          FY19Q2       MacBook Pro         Q2   \n", "4          2712.73  FY19          FY19Q2       MacBook Pro         Q2   \n", "...            ...   ...             ...               ...        ...   \n", "264218     1291.00  FY24          FY24Q3       MacBook Air         Q3   \n", "264219     1291.00  FY24          FY24Q3       MacBook Air         Q3   \n", "264220     9676.00  FY24          FY24Q4       MacBook Pro         Q4   \n", "264221     3661.00  FY24          FY24Q4        Vision Pro         Q4   \n", "264222     2403.00  FY24          FY24Q4              iMac         Q4   \n", "\n", "       Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0                       W08       00k6F0000168pJf   \n", "1                       W03       00k6F0000169QKA   \n", "2                       W12       00k6F0000169aqO   \n", "3                       W13       00k6F0000169l19   \n", "4                       W05       00k6F000015xT5H   \n", "...                     ...                   ...   \n", "264218                  W10       00kIS00000757ZB   \n", "264219                  W09       00kIS0000074ual   \n", "264220                  W07       00kIS000007lKUp   \n", "264221                  W02       00kIS0000077qYX   \n", "264222                  W12       00kIS00000757Un   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                              Z0V1       1.0                  CPU  ...   \n", "1                         MREA2CH/A       1.0                  CPU  ...   \n", "2                              Z0UK      12.0                  CPU  ...   \n", "3                              Z0V7       1.0                  CPU  ...   \n", "4                              Z0V0       1.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "264218                         Z1BB       1.0                  CPU  ...   \n", "264219                         Z1BP       1.0                  CPU  ...   \n", "264220                         Z1AY       4.0                  CPU  ...   \n", "264221                    MW8X3CH/A       1.0               Vision  ...   \n", "264222                         Z19Q       1.0                  CPU  ...   \n", "\n", "       Enroll_Date__c Acquisition_Group__c NCR_Program__c  \\\n", "0                None                 None          False   \n", "1                None                 None          False   \n", "2                None                 None          False   \n", "3                None                 None          False   \n", "4                None                 None          False   \n", "...               ...                  ...            ...   \n", "264218           None                 None          False   \n", "264219           None                 None          False   \n", "264220           None                 None          False   \n", "264221           None                 None          False   \n", "264222           None                 None          False   \n", "\n", "       Reseller_for_acquisition__c Status__c attributes.type_account  \\\n", "0                             None      None                 Account   \n", "1                             None      None                 Account   \n", "2                             None      None                 Account   \n", "3                             None      None                 Account   \n", "4                             None      None                 Account   \n", "...                            ...       ...                     ...   \n", "264218                        None      None                 Account   \n", "264219                        None      None                 Account   \n", "264220                        None      None                 Account   \n", "264221                        None      None                 Account   \n", "264222                        None      None                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/0016F000...   \n", "1       /services/data/v59.0/sobjects/Account/0016F000...   \n", "2       /services/data/v59.0/sobjects/Account/0016F000...   \n", "3       /services/data/v59.0/sobjects/Account/0016F000...   \n", "4       /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                   ...   \n", "264218  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264219  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264220  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264221  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264222  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "264218                  User   \n", "264219                  User   \n", "264220                  User   \n", "264221                  User   \n", "264222                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \n", "0       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>  \n", "1       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>  \n", "2       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>  \n", "3       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>  \n", "4       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>  \n", "...                                                   ...            ...  \n", "264218  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "264219  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "264220  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "264221  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "264222  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "\n", "[264223 rows x 107 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Account'])\n", "\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Account']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_account'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items"]}, {"cell_type": "code", "execution_count": null, "id": "1222d470-72d6-4963-838c-712e49dfd580", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 21, "id": "b01d3147-de43-43ea-b000-89df8296b3a4", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Acquisition_Group__c</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 107 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c Product_Family__c Quarter__c  \\\n", "0          3191.75  FY19          FY19Q2       MacBook Pro         Q2   \n", "1          1082.00  FY19          FY19Q3       MacBook Air         Q3   \n", "2         16296.00  FY19          FY19Q2       MacBook Pro         Q2   \n", "3          2059.52  FY19          FY19Q2       MacBook Pro         Q2   \n", "4          2712.73  FY19          FY19Q2       MacBook Pro         Q2   \n", "...            ...   ...             ...               ...        ...   \n", "264218     1291.00  FY24          FY24Q3       MacBook Air         Q3   \n", "264219     1291.00  FY24          FY24Q3       MacBook Air         Q3   \n", "264220     9676.00  FY24          FY24Q4       MacBook Pro         Q4   \n", "264221     3661.00  FY24          FY24Q4        Vision Pro         Q4   \n", "264222     2403.00  FY24          FY24Q4              iMac         Q4   \n", "\n", "       Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0                       W08       00k6F0000168pJf   \n", "1                       W03       00k6F0000169QKA   \n", "2                       W12       00k6F0000169aqO   \n", "3                       W13       00k6F0000169l19   \n", "4                       W05       00k6F000015xT5H   \n", "...                     ...                   ...   \n", "264218                  W10       00kIS00000757ZB   \n", "264219                  W09       00kIS0000074ual   \n", "264220                  W07       00kIS000007lKUp   \n", "264221                  W02       00kIS0000077qYX   \n", "264222                  W12       00kIS00000757Un   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                              Z0V1       1.0                  CPU  ...   \n", "1                         MREA2CH/A       1.0                  CPU  ...   \n", "2                              Z0UK      12.0                  CPU  ...   \n", "3                              Z0V7       1.0                  CPU  ...   \n", "4                              Z0V0       1.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "264218                         Z1BB       1.0                  CPU  ...   \n", "264219                         Z1BP       1.0                  CPU  ...   \n", "264220                         Z1AY       4.0                  CPU  ...   \n", "264221                    MW8X3CH/A       1.0               Vision  ...   \n", "264222                         Z19Q       1.0                  CPU  ...   \n", "\n", "       Acquisition_Group__c NCR_Program__c Reseller_for_acquisition__c  \\\n", "0                      None          False                        None   \n", "1                      None          False                        None   \n", "2                      None          False                        None   \n", "3                      None          False                        None   \n", "4                      None          False                        None   \n", "...                     ...            ...                         ...   \n", "264218                 None          False                        None   \n", "264219                 None          False                        None   \n", "264220                 None          False                        None   \n", "264221                 None          False                        None   \n", "264222                 None          False                        None   \n", "\n", "       Status__c attributes.type_account  \\\n", "0           None                 Account   \n", "1           None                 Account   \n", "2           None                 Account   \n", "3           None                 Account   \n", "4           None                 Account   \n", "...          ...                     ...   \n", "264218      None                 Account   \n", "264219      None                 Account   \n", "264220      None                 Account   \n", "264221      None                 Account   \n", "264222      None                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/0016F000...   \n", "1       /services/data/v59.0/sobjects/Account/0016F000...   \n", "2       /services/data/v59.0/sobjects/Account/0016F000...   \n", "3       /services/data/v59.0/sobjects/Account/0016F000...   \n", "4       /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                   ...   \n", "264218  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264219  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264220  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264221  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264222  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "264218                  User   \n", "264219                  User   \n", "264220                  User   \n", "264221                  User   \n", "264222                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \\\n", "0       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "1       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "2       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "3       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "4       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "...                                                   ...            ...   \n", "264218  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "264219  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "264220  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "264221  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "264222  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "         Name_disti/t1  \n", "0       倍升互联（北京）科技有限公司  \n", "1       倍升互联（北京）科技有限公司  \n", "2       倍升互联（北京）科技有限公司  \n", "3       倍升互联（北京）科技有限公司  \n", "4       倍升互联（北京）科技有限公司  \n", "...                ...  \n", "264218  伟仕佳杰（重庆）科技有限公司  \n", "264219  伟仕佳杰（重庆）科技有限公司  \n", "264220  伟仕佳杰（重庆）科技有限公司  \n", "264221  伟仕佳杰（重庆）科技有限公司  \n", "264222  伟仕佳杰（重庆）科技有限公司  \n", "\n", "[264223 rows x 107 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Apple_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'Name_disti/t1'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Apple_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_disti/t1'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 22, "id": "ac201719-7833-408c-9d30-f9416455cc31", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "      <th>T2_Reseller</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 107 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c Product_Family__c Quarter__c  \\\n", "0          3191.75  FY19          FY19Q2       MacBook Pro         Q2   \n", "1          1082.00  FY19          FY19Q3       MacBook Air         Q3   \n", "2         16296.00  FY19          FY19Q2       MacBook Pro         Q2   \n", "3          2059.52  FY19          FY19Q2       MacBook Pro         Q2   \n", "4          2712.73  FY19          FY19Q2       MacBook Pro         Q2   \n", "...            ...   ...             ...               ...        ...   \n", "264218     1291.00  FY24          FY24Q3       MacBook Air         Q3   \n", "264219     1291.00  FY24          FY24Q3       MacBook Air         Q3   \n", "264220     9676.00  FY24          FY24Q4       MacBook Pro         Q4   \n", "264221     3661.00  FY24          FY24Q4        Vision Pro         Q4   \n", "264222     2403.00  FY24          FY24Q4              iMac         Q4   \n", "\n", "       Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0                       W08       00k6F0000168pJf   \n", "1                       W03       00k6F0000169QKA   \n", "2                       W12       00k6F0000169aqO   \n", "3                       W13       00k6F0000169l19   \n", "4                       W05       00k6F000015xT5H   \n", "...                     ...                   ...   \n", "264218                  W10       00kIS00000757ZB   \n", "264219                  W09       00kIS0000074ual   \n", "264220                  W07       00kIS000007lKUp   \n", "264221                  W02       00kIS0000077qYX   \n", "264222                  W12       00kIS00000757Un   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                              Z0V1       1.0                  CPU  ...   \n", "1                         MREA2CH/A       1.0                  CPU  ...   \n", "2                              Z0UK      12.0                  CPU  ...   \n", "3                              Z0V7       1.0                  CPU  ...   \n", "4                              Z0V0       1.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "264218                         Z1BB       1.0                  CPU  ...   \n", "264219                         Z1BP       1.0                  CPU  ...   \n", "264220                         Z1AY       4.0                  CPU  ...   \n", "264221                    MW8X3CH/A       1.0               Vision  ...   \n", "264222                         Z19Q       1.0                  CPU  ...   \n", "\n", "       NCR_Program__c Reseller_for_acquisition__c Status__c  \\\n", "0               False                        None      None   \n", "1               False                        None      None   \n", "2               False                        None      None   \n", "3               False                        None      None   \n", "4               False                        None      None   \n", "...               ...                         ...       ...   \n", "264218          False                        None      None   \n", "264219          False                        None      None   \n", "264220          False                        None      None   \n", "264221          False                        None      None   \n", "264222          False                        None      None   \n", "\n", "       attributes.type_account  \\\n", "0                      Account   \n", "1                      Account   \n", "2                      Account   \n", "3                      Account   \n", "4                      Account   \n", "...                        ...   \n", "264218                 Account   \n", "264219                 Account   \n", "264220                 Account   \n", "264221                 Account   \n", "264222                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/0016F000...   \n", "1       /services/data/v59.0/sobjects/Account/0016F000...   \n", "2       /services/data/v59.0/sobjects/Account/0016F000...   \n", "3       /services/data/v59.0/sobjects/Account/0016F000...   \n", "4       /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                   ...   \n", "264218  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264219  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264220  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264221  /services/data/v59.0/sobjects/Account/001IS000...   \n", "264222  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "264218                  User   \n", "264219                  User   \n", "264220                  User   \n", "264221                  User   \n", "264222                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \\\n", "0       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "1       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "2       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "3       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "4       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "...                                                   ...            ...   \n", "264218  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "264219  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "264220  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "264221  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "264222  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "         Name_disti/t1   T2_Reseller  \n", "0       倍升互联（北京）科技有限公司           NaN  \n", "1       倍升互联（北京）科技有限公司           NaN  \n", "2       倍升互联（北京）科技有限公司           NaN  \n", "3       倍升互联（北京）科技有限公司           NaN  \n", "4       倍升互联（北京）科技有限公司           NaN  \n", "...                ...           ...  \n", "264218  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司  \n", "264219  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司  \n", "264220  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司  \n", "264221  伟仕佳杰（重庆）科技有限公司    山东亿达数码有限公司  \n", "264222  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司  \n", "\n", "[264223 rows x 107 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 T2_Reseller__r 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['T2_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'T2_Reseller'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['T2_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_T2_Reseller'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 23, "id": "dbf7958d-5833-4b75-bb44-bb99fff2169d", "metadata": {}, "outputs": [], "source": ["# 删除列名中的 '__c' 并替换 '_' 为 ' '\n", "df_opportunity_line_items.columns = df_opportunity_line_items.columns.str.replace('__c$', '', regex=True).str.replace('_', ' ')"]}, {"cell_type": "code", "execution_count": 24, "id": "6548de6a-ff70-4439-ab02-d40aaf5e5805", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Revenue', 'FY', 'ST FYQuarter', 'Product Family', 'Quarter', 'Sell Through Week', 'Oppty Line Item ID', 'Marketing Part Number MPN', 'Quantity', 'Line of business2', 'attributes.type original', 'attributes.url original', 'attributes', 'Project Type', 'Sold To ID', 'Opportunity Reseller Apple ID', 'Apple HQ ID', 'Opportunity Reseller Track', 'ESC Store', 'Opportunity Type', 'leasingornot', 'Penetrated Account', 'Opportunity ID', 'Name original', 'Probability', 'JD Appended', 'Account Group', 'Group Province', 'Group City', 'Province', 'City', 'Sub Segment', 'Vertical Industry', 'zhan<PERSON><PERSON>jian', 'Mac as Choice start time', 'FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY22Q1', 'FY25Q1 Top Account', 'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account', 'FY25Q1 AE', 'FY24Q4 AE', 'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE', 'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE', 'FY25Q1 AEM', 'FY24Q4 AEM', 'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM', 'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM', 'FY21Q2 AE', 'FY21Q2 AEM', 'FY21Q3 AE', 'FY21Q3 AEM', 'FY21Q4 AE', 'FY21Q4 AEM', 'FY22Q1 AE', 'FY22Q1 AEM', 'Segment', 'Large Account', 'Total Mac Demand', 'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Industry Target Account', 'Source Detail', 'Sales Region', 'Name account', 'Account ID', 'Mac as Choice', 'Top Account Deep Dive', 'Enroll Date', 'Acquisition Group', 'NCR Program', 'Reseller for acquisition', 'Status', 'attributes.type account', 'attributes.url account', 'Owner.attributes.type', 'Owner.attributes.url', 'Owner.Name', 'Name disti/t1', 'T2 Reseller']\n"]}], "source": ["print(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 25, "id": "f2534630-014b-4769-bb99-248ef0f71315", "metadata": {"tags": []}, "outputs": [], "source": ["# 240812新增rename 'Name original':'Opportunity Name'\n", "df_opportunity_line_items.rename(columns={'Name account':'Account Name','Owner.Name':'Account Owner','Apple HQ ID':'Apple ID','Project Type':'Deal type',\n", "    'Name disti/t1':'Disti/T1 Reseller','FY22Q1':'FY22Q1 Large Account',\n", "                                          'leasingornot':'Leasing or Not','Line of business2':'Line of Business','zhanbaoshijian':'Mac as Choice加入时间',\n", "                                         'Mac as Choice start time':'Mac as Choice start time','Marketing Part Number MPN':'Marketing Part Number (MPN)','Probability':'Probability (%)',\n", "                                         'Quarter':'Product ST Quarter','Sell Through Week':'Product ST Week' ,'Revenue':'ProductLineRevenue','Province':'Province/Region',\n", "                                         'Opportunity Reseller Apple ID':'Reseller Apple ID','Opportunity Reseller Track':'Reseller Track','Name original':'Opportunity Name',\n", "                                         'Enroll Date':'NCR Enroll Date','Acquisition Group':'NCR Group','Reseller for acquisition':'NCR Reseller','Status':'NCR Status'\n", "                                         }, inplace=True)"]}, {"cell_type": "code", "execution_count": 26, "id": "bbb2ad26-b36f-4faa-9438-a5e2f22f8c36", "metadata": {"tags": []}, "outputs": [], "source": ["df_header_filepath = (f'one_time_startfromFY19_getpplby_api_data.csv').replace('\\\\','')\n", "# 对比header\n", "# df_header = pd.read_csv(df_header_filepath).head(10)\n", "# set(list(df_header)) - set(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 27, "id": "3938cf4a-3206-4c99-afcb-3ccfbe405f95", "metadata": {"tags": []}, "outputs": [], "source": ["# 将布尔列转换为 0 和 1\n", "bool_columns = df_opportunity_line_items.select_dtypes(include='bool').columns\n", "df_opportunity_line_items[bool_columns] = df_opportunity_line_items[bool_columns].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "9be1793a-e2f4-4247-a587-a326f4e5ccfa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 28, "id": "05a4bb15-dfa0-4a6c-8b8c-a6097ccebf58", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns removed successfully.\n"]}], "source": ["#240812减少要删除的列 Name original\n", "# 要删除的列列表\n", "columns_to_remove = ['JD Appended','Owner.attributes.type','Owner.attributes.url',\n", "                     'attributes','attributes.type account','attributes.type original','attributes.url account','attributes.url original'\n", "]\n", "\n", "# 尝试删除列，并在失败时捕获异常\n", "try:\n", "    df_opportunity_line_items.drop(columns=columns_to_remove, inplace=True)\n", "    print(\"Columns removed successfully.\")\n", "except KeyError as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "3e337560-e2bf-42d2-94ac-a245a9192702", "metadata": {"tags": []}, "outputs": [], "source": ["df_opportunity_line_items.to_csv(df_header_filepath, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "4ade7c72-b559-4e31-91ca-dd55827d25fd", "metadata": {"tags": []}, "outputs": [], "source": ["bool_columns"]}, {"cell_type": "code", "execution_count": null, "id": "3e81c82b-6dec-435f-bd56-4cfbbfd270a2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}