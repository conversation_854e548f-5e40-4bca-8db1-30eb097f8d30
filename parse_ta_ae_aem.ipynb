{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1248a574-721f-4135-b3d8-016689b818e5", "metadata": {}, "outputs": [], "source": ["import os\n", "import datetime\n", "import configparser\n", "import requests\n", "import json\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "id": "a1a86fe7-af52-47a4-b6b4-1c22c7665a69", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "b4269bfd-c787-4ef5-b649-7332ea2f6d08", "metadata": {}, "outputs": [], "source": ["from acquire_mapping import Mapping"]}, {"cell_type": "code", "execution_count": 4, "id": "ecd0b64d-6c18-47cf-812f-1e12e4fef44e", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 5, "id": "ef908fa0-5f30-43c5-8ba0-e6b46eecfa0e", "metadata": {}, "outputs": [], "source": ["dfsf = pd.read_csv(f'{homepath}/Library/CloudStorage/Box-Box/Planning Team/Tableau Auto-Refresh Raw Data/19 SalesForce PPL/PPL_by_Accountname_FY21_CQ_byapi.csv')"]}, {"cell_type": "code", "execution_count": 10, "id": "6c8e894d-2071-40bb-88f2-185aefcccc59", "metadata": {}, "outputs": [{"data": {"text/plain": ["15967"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["len(dfsf['Account Name'].unique())"]}, {"cell_type": "code", "execution_count": 12, "id": "79ade46b-92aa-4f04-aaef-63d14d53c693", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY25Q3 Top Account', 'FY25Q2 Top Account', 'FY25Q1 Top Account', 'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account', 'FY25Q3 AE', 'FY25Q2 AE', 'FY25Q1 AE', 'FY24Q4 AE', 'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE', 'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE', 'FY25Q3 AEM', 'FY25Q2 AEM', 'FY25Q1 AEM', 'FY24Q4 AEM', 'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM', 'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM', 'FY21Q2 AE', 'FY21Q2 AEM', 'FY21Q3 AE', 'FY21Q3 AEM', 'FY21Q4 AE', 'FY21Q4 AEM', 'FY22Q1 AE', 'FY22Q1 AEM']\n"]}], "source": ["# 找出以 AE, AEM, 或 Top Account 结尾的列\n", "matched_columns = [col for col in dfsf.columns if col.endswith(('AE', 'AEM', 'Top Account'))]\n", "\n", "print(matched_columns)"]}, {"cell_type": "code", "execution_count": 29, "id": "18c97eb7-b4b7-443d-b4c2-07b9479f2bc4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account', 'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE', 'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE', 'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM', 'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM']\n"]}], "source": ["# 你给出的目标前缀列表\n", "prefixes = ['FY24Q3', 'FY24Q2', 'FY24Q1',\n", "            'FY23Q4', 'FY23Q3', 'FY23Q2', 'FY23Q1',\n", "            'FY22Q4', 'FY22Q3', 'FY22Q2']\n", "\n", "# 筛选匹配这些前缀的列名\n", "filtered_columns = [col for col in matched_columns if any(col.startswith(prefix) for prefix in prefixes)]\n", "\n", "print(filtered_columns)"]}, {"cell_type": "code", "execution_count": 30, "id": "501db1c1-c662-4d95-8f4b-63b84578ec1a", "metadata": {}, "outputs": [], "source": ["filtered_columns.append('Account Name')"]}, {"cell_type": "code", "execution_count": 22, "id": "1ed7b34d-9479-4015-8148-818de0d533ae", "metadata": {}, "outputs": [], "source": ["# filtered_columns.append('Account Group')"]}, {"cell_type": "code", "execution_count": 55, "id": "7c25d9c3-1769-490f-8503-0f99cb53bfa2", "metadata": {}, "outputs": [], "source": ["dfsf1 = dfsf[filtered_columns].drop_duplicates()"]}, {"cell_type": "code", "execution_count": 56, "id": "6d3c3760-5393-45a6-91a7-c85fc5850f01", "metadata": {}, "outputs": [], "source": ["# 所有要处理的列\n", "all_cols = [\n", "    'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account',\n", "    'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account',\n", "    'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account',\n", "    'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE',\n", "    'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE',\n", "    'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM',\n", "    'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM'\n", "]\n", "\n", "# 拆分为两类\n", "top_account_cols = [col for col in all_cols if 'Top Account' in col]\n", "ae_aem_cols = [col for col in all_cols if col not in top_account_cols]\n", "\n", "# 先把 0 也当成缺失处理\n", "dfsf1[top_account_cols + ae_aem_cols] = dfsf1[top_account_cols + ae_aem_cols].replace(0, pd.NA)\n", "\n", "# 对 Top Account 类：组内补全，然后把剩余 NaN → 0\n", "dfsf1[top_account_cols] = dfsf1.groupby('Account Name')[top_account_cols].transform(lambda x: x.ffill().bfill())\n", "dfsf1[top_account_cols] = dfsf1[top_account_cols].fillna(0)\n", "\n", "# 对 AE 和 AEM 类：组内补全，不填回 0，保持 NaN\n", "dfsf1[ae_aem_cols] = dfsf1.groupby('Account Name')[ae_aem_cols].transform(lambda x: x.ffill().bfill())"]}, {"cell_type": "code", "execution_count": 57, "id": "e4eec8d2-3b8b-4719-bc3c-23e6e386c489", "metadata": {}, "outputs": [], "source": ["dfsf1.loc[dfsf1['Account Name'] == '上海识致信息科技有限责任公司', 'FY23Q1 AEM'] = 'Xiaomeng Li'"]}, {"cell_type": "code", "execution_count": 58, "id": "1d31dfff-e927-4e87-8a3c-cbd8c7e6e519", "metadata": {}, "outputs": [], "source": ["dfsf1.loc[dfsf1['Account Name'] == '中国南方航空股份有限公司', 'FY22Q3 AEM'] = 'Sunny Li'"]}, {"cell_type": "code", "execution_count": null, "id": "02b14cc5-66ba-4334-96fe-1d47cc4da6cf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 59, "id": "e53f85be-9d94-421b-8d15-6bbbcc533645", "metadata": {}, "outputs": [], "source": ["# # 你指定的需要处理的列\n", "# cols_to_fill = [\n", "#     'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account',\n", "#     'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account',\n", "#     'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account',\n", "#     'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE',\n", "#     'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE',\n", "#     'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM',\n", "#     'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM'\n", "# ]\n", "\n", "# # 将 0 视作缺失值\n", "# dfsf1[cols_to_fill] = dfsf1[cols_to_fill].replace(0, pd.NA)\n", "\n", "# # 按 Account Name 分组，对这些列向组内填充非缺失值\n", "# dfsf1[cols_to_fill] = dfsf1.groupby('Account Name')[cols_to_fill].transform(lambda x: x.ffill().bfill())\n", "\n", "# # 最后将仍为 NA 的填回 0（如果你需要）\n", "# dfsf1[cols_to_fill] = dfsf1[cols_to_fill].fillna(0)"]}, {"cell_type": "code", "execution_count": 60, "id": "********-cf00-4ffb-9940-8040d85e0c35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["             Account Name\n", "1667     深圳赛诺菲巴斯德生物制品有限公司\n", "1960      拉扎斯网络科技（上海）有限公司\n", "3082             桂林航空有限公司\n", "3087           长安航空有限责任公司\n", "6025    上海凯长信息科技有限公司无锡分公司\n", "...                   ...\n", "191038      昆仑芯（北京）科技有限公司\n", "197123         北京诺华制药有限公司\n", "209530    埃培智市场咨询（上海）有限公司\n", "211373       云南祥鹏航空有限责任公司\n", "213715     优酷信息技术（北京）有限公司\n", "\n", "[72 rows x 1 columns]\n"]}], "source": ["# 找出 Account Name 和 Account Group 同时重复的行（至少出现两次）\n", "duplicated_accounts = dfsf1[dfsf1.duplicated(subset=['Account Name'], keep=False)]\n", "\n", "# 显示这些重复的 Account Name 和 Account Group（去重组合）\n", "repeated_pairs = duplicated_accounts[['Account Name']].drop_duplicates()\n", "\n", "print(repeated_pairs)"]}, {"cell_type": "code", "execution_count": 61, "id": "117c2bfa-9154-483c-8f1b-4e8535a09d93", "metadata": {}, "outputs": [], "source": ["# 取重复的 Account Name 列表\n", "repeated_names = repeated_pairs['Account Name'].unique()\n", "\n", "# 只保留这些重复客户的记录\n", "df_repeat = dfsf1[dfsf1['Account Name'].isin(repeated_names)]\n", "\n", "# 检查这些客户每列是否有多个不同值（即是否存在不一致）\n", "# 先按 Account Name 分组，再统计每列唯一值数量\n", "diff_check = df_repeat.groupby('Account Name').nunique(dropna=False)\n", "\n", "# 找出有值不一致的列（即某列的唯一值数 > 1）\n", "inconsistent = diff_check.apply(lambda row: row[row > 1].index.tolist(), axis=1)\n", "\n", "# 打印哪些客户在哪些列上有不同值\n", "for acct, diff_cols in inconsistent.items():\n", "    if diff_cols:\n", "        print(f\"客户 {acct} 存在不同值的列：{diff_cols}\")"]}, {"cell_type": "code", "execution_count": null, "id": "71e94f08-c8f7-4f2b-95df-d0f1e104e1f0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 62, "id": "cdcdaf66-3a06-47be-82aa-09c0adfb6dd5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["所有重复记录完全一致，已自动去重。\n"]}], "source": ["\n", "\n", "# 按 Account Name 分组\n", "grouped = dfsf1.groupby('Account Name')\n", "\n", "# 找出有多个不同记录的 Account Name（即组内有不同的值）\n", "conflict_accounts = []\n", "for name, group in grouped:\n", "    if len(group) > 1 and group.drop_duplicates().shape[0] > 1:\n", "        conflict_accounts.append(name)\n", "\n", "# 有冲突账户（数据不一致）时打印它们\n", "if conflict_accounts:\n", "    print(\"以下 Account Name 的记录在其他字段存在差异，请人工处理：\")\n", "    for acct in conflict_accounts:\n", "        print(acct)\n", "else:\n", "    # 没有冲突，可以安全去重\n", "    dfsf1 = dfsf1.drop_duplicates()\n", "    print(\"所有重复记录完全一致，已自动去重。\")"]}, {"cell_type": "code", "execution_count": 64, "id": "4304a5d9-1526-488f-939b-2c0a48a5d3b1", "metadata": {}, "outputs": [], "source": ["dfsf1.to_csv(f'{homepath}/Documents/ML/DIM/TA_AE_AEM_FY22Q2toFY24Q3.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "7e28c85e-0ec0-4cdc-a117-eb2dc48f6f20", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}