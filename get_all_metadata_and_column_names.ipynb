{"cells": [{"cell_type": "code", "execution_count": 6, "id": "d354ae77-c9f8-4361-bb21-290476cf4db3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import datetime\n", "import os\n", "from string import Template\n", "from pathlib import Path\n", "import configparser\n", "import requests\n", "import json\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 7, "id": "19c1b01f-6f9c-4239-819f-167fddc10655", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 8, "id": "d93186ce-497f-4043-875e-934ae760e4d1", "metadata": {}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQ4AQA6JhzNdy7NgWrf.WWFm.5kG85evS7HMU8DPnhiiSO2zhqBIEo6Z8ig5swbKN4ilpkFBagVhDsAuWo4IlTprlsykVfL5'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 10, "id": "968747b5-bfa7-41d9-bb14-dfd159366f28", "metadata": {"scrolled": true}, "outputs": [], "source": ["\n", "\n", "# 替换为你的 Salesforce 实例域名和报表 ID\n", "instance_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "# report_id = \"00OIS000001TPCY\"\n", "report_id = \"00OIS000001TEOk\"\n", "# access_token = \"00D20000000JPf6!AQ4AQDJU3_dhyicWbI4oh7DhiRhiAGomTWxU5SPWEc.RW2p1LaiZUlKCMx68rVWu1gSvVcFfbH3gEPUBrFLup0rQw576pYml\"\n", "\n", "# 构建请求 URL\n", "url = f\"{instance_url}/services/data/v59.0/analytics/reports/{report_id}/describe\"\n", "\n", "# 设置请求头\n", "headers = {\n", "    \"Authorization\": f\"Bearer {access_token}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# 发送请求\n", "response = requests.get(url, headers=headers)\n", "\n", "# 检查响应状态码\n", "if response.status_code == 200:\n", "    report_metadata = response.json()\n", "    # 打印或处理报表元数据\n", "    # print(report_metadata)\n", "else:\n", "    print(f\"Failed to retrieve report metadata. Status code: {response.status_code}, Response: {response.text}\")"]}, {"cell_type": "code", "execution_count": 11, "id": "7112ccbd-1021-4a28-8abe-ce257ffcb282", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Account.Account_Group__c',\n", " 'OpportunityLineItem.Revenue__c',\n", " 'Account.Enroll_Date__c',\n", " 'Account.Acquisition_Group__c',\n", " 'Account.NCR_Program__c',\n", " 'Account.Status__c',\n", " 'Account.Reseller_for_acquisition__c',\n", " 'Account.Group_Province__c',\n", " 'Account.Group_City__c',\n", " 'ACCOUNT_NAME',\n", " 'Account.Province__c',\n", " 'Account.City__c',\n", " 'Opportunity.Project_Type__c',\n", " 'Opportunity.Apple_Reseller__c',\n", " 'Opportunity.Sold_To_ID__c',\n", " 'Opportunity.Opportunity_Reseller_Apple_ID__c',\n", " 'Opportunity.T2_Reseller__c',\n", " 'Opportunity.Apple_HQ_ID__c',\n", " 'Opportunity.Opportunity_Reseller_Track__c',\n", " 'Opportunity.ESC_Store__c',\n", " 'Account.Sub_Segment__c',\n", " 'Account.Vertical_Industry__c',\n", " 'Account.<PERSON>_as_Choice__c',\n", " 'Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c',\n", " 'Account.<PERSON>_as_Choice_start_time__c',\n", " 'Account.Top_Account_Deep_Dive__c',\n", " 'Account.FY21Q3_Large_Account__c',\n", " 'Account.FY21Q4_Large_Account__c',\n", " 'Account.FY22Q1__c',\n", " 'Account.FY22Q2_Top_Account__c',\n", " 'Account.FY22Q3_Top_Account__c',\n", " 'Account.FY22Q4_Top_Account__c',\n", " 'Account.FY23Q1_Top_Account__c',\n", " 'Account.FY23Q2_Top_Account__c',\n", " 'Account.FY23Q3_Top_Account__c',\n", " 'Account.FY23Q4_Top_Account__c',\n", " 'Account.FY24Q1_Top_Account__c',\n", " 'Account.FY24Q2_Top_Account__c',\n", " 'Account.FY24Q3_Top_Account__c',\n", " 'Account.FY21Q2_AE__c',\n", " 'Account.FY21Q2_AEM__c',\n", " 'Account.FY21Q3_AE__c',\n", " 'Account.FY21Q3_AEM__c',\n", " 'Account.FY21Q4_AE__c',\n", " 'Account.FY21Q4_AEM__c',\n", " 'Account.FY22Q1_AE__c',\n", " 'Account.FY22Q1_AEM__c',\n", " 'Account.FY22Q2_AE__c',\n", " 'Account.FY22Q2_AEM__c',\n", " 'Account.FY22Q3_AE__c',\n", " 'Account.FY22Q3_AEM__c',\n", " 'Account.FY22Q4_AE__c',\n", " 'Account.FY22Q4_AEM__c',\n", " 'Account.FY23Q1_AE__c',\n", " 'Account.FY23Q1_AEM__c',\n", " 'Account.FY23Q2_AE__c',\n", " 'Account.FY23Q2_AEM__c',\n", " 'Account.FY23Q3_AE__c',\n", " 'Account.FY23Q3_AEM__c',\n", " 'Account.FY23Q4_AE__c',\n", " 'Account.FY23Q4_AEM__c',\n", " 'Account.FY24Q1_AE__c',\n", " 'Account.FY24Q1_AEM__c',\n", " 'Account.FY24Q2_AE__c',\n", " 'Account.FY24Q2_AEM__c',\n", " 'Account.FY24Q3_AE__c',\n", " 'Account.FY24Q3_AEM__c',\n", " 'ACCOUNT_OWNER',\n", " 'QUANTITY',\n", " 'ACCOUNT_ID',\n", " 'FAMILY',\n", " 'OpportunityLineItem.FY__c',\n", " 'OpportunityLineItem.ST_FYQuarter__c',\n", " 'OpportunityLineItem.Product_Family__c',\n", " 'OpportunityLineItem.Quarter__c',\n", " 'OpportunityLineItem.Sell_Through_Week__c',\n", " 'Opportunity.Opportunity_Type__c',\n", " 'OPPORTUNITY_ID',\n", " 'OPPORTUNITY_NAME',\n", " 'OpportunityLineItem.Oppty_Line_Item_ID__c',\n", " 'PROBABILITY',\n", " 'Account.Segment__c',\n", " 'Account.Large_Account__c',\n", " 'OpportunityLineItem.Marketing_Part_Number_MPN__c',\n", " 'Account.Total_Mac_Demand__c',\n", " 'Account.PC_Install_Base__c',\n", " 'Account.FY22_Fcst__c',\n", " 'Account.FY23_Fcst__c',\n", " 'Opportunity.leasingornot__c',\n", " 'Account.Industry_Target_Account__c',\n", " 'Opportunity.Penetrated_Account__c',\n", " 'Account.Source_Detail__c',\n", " 'Account.Sales_Region__c']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取列名\n", "report_metadata['reportMetadata']['detailColumns']"]}, {"cell_type": "code", "execution_count": 12, "id": "43e500c2-d8a5-496a-9ab9-65ce44806f9f", "metadata": {}, "outputs": [], "source": ["# 获取全部信息并导出\n", "\n", "# 将 JSON 数据导出并美化\n", "with open('all.json', 'w', encoding='utf-8') as json_file:\n", "    json.dump(report_metadata, json_file, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": 13, "id": "722e6abe-0ab0-4612-8345-4dc6e6b3c304", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"Opportunity\": {\n", "        \"children\": [\n", "            \"Account\",\n", "            \"OpportunityLineItem\",\n", "            \"Project_Type__c\",\n", "            \"Apple_Reseller__c\",\n", "            \"Sold_To_ID__c\",\n", "            \"Opportunity_Reseller_Apple_ID__c\",\n", "            \"T2_Reseller__c\",\n", "            \"Apple_HQ_ID__c\",\n", "            \"Opportunity_Reseller_Track__c\",\n", "            \"ESC_Store__c\",\n", "            \"AccountId\",\n", "            \"Opportunity_Type__c\",\n", "            \"Id\",\n", "            \"Name\",\n", "            \"Probability\",\n", "            \"leasingornot__c\",\n", "            \"Penetrated_Account__c\"\n", "        ],\n", "        \"fields\": [\n", "            \"Account.Account_Group__c\",\n", "            \"OpportunityLineItem.Revenue__c\",\n", "            \"Account.Enroll_Date__c\",\n", "            \"Account.Acquisition_Group__c\",\n", "            \"Account.NCR_Program__c\",\n", "            \"Account.Status__c\",\n", "            \"Account.Reseller_for_acquisition__c\",\n", "            \"Account.Group_Province__c\",\n", "            \"Account.Group_City__c\",\n", "            \"ACCOUNT_NAME\",\n", "            \"Account.Province__c\",\n", "            \"Account.City__c\",\n", "            \"Opportunity.Project_Type__c\",\n", "            \"Opportunity.Apple_Reseller__c\",\n", "            \"Opportunity.Sold_To_ID__c\",\n", "            \"Opportunity.Opportunity_Reseller_Apple_ID__c\",\n", "            \"Opportunity.T2_Reseller__c\",\n", "            \"Opportunity.Apple_HQ_ID__c\",\n", "            \"Opportunity.Opportunity_Reseller_Track__c\",\n", "            \"Opportunity.ESC_Store__c\",\n", "            \"Account.Sub_Segment__c\",\n", "            \"Account.Vertical_Industry__c\",\n", "            \"Account.<PERSON>_as_Choice__c\",\n", "            \"Account.z<PERSON><PERSON><PERSON><PERSON><PERSON>__c\",\n", "            \"Account.<PERSON>_as_Choice_start_time__c\",\n", "            \"Account.Top_Account_Deep_Dive__c\",\n", "            \"Account.FY21Q3_Large_Account__c\",\n", "            \"Account.FY21Q4_Large_Account__c\",\n", "            \"Account.FY22Q1__c\",\n", "            \"Account.FY22Q2_Top_Account__c\",\n", "            \"Account.FY22Q3_Top_Account__c\",\n", "            \"Account.FY22Q4_Top_Account__c\",\n", "            \"Account.FY23Q1_Top_Account__c\",\n", "            \"Account.FY23Q2_Top_Account__c\",\n", "            \"Account.FY23Q3_Top_Account__c\",\n", "            \"Account.FY23Q4_Top_Account__c\",\n", "            \"Account.FY24Q1_Top_Account__c\",\n", "            \"Account.FY24Q2_Top_Account__c\",\n", "            \"Account.FY24Q3_Top_Account__c\",\n", "            \"Account.FY21Q2_AE__c\",\n", "            \"Account.FY21Q2_AEM__c\",\n", "            \"Account.FY21Q3_AE__c\",\n", "            \"Account.FY21Q3_AEM__c\",\n", "            \"Account.FY21Q4_AE__c\",\n", "            \"Account.FY21Q4_AEM__c\",\n", "            \"Account.FY22Q1_AE__c\",\n", "            \"Account.FY22Q1_AEM__c\",\n", "            \"Account.FY22Q2_AE__c\",\n", "            \"Account.FY22Q2_AEM__c\",\n", "            \"Account.FY22Q3_AE__c\",\n", "            \"Account.FY22Q3_AEM__c\",\n", "            \"Account.FY22Q4_AE__c\",\n", "            \"Account.FY22Q4_AEM__c\",\n", "            \"Account.FY23Q1_AE__c\",\n", "            \"Account.FY23Q1_AEM__c\",\n", "            \"Account.FY23Q2_AE__c\",\n", "            \"Account.FY23Q2_AEM__c\",\n", "            \"Account.FY23Q3_AE__c\",\n", "            \"Account.FY23Q3_AEM__c\",\n", "            \"Account.FY23Q4_AE__c\",\n", "            \"Account.FY23Q4_AEM__c\",\n", "            \"Account.FY24Q1_AE__c\",\n", "            \"Account.FY24Q1_AEM__c\",\n", "            \"Account.FY24Q2_AE__c\",\n", "            \"Account.FY24Q2_AEM__c\",\n", "            \"Account.FY24Q3_AE__c\",\n", "            \"Account.FY24Q3_AEM__c\",\n", "            \"ACCOUNT_OWNER\",\n", "            \"ACCOUNT_ID\",\n", "            \"FAMILY\",\n", "            \"OpportunityLineItem.FY__c\",\n", "            \"OpportunityLineItem.ST_FYQuarter__c\",\n", "            \"OpportunityLineItem.Product_Family__c\",\n", "            \"OpportunityLineItem.Quarter__c\",\n", "            \"OpportunityLineItem.Sell_Through_Week__c\",\n", "            \"Opportunity.Opportunity_Type__c\",\n", "            \"OPPORTUNITY_ID\",\n", "            \"OPPORTUNITY_NAME\",\n", "            \"OpportunityLineItem.Oppty_Line_Item_ID__c\",\n", "            \"PROBABILITY\",\n", "            \"Account.Segment__c\",\n", "            \"Account.Large_Account__c\",\n", "            \"OpportunityLineItem.Marketing_Part_Number_MPN__c\",\n", "            \"Account.Total_Mac_Demand__c\",\n", "            \"Account.PC_Install_Base__c\",\n", "            \"Account.FY22_Fcst__c\",\n", "            \"Account.FY23_Fcst__c\",\n", "            \"Opportunity.leasingornot__c\",\n", "            \"Account.Industry_Target_Account__c\",\n", "            \"Opportunity.Penetrated_Account__c\",\n", "            \"Account.Source_Detail__c\",\n", "            \"Account.Sales_Region__c\"\n", "        ]\n", "    },\n", "    \"QUANTITY\": {\n", "        \"children\": [],\n", "        \"fields\": [\n", "            \"QUANTITY\"\n", "        ]\n", "    }\n", "}\n"]}], "source": ["\n", "\n", "# 提取 detailColumnInfo 中的对象及其关系\n", "detail_column_info = report_metadata['reportExtendedMetadata']['detailColumnInfo']\n", "\n", "object_relationships = {}\n", "\n", "for field, info in detail_column_info.items():\n", "    # print(f\"Processing field: {field}, keys: {info.keys()}\")\n", "    if 'entityColumnName' in info:\n", "        entity_column_name = info['entityColumnName']\n", "    else:\n", "        entity_column_name = field\n", "\n", "    fully_qualified_name = info.get('fullyQualifiedName', entity_column_name)\n", "    \n", "    # 分割 fullyQualifiedName 获取对象关系\n", "    parts = fully_qualified_name.split('.')\n", "    if len(parts) > 1:\n", "        parent_object = parts[0]\n", "        child_object = parts[1]\n", "        \n", "        if parent_object not in object_relationships:\n", "            object_relationships[parent_object] = {'children': [], 'fields': []}\n", "        \n", "        if child_object not in object_relationships[parent_object]['children']:\n", "            object_relationships[parent_object]['children'].append(child_object)\n", "        \n", "        object_relationships[parent_object]['fields'].append(field)\n", "    else:\n", "        if parts[0] not in object_relationships:\n", "            object_relationships[parts[0]] = {'children': [], 'fields': []}\n", "        object_relationships[parts[0]]['fields'].append(field)\n", "\n", "# 打印对象及其关系\n", "print(json.dumps(object_relationships, ensure_ascii=False, indent=4))"]}, {"cell_type": "code", "execution_count": 14, "id": "cddfb6a4-3703-41bc-864d-5a943bf0b7f2", "metadata": {}, "outputs": [], "source": ["\n", "# 将 JSON 数据导出并美化\n", "with open('relation.json', 'w', encoding='utf-8') as json_file:\n", "    json.dump(object_relationships, json_file, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "id": "54e6d2d6-1c0a-4a35-a9fc-dc90be25ffc2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}