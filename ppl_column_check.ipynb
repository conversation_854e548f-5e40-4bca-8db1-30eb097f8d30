# ============================================================================
# Account Dictionary 数据转换流程
# ============================================================================
# 功能：将两个不同格式的Account Dictionary进行字段映射和转换
# 1. df_acent_account_dictionary: 标准报表格式的Account Dictionary
# 2. df_allaccount_list: API导出的All Account List，列名为时间季度格式
# 目标：实现两种格式之间的双向转换

import pandas as pd
import numpy as np
import os
import difflib  # 用于字符串相似度分析

# ============================================================================
# 步骤1: 设置路径
# ============================================================================
# 获取当前工作目录和用户主目录，用于后续文件路径设置

currentpath = os.getcwd()
homepath = os.environ['HOME']
print(f"当前工作目录: {currentpath}")
print(f"用户主目录: {homepath}")

# =============================================================================
# 加载SalesForce PPL数据
# =============================================================================

# 加载SalesForce PPL（Pipeline）数据
# 这是主要的销售机会数据，包含详细的销售预测和账户信息
print("正在加载SalesForce PPL数据...")
dfsf = pd.read_csv(f'{homepath}/Library/CloudStorage/Box-Box/Planning Team/Tableau Auto-Refresh Raw Data/19 SalesForce PPL/PPL_by_Accountname_FY21_CQ_byapi.csv',low_memory=False)

print(f"SalesForce PPL数据加载完成：{dfsf.shape}")
print(f"数据包含 {dfsf.shape[0]:,} 行记录，{dfsf.shape[1]} 个字段")

# 显示数据基本信息
print(f"\nSalesForce PPL数据基本信息:")
print(f"- 数据形状: {dfsf.shape}")
print(f"- 内存使用: {dfsf.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
print(f"- 列名前5个: {list(dfsf.columns[:5])}")

# 显示数据预览
dfsf

dfsf_columns = pd.read_excel(f'{homepath}/Documents/ML/started/salesforce/sf_acent_comparison.xlsx')
dfsf_columns

# 先把列名都转成字符串并去除首尾空白
set_dfsf   = set(col.strip() for col in map(str, dfsf.columns))
set_column = set(str(col).strip() for col in dfsf_columns['dfsf列名'].dropna())

# 判断集合是否相等
is_same = set_dfsf == set_column
print(f"元素集合是否一致：{is_same}")

# 如果不一致，分别列出差异
only_in_column = set_column - set_dfsf
only_in_dfsf    = set_dfsf - set_column

if only_in_column:
    print("只在 dfsf_columns['dfsf列名'] 中出现，但不在 dfsf.columns：")
    print(only_in_column)
if only_in_dfsf:
    print("只在 dfsf.columns 中出现，但不在 dfsf_columns['dfsf列名']：")
    print(only_in_dfsf)

df_elina = pd.read_excel(f'{homepath}/Documents/ML/started/salesforce/harry_elina_submission/GC SF所用字段.xlsx')


df_elina

