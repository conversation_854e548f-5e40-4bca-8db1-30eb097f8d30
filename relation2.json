{"Opportunity": {"children": ["Account", "OpportunityLineItem", "Owner", "Project_Type__c", "Apple_Reseller__c", "Opportunity_Reseller_Apple_ID__c", "T2_Reseller__c", "Apple_HQ_ID__c", "Opportunity_Reseller_Track__c", "ESC_Store__c", "AccountId", "Opportunity_Type__c", "Probability", "Penetrated_Account__c", "leasingornot__c", "Id", "Sold_To_ID__c", "Name"], "fields": ["Account.Account_Group__c", "OpportunityLineItem.Revenue__c", "User.Sales_Region__c", "Account.Group_Province__c", "Account.Group_City__c", "ACCOUNT_NAME", "Account.Province__c", "Account.City__c", "Opportunity.Project_Type__c", "Opportunity.Apple_Reseller__c", "Opportunity.Opportunity_Reseller_Apple_ID__c", "Opportunity.T2_Reseller__c", "Opportunity.Apple_HQ_ID__c", "Opportunity.Opportunity_Reseller_Track__c", "Opportunity.ESC_Store__c", "Account.Sub_Segment__c", "Account.Vertical_Industry__c", "Account.<PERSON>_as_Choice__c", "Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c", "Account.<PERSON>_as_Choice_start_time__c", "Account.Top_Account_Deep_Dive__c", "Account.FY21Q3_Large_Account__c", "Account.FY21Q4_Large_Account__c", "Account.FY22Q1__c", "Account.FY22Q2_Top_Account__c", "Account.FY22Q3_Top_Account__c", "Account.FY22Q4_Top_Account__c", "Account.FY23Q1_Top_Account__c", "Account.FY23Q2_Top_Account__c", "Account.FY23Q3_Top_Account__c", "Account.FY23Q4_Top_Account__c", "Account.FY24Q1_Top_Account__c", "Account.FY24Q2_Top_Account__c", "Account.FY24Q3_Top_Account__c", "Account.FY21Q2_AE__c", "Account.FY21Q2_AEM__c", "Account.FY21Q3_AE__c", "Account.FY21Q3_AEM__c", "Account.FY21Q4_AE__c", "Account.FY21Q4_AEM__c", "Account.FY22Q1_AE__c", "Account.FY22Q1_AEM__c", "Account.FY22Q2_AE__c", "Account.FY22Q2_AEM__c", "Account.FY22Q3_AE__c", "Account.FY22Q3_AEM__c", "Account.FY22Q4_AE__c", "Account.FY22Q4_AEM__c", "Account.FY23Q1_AE__c", "Account.FY23Q1_AEM__c", "Account.FY23Q2_AE__c", "Account.FY23Q2_AEM__c", "Account.FY23Q3_AE__c", "Account.FY23Q3_AEM__c", "Account.FY23Q4_AE__c", "Account.FY23Q4_AEM__c", "Account.FY24Q1_AE__c", "Account.FY24Q1_AEM__c", "Account.FY24Q2_AE__c", "Account.FY24Q2_AEM__c", "Account.FY24Q3_AEM__c", "Account.FY24Q3_AE__c", "ACCOUNT_OWNER", "ACCOUNT_ID", "FAMILY", "OpportunityLineItem.FY__c", "OpportunityLineItem.ST_FYQuarter__c", "OpportunityLineItem.Product_Family__c", "OpportunityLineItem.Quarter__c", "OpportunityLineItem.Sell_Through_Week__c", "Opportunity.Opportunity_Type__c", "PROBABILITY", "Account.Segment__c", "Account.Large_Account__c", "OpportunityLineItem.Marketing_Part_Number_MPN__c", "Account.PC_Install_Base__c", "Account.Source_Detail__c", "Account.Total_Mac_Demand__c", "Account.Industry_Target_Account__c", "Opportunity.Penetrated_Account__c", "Opportunity.leasingornot__c", "Account.FY22_Fcst__c", "Account.FY23_Fcst__c", "Account.Sales_Region__c", "OPPORTUNITY_ID", "OpportunityLineItem.Oppty_Line_Item_ID__c", "Opportunity.Sold_To_ID__c", "OPPORTUNITY_NAME"]}, "QUANTITY": {"children": [], "fields": ["QUANTITY"]}}