{"cells": [{"cell_type": "code", "execution_count": 2, "id": "3b4bd586-dbc4-460a-911a-3c6c778e107d", "metadata": {}, "outputs": [], "source": ["import base64\n", "import hashlib\n", "import os\n", "import requests"]}, {"cell_type": "code", "execution_count": 3, "id": "bd673dde-545c-4e3d-9b0f-8512a8ffcde0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Code Verifier: XHnqc-4GsLw5x7VP5-C628cWHzugcorYjyS5aXqKYCXuD_fV9owKq74-RnEOKZKxFoOVcF-s3DU0hA8N5LibF-raPxWguHcXkerJi--SqOokdSuBt9PTV8EhmSrAU8vSbtOHITKQtQXNZllr6UpM0Wz76gVI_tFnga8nUi0MglE\n", "Code Challenge: nRhHVbnY6VN7TOyQordzTPfKtB6A7WiozNUIw1q4lzw\n"]}], "source": ["\n", "\n", "def generate_code_verifier(length=128):\n", "    return base64.urlsafe_b64encode(os.urandom(length)).rstrip(b'=').decode('utf-8')\n", "\n", "def generate_code_challenge(code_verifier):\n", "    code_challenge = hashlib.sha256(code_verifier.encode('utf-8')).digest()\n", "    return base64.urlsafe_b64encode(code_challenge).rstrip(b'=').decode('utf-8')\n", "\n", "code_verifier = generate_code_verifier()\n", "code_challenge = generate_code_challenge(code_verifier)\n", "\n", "print(f\"Code Verifier: {code_verifier}\")\n", "print(f\"Code Challenge: {code_challenge}\")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "525cfa23-225f-49fd-a2bc-67bb19e68012", "metadata": {}, "outputs": [], "source": ["redirect_uri='https://d20000000jpf6eag.my.salesforce.com/home/<USER>'\n", "client_id='3MVG9WtWSKUDG.x6N_g.w4D.yzjTMKXgLFyB3CgOrZYqvP5MW.yI80efvT9Hg0aCTfzh1IkP_Yc47OXwS4Phg'\n", "# code_challenge=code_challenge"]}, {"cell_type": "code", "execution_count": 6, "id": "829ded03-4fc9-48a1-9a6b-01b35eccab77", "metadata": {}, "outputs": [], "source": ["url_splicing = f'https://login.salesforce.com/services/oauth2/authorize?response_type=code&client_id={client_id}&redirect_uri={redirect_uri}&code_challenge={code_challenge}&code_challenge_method=S256'"]}, {"cell_type": "code", "execution_count": 7, "id": "6a28a100-7853-466b-a844-2f59f5344923", "metadata": {}, "outputs": [{"data": {"text/plain": ["'https://login.salesforce.com/services/oauth2/authorize?response_type=code&client_id=3MVG9WtWSKUDG.x6N_g.w4D.yzjTMKXgLFyB3CgOrZYqvP5MW.yI80efvT9Hg0aCTfzh1IkP_Yc47OXwS4Phg&redirect_uri=https://d20000000jpf6eag.my.salesforce.com/home/<USER>'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["url_splicing"]}, {"cell_type": "code", "execution_count": 8, "id": "49b180b6-1343-4532-a10c-90db007da9ea", "metadata": {}, "outputs": [], "source": ["code = 'aPrxDtC6R2ZDACGz2zCCiXjDNioI28QT4t07MqPoYttag7PtqjS77A.OKwPmbQhPQ3.xzxdApg=='"]}, {"cell_type": "code", "execution_count": 9, "id": "ed0ab0e3-157d-44ba-8bcb-1f61f99649f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'access_token': '00D20000000JPf6!AQ4AQPryH_u9PljPjleHiy5vQUn0rGqrOu.mrrI03jUaSiveXgJUE2bPF3W1aIRVHcfCHswf1SchCRTIlo3bqeSQtuOmzJMq', 'refresh_token': '***************************************************************************************', 'signature': 'dz5Xmsn9YjSFhfs02EICrRC0CVpjDBfhHaE4tKIBiWM=', 'scope': 'cdp_ingest_api custom_permissions cdp_segment_api cdp_api content interaction_api cdp_identityresolution_api chatbot_api wave_api einstein_gpt_api cdp_calculated_insight_api web id api eclair_api pardot_api lightning visualforce cdp_query_api sfap_api openid cdp_profile_api refresh_token user_registration_api pwdless_login_api chatter_api forgot_password full', 'id_token': '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', 'instance_url': 'https://d20000000jpf6eag.my.salesforce.com', 'id': 'https://login.salesforce.com/id/00D20000000JPf6EAG/005IS000000YL4HYAW', 'token_type': 'Bearer', 'issued_at': '1716347488631', 'api_instance_url': 'https://api.salesforce.com'}\n"]}], "source": ["\n", "\n", "url = \"https://login.salesforce.com/services/oauth2/token\"\n", "\n", "payload = {\n", "    'grant_type': 'authorization_code',\n", "    'client_id': client_id,\n", "    'client_secret': '****************************************************************',\n", "    'code': code,\n", "    'redirect_uri': redirect_uri,\n", "    'code_verifier': code_verifier  # 如果使用了PKCE\n", "}\n", "headers = {\n", "    'Content-Type': 'application/x-www-form-urlencoded'\n", "}\n", "\n", "response = requests.post(url, headers=headers, data=payload)\n", "\n", "print(response.json())\n"]}, {"cell_type": "code", "execution_count": null, "id": "9ac8d53f-a24e-4496-acc6-00f3720a87c6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}