{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bffc08c3-8517-491e-b2ee-97d97a688962", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:38.905566Z", "iopub.status.busy": "2025-02-24T01:37:38.905488Z", "iopub.status.idle": "2025-02-24T01:37:38.950282Z", "shell.execute_reply": "2025-02-24T01:37:38.950009Z"}, "tags": []}, "outputs": [], "source": ["import os\n", "import datetime\n", "import configparser\n", "import requests\n", "import json\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "id": "bd75d82a-d8e0-453d-a822-aff1c3addcf0", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:38.952245Z", "iopub.status.busy": "2025-02-24T01:37:38.952137Z", "iopub.status.idle": "2025-02-24T01:37:39.249695Z", "shell.execute_reply": "2025-02-24T01:37:39.249335Z"}, "tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "b00c9ef0-1f6d-4e7a-bf35-fe84bd6a9211", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.251557Z", "iopub.status.busy": "2025-02-24T01:37:39.251405Z", "iopub.status.idle": "2025-02-24T01:37:39.254477Z", "shell.execute_reply": "2025-02-24T01:37:39.254243Z"}, "tags": []}, "outputs": [], "source": ["from acquire_mapping import Mapping"]}, {"cell_type": "code", "execution_count": 4, "id": "0257f3a9-3d08-44ca-bb34-94ad1e5ab353", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.255862Z", "iopub.status.busy": "2025-02-24T01:37:39.255764Z", "iopub.status.idle": "2025-02-24T01:37:39.257580Z", "shell.execute_reply": "2025-02-24T01:37:39.257287Z"}, "tags": []}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 5, "id": "ff3bfce2-8b17-4965-a068-f6be08355b51", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.258912Z", "iopub.status.busy": "2025-02-24T01:37:39.258825Z", "iopub.status.idle": "2025-02-24T01:37:39.283219Z", "shell.execute_reply": "2025-02-24T01:37:39.282957Z"}, "tags": []}, "outputs": [], "source": ["fiscaldate_mapping = Mapping.fiscaldate\n", "fiscaldate_mapping2 = fiscaldate_mapping.copy()[['Date','week_in_fiscal_quarter','fiscal_qtr_year_name']]\n", "fiscaldate_mapping2['week_in_fiscal_quarter'] = fiscaldate_mapping2['week_in_fiscal_quarter'].str.slice(1)\n", "fiscaldate_mapping2.rename(columns={'Date':'fiscal_dt'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "56e1853b-0edf-474f-9860-3cb40474ff23", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.284957Z", "iopub.status.busy": "2025-02-24T01:37:39.284854Z", "iopub.status.idle": "2025-02-24T01:37:39.286951Z", "shell.execute_reply": "2025-02-24T01:37:39.286677Z"}, "tags": []}, "outputs": [], "source": ["now = datetime.datetime.now().strftime('%Y-%m-%d')\n", "now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"]}, {"cell_type": "code", "execution_count": 7, "id": "2a5750fc-96d8-4091-afd5-d302a21c4e78", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.288452Z", "iopub.status.busy": "2025-02-24T01:37:39.288352Z", "iopub.status.idle": "2025-02-24T01:37:39.297893Z", "shell.execute_reply": "2025-02-24T01:37:39.297627Z"}, "tags": []}, "outputs": [], "source": ["fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_qtr_year_name'].unique().tolist()[0]\n", "week_in_fiscal_quarter = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['week_in_fiscal_quarter'].unique().tolist()[0][1:]\n", "fiscal_week_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_week_year_name'].unique().tolist()[0]\n", "# 如果是单个数字，则在前面添加 '0'\n", "if week_in_fiscal_quarter.isdigit() and len(week_in_fiscal_quarter) == 1:\n", "    week_in_fiscal_quarter2 = '0' + week_in_fiscal_quarter\n", "else:\n", "    week_in_fiscal_quarter2 = week_in_fiscal_quarter\n", "\n", "yqw_addzero = fiscal_qtr_year_name + 'W' + week_in_fiscal_quarter2\n", "next_fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date'] >= now]['fiscal_qtr_year_name'].unique()[1]"]}, {"cell_type": "code", "execution_count": 8, "id": "330d730e-de9d-418a-8730-8017b8c19ca1", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.299373Z", "iopub.status.busy": "2025-02-24T01:37:39.299283Z", "iopub.status.idle": "2025-02-24T01:37:39.302140Z", "shell.execute_reply": "2025-02-24T01:37:39.301822Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY25Q2', 'FY25Q1', 'FY24Q4', 'FY24Q3', 'FY24Q2', 'FY24Q1', 'FY23Q4', 'FY23Q3', 'FY23Q2', 'FY23Q1', 'FY22Q4', 'FY22Q3', 'FY22Q2']\n"]}], "source": ["def generate_fiscal_quarters(start_fiscal, end_fiscal):\n", "    start_year = int(start_fiscal[2:4])\n", "    start_qtr = int(start_fiscal[5])\n", "    end_year = int(end_fiscal[2:4])\n", "    end_qtr = int(end_fiscal[5])\n", "\n", "    fiscal_quarters = []\n", "    \n", "    current_year = start_year\n", "    current_qtr = start_qtr\n", "\n", "    while (current_year > end_year) or (current_year == end_year and current_qtr >= end_qtr):\n", "        fiscal_quarters.append(f\"FY{current_year:02d}Q{current_qtr}\")\n", "        if current_qtr == 1:\n", "            current_qtr = 4\n", "            current_year -= 1\n", "        else:\n", "            current_qtr -= 1\n", "\n", "    return fiscal_quarters\n", "\n", "# Example usage\n", "# fiscal_qtr_year_name = \"FY24Q4\"\n", "end_fiscal = \"FY22Q2\"\n", "\n", "fiscal_quarters_list = generate_fiscal_quarters(fiscal_qtr_year_name, end_fiscal)\n", "print(fiscal_quarters_list)"]}, {"cell_type": "code", "execution_count": 9, "id": "16949f44-14c1-4fe6-8777-8dee59ae5fe6", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.303827Z", "iopub.status.busy": "2025-02-24T01:37:39.303702Z", "iopub.status.idle": "2025-02-24T01:37:39.305820Z", "shell.execute_reply": "2025-02-24T01:37:39.305553Z"}, "tags": []}, "outputs": [], "source": ["# Generate fiscal_quarters_list_ta ae aem\n", "fiscal_quarters_list_ta = [f\"Account.{fq}_Top_Account__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_ae = [f\"Account.{fq}_AE__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_aem = [f\"Account.{fq}_AEM__c\" for fq in fiscal_quarters_list]"]}, {"cell_type": "code", "execution_count": 10, "id": "14a241dd-de3c-4f66-a9c7-1d22af5e0331", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.307478Z", "iopub.status.busy": "2025-02-24T01:37:39.307355Z", "iopub.status.idle": "2025-02-24T01:37:39.309085Z", "shell.execute_reply": "2025-02-24T01:37:39.308830Z"}, "tags": []}, "outputs": [], "source": ["# Combine all the lists into one\n", "all_fiscal_quarters_columns = fiscal_quarters_list_ta + fiscal_quarters_list_ae + fiscal_quarters_list_aem"]}, {"cell_type": "code", "execution_count": 11, "id": "fbebb1de-4648-4531-bec3-752b2ddc5ff7", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.310515Z", "iopub.status.busy": "2025-02-24T01:37:39.310429Z", "iopub.status.idle": "2025-02-24T01:37:39.315021Z", "shell.execute_reply": "2025-02-24T01:37:39.314803Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQ4AQEbrpRPMM_OG_3wvHrvxxkAytHpW1Vio440IuULyBSTlBEkE3IvDvhq3dL2y3p2froqLwLoxfQ0Gb1KqFD_MLhGCO_9X'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 12, "id": "bb4b086a-277c-415b-98fb-6ebb4ce33444", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.316321Z", "iopub.status.busy": "2025-02-24T01:37:39.316232Z", "iopub.status.idle": "2025-02-24T01:37:39.318820Z", "shell.execute_reply": "2025-02-24T01:37:39.318545Z"}, "tags": []}, "outputs": [], "source": ["# 250220 \n", "sql_statement = \"\"\"\n", "SELECT \n", "    Opportunity.use_case__c, \n", "    Opportunity.purchase_purpose__c,\n", "    Opportunity.AEapproved__c,\n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "\n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__r.Name, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "\n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "\n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c,\n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__r.Name, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Opportunity.Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.OPPORTUNITY_ID__c,\n", "    Opportunity.Name,\n", "    Opportunity.Probability,\n", "    Opportunity.JD_Appended__c,\n", "    Opportunity.Account.Mac_as_Choice__c,\n", "    Opportunity.Account.Top_Account_Deep_Dive__c,\n", "    Opportunity.Apple_Reseller__r.Name,\n", "    \n", "    Account.Enroll_Date__c,\n", "    Account.Acquisition_Group__c,\n", "    Account.NCR_Program__c,\n", "    Account.Reseller_for_acquisition__c,\n", "    Account.Status__c,\n", "    Opportunity.Business_Model__c,\n", "    \n", "    \n", "    (SELECT \n", "        Revenue__c, \n", "        FY__c, \n", "        ST_FYQuarter__c, \n", "        Product_Family__c,\n", "        Quarter__c, \n", "        Sell_Through_Week__c, \n", "        Oppty_Line_Item_ID__c, \n", "        Marketing_Part_Number_MPN__c,\n", "        FPH_Level_1_Name__c,\n", "        FPH_Level_2_Name__c,\n", "        FPH_Level_3_Name__c,\n", "        FPH_Level_4_Name__c,\n", "        FPH_Level_5_Name__c,\n", "        Quantity,\n", "        Line_of_business2__c\n", "     FROM OpportunityLineItems\n", ")\n", "FROM Opportunity\n", "WHERE  Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' \n", "and Opportunity_Reseller_Track__c = 'ENT' \n", "and Id IN (\n", "        SELECT OpportunityId \n", "        FROM OpportunityLineItem \n", "        WHERE FY__c > 'FY23' \n", "    )\n", "\n", "    \n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "ffcfbc02-4cc9-4b1b-8091-b4a2a1119e7e", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.320121Z", "iopub.status.busy": "2025-02-24T01:37:39.320034Z", "iopub.status.idle": "2025-02-24T01:37:39.321729Z", "shell.execute_reply": "2025-02-24T01:37:39.321491Z"}, "tags": []}, "outputs": [], "source": ["# Insert the fiscal quarters columns after 'Account.FY22Q1__c,'\n", "insert_point = sql_statement.find(\"Account.FY22Q1__c,\") + len(\"Account.FY22Q1__c,\")\n", "sql_statement = sql_statement[:insert_point] + \"\\n    \" + \",\\n    \".join(all_fiscal_quarters_columns) + \",\" + sql_statement[insert_point:]"]}, {"cell_type": "code", "execution_count": 14, "id": "9dadc1c4-2006-49f0-995e-18c65bffdc8b", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.322945Z", "iopub.status.busy": "2025-02-24T01:37:39.322869Z", "iopub.status.idle": "2025-02-24T01:37:39.324385Z", "shell.execute_reply": "2025-02-24T01:37:39.324141Z"}, "tags": []}, "outputs": [], "source": ["\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")"]}, {"cell_type": "code", "execution_count": 15, "id": "58f58cec-f923-4c69-a5d4-f840a6ae25c6", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.325615Z", "iopub.status.busy": "2025-02-24T01:37:39.325524Z", "iopub.status.idle": "2025-02-24T01:37:39.327640Z", "shell.execute_reply": "2025-02-24T01:37:39.327388Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["\"SELECT Opportunity.use_case__c, Opportunity.purchase_purpose__c, Opportunity.AEapproved__c, Account.Account_Group__c, Account.Group_Province__c, Account.Group_City__c, Account.Province__c, Account.City__c, Opportunity.Project_Type__c, Opportunity.Sold_To_ID__c, Opportunity.Opportunity_Reseller_Apple_ID__c, Opportunity.T2_Reseller__r.Name, Opportunity.Apple_HQ_ID__c, Opportunity.Opportunity_Reseller_Track__c, Opportunity.ESC_Store__c, Account.Sub_Segment__c, Account.Vertical_Industry__c, Account.zhanbaoshijian__c, Account.Mac_as_Choice_start_time__c, Account.FY21Q3_Large_Account__c, Account.FY21Q4_Large_Account__c, Account.FY22Q1__c, Account.FY25Q2_Top_Account__c, Account.FY25Q1_Top_Account__c, Account.FY24Q4_Top_Account__c, Account.FY24Q3_Top_Account__c, Account.FY24Q2_Top_Account__c, Account.FY24Q1_Top_Account__c, Account.FY23Q4_Top_Account__c, Account.FY23Q3_Top_Account__c, Account.FY23Q2_Top_Account__c, Account.FY23Q1_Top_Account__c, Account.FY22Q4_Top_Account__c, Account.FY22Q3_Top_Account__c, Account.FY22Q2_Top_Account__c, Account.FY25Q2_AE__c, Account.FY25Q1_AE__c, Account.FY24Q4_AE__c, Account.FY24Q3_AE__c, Account.FY24Q2_AE__c, Account.FY24Q1_AE__c, Account.FY23Q4_AE__c, Account.FY23Q3_AE__c, Account.FY23Q2_AE__c, Account.FY23Q1_AE__c, Account.FY22Q4_AE__c, Account.FY22Q3_AE__c, Account.FY22Q2_AE__c, Account.FY25Q2_AEM__c, Account.FY25Q1_AEM__c, Account.FY24Q4_AEM__c, Account.FY24Q3_AEM__c, Account.FY24Q2_AEM__c, Account.FY24Q1_AEM__c, Account.FY23Q4_AEM__c, Account.FY23Q3_AEM__c, Account.FY23Q2_AEM__c, Account.FY23Q1_AEM__c, Account.FY22Q4_AEM__c, Account.FY22Q3_AEM__c, Account.FY22Q2_AEM__c, Account.FY21Q2_AE__c, Account.FY21Q2_AEM__c, Account.FY21Q3_AE__c, Account.FY21Q3_AEM__c, Account.FY21Q4_AE__c, Account.FY21Q4_AEM__c, Account.FY22Q1_AE__c, Account.FY22Q1_AEM__c, Opportunity.Opportunity_Type__c, Account.Segment__c, Account.Large_Account__c, Account.Total_Mac_Demand__c, Account.PC_Install_Base__c, Account.FY22_Fcst__c, Account.FY23_Fcst__c, Opportunity.leasingornot__c, Account.Industry_Target_Account__c, Opportunity.Penetrated_Account__r.Name, Account.Source_Detail__c, Account.Sales_Region__c, Account.Name, Opportunity.Account.Owner.Name, Account.Account_ID__c, Opportunity.OPPORTUNITY_ID__c, Opportunity.Name, Opportunity.Probability, Opportunity.JD_Appended__c, Opportunity.Account.Mac_as_Choice__c, Opportunity.Account.Top_Account_Deep_Dive__c, Opportunity.Apple_Reseller__r.Name, Account.Enroll_Date__c, Account.Acquisition_Group__c, Account.NCR_Program__c, Account.Reseller_for_acquisition__c, Account.Status__c, Opportunity.Business_Model__c, (SELECT Revenue__c, FY__c, ST_FYQuarter__c, Product_Family__c, Quarter__c, Sell_Through_Week__c, Oppty_Line_Item_ID__c, Marketing_Part_Number_MPN__c, FPH_Level_1_Name__c, FPH_Level_2_Name__c, FPH_Level_3_Name__c, FPH_Level_4_Name__c, FPH_Level_5_Name__c, Quantity, Line_of_business2__c FROM OpportunityLineItems ) FROM Opportunity WHERE Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT' and Id IN ( SELECT OpportunityId FROM OpportunityLineItem WHERE FY__c > 'FY23' )\""]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_statement_single_line"]}, {"cell_type": "code", "execution_count": 16, "id": "cd52a5b1-40f5-4b20-8507-506dba08b0b4", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:37:39.329019Z", "iopub.status.busy": "2025-02-24T01:37:39.328927Z", "iopub.status.idle": "2025-02-24T01:39:02.998677Z", "shell.execute_reply": "2025-02-24T01:39:02.998336Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-247\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-477\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-705\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-859\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-983\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1087\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1222\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1385\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1519\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1669\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1862\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1863\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1865\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1867\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-1995\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-2154\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-2344\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-2536\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-2651\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-2845\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-3052\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-3212\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-3394\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-3538\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-3680\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-3735\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-3788\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-3927\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-4167\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-4352\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-4549\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-4731\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-4783\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-4808\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-5014\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-5200\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-5366\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-5517\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-5623\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-5762\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-5940\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-6094\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-6247\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-6394\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-6618\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-6806\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-6934\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-7149\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-7219\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-7386\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-7485\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-7545\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-7650\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-7824\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-7931\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-8097\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-8313\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-8513\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-8606\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-8697\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-8876\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-9110\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-9310\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-9490\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-9613\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-9678\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-9903\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10146\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10388\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10389\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10390\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10393\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10395\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10397\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10398\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10399\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10400\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10401\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10402\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10403\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10404\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10405\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10406\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10407\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10504\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10578\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10689\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10860\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-10986\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-11106\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-11247\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-11421\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-11660\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-11802\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-11927\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-12068\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-12273\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-12453\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-12709\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-12900\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-13079\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-13316\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-13595\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-13888\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-14103\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-14384\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-14701\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-15011\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-15313\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-15663\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-15866\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-16094\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3wKLVto3pTA2Q-16394\n"]}], "source": ["\n", "# for check keyong\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"00D20000000JPf6!AQ4AQDJU3_dhyicWbI4oh7DhiRhiAGomTWxU5SPWEc.RW2p1LaiZUlKCMx68rVWu1gSvVcFfbH3gEPUBrFLup0rQw576pYml\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "\n", "\n", "\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data_fordebug.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "6d536d51-272d-49a9-921c-02b4448e0421", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "26b2f484-9a4f-4d67-9df8-29e1f77db699", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:03.001000Z", "iopub.status.busy": "2025-02-24T01:39:03.000805Z", "iopub.status.idle": "2025-02-24T01:39:05.395999Z", "shell.execute_reply": "2025-02-24T01:39:05.395622Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data saved to salesforce_data.json\n"]}], "source": ["\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# !Optionally, save to a file\n", "with open('salesforce_data_fordebug.json', 'w') as f:\n", "    json.dump(all_records, f, indent=4)\n", "\n", "print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "28cd0594-e510-4874-9091-dc7cdb4dbe90", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:05.400312Z", "iopub.status.busy": "2025-02-24T01:39:05.400196Z", "iopub.status.idle": "2025-02-24T01:39:05.402172Z", "shell.execute_reply": "2025-02-24T01:39:05.401865Z"}, "tags": []}, "outputs": [], "source": ["\n", "# # 将 JSON 数据导出并美化\n", "# with open('ppl.json', 'w', encoding='utf-8') as json_file:\n", "#     json.dump(all_records, json_file, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": 19, "id": "5c640dcf-a3d9-41d5-bbc9-fa4cb4661e8c", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:05.406477Z", "iopub.status.busy": "2025-02-24T01:39:05.406368Z", "iopub.status.idle": "2025-02-24T01:39:05.410382Z", "shell.execute_reply": "2025-02-24T01:39:05.410115Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["['attributes',\n", " 'use_case__c',\n", " 'purchase_purpose__c',\n", " 'AEapproved__c',\n", " 'Account',\n", " 'Project_Type__c',\n", " 'Sold_To_ID__c',\n", " 'Opportunity_Reseller_Apple_ID__c',\n", " 'T2_Reseller__r',\n", " 'Apple_HQ_ID__c',\n", " 'Opportunity_Reseller_Track__c',\n", " 'ESC_Store__c',\n", " 'Opportunity_Type__c',\n", " 'leasingornot__c',\n", " 'Penetrated_Account__r',\n", " 'Opportunity_ID__c',\n", " 'Name',\n", " 'Probability',\n", " 'JD_Appended__c',\n", " 'Apple_Reseller__r',\n", " 'Business_Model__c']"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["meta = list(all_records[0].keys())\n", "# 移除 'OpportunityLineItems' 元素\n", "if 'OpportunityLineItems' in meta:\n", "    meta.remove('OpportunityLineItems')\n", "#     #241209kaifa\n", "# if 'Penetrated_Account__r' in meta:\n", "#     meta.remove('Penetrated_Account__r')\n", "      \n", "\n", "# 打印结果\n", "meta"]}, {"cell_type": "code", "execution_count": 20, "id": "c36d5345-d8a9-4302-ae28-b15d33dd1679", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:05.413426Z", "iopub.status.busy": "2025-02-24T01:39:05.413283Z", "iopub.status.idle": "2025-02-24T01:39:07.119947Z", "shell.execute_reply": "2025-02-24T01:39:07.119579Z"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>FPH_Level_1_Name__c</th>\n", "      <th>FPH_Level_2_Name__c</th>\n", "      <th>...</th>\n", "      <th>ESC_Store__c</th>\n", "      <th>Opportunity_Type__c</th>\n", "      <th>leasingornot__c</th>\n", "      <th>Penetrated_Account__r</th>\n", "      <th>Opportunity_ID__c</th>\n", "      <th>Name</th>\n", "      <th>Probability</th>\n", "      <th>JD_Appended__c</th>\n", "      <th>Apple_Reseller__r</th>\n", "      <th>Business_Model__c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6138.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q2</td>\n", "      <td>Mac Mini</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00kIS000008xYNo</td>\n", "      <td>MU9D3CH/A</td>\n", "      <td>Mac</td>\n", "      <td>Mac Mini</td>\n", "      <td>...</td>\n", "      <td>001IS000006XgTHYA0</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000050yyf</td>\n", "      <td>办公用品</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2419.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS0000031Owo</td>\n", "      <td>Z1AV</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>0066F00001GUWhX</td>\n", "      <td>FY24 Q4 CPU</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2056.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS000003154C</td>\n", "      <td>Z1AU</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>0066F00001GUWhX</td>\n", "      <td>FY24 Q4 CPU</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bws</td>\n", "      <td>Z1BD</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>0066F00001GUWhX</td>\n", "      <td>FY24 Q4 CPU</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bwt</td>\n", "      <td>Z1BB</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>0066F00001GUWhX</td>\n", "      <td>FY24 Q4 CPU</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93029</th>\n", "      <td>2352.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q1</td>\n", "      <td>iPad 10th Gen Wifi</td>\n", "      <td>Q1</td>\n", "      <td>W12</td>\n", "      <td>00kIS000008wJkX</td>\n", "      <td>MPQ83CH/A</td>\n", "      <td>iPad</td>\n", "      <td>iPad Entry</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS00000511GE</td>\n", "      <td>南通优护-JD-FY25Q1</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93030</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>0016F00004A7QLBQA3</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003giI0</td>\n", "      <td>办公用机</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93031</th>\n", "      <td>9676.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>0016F00004A7QLBQA3</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000046Ct4</td>\n", "      <td>办公用机</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93032</th>\n", "      <td>3661.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>Vision</td>\n", "      <td>Vision Pro</td>\n", "      <td>...</td>\n", "      <td>0016F000048n9GDQAY</td>\n", "      <td>Gifting</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000044cJh</td>\n", "      <td>零售</td>\n", "      <td>25.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>Channel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93033</th>\n", "      <td>2403.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>Mac</td>\n", "      <td>iMac</td>\n", "      <td>...</td>\n", "      <td>0016F00004Fk41zQAB</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003gqRq</td>\n", "      <td>办公自用</td>\n", "      <td>25.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93034 rows × 38 columns</p>\n", "</div>"], "text/plain": ["       Revenue__c FY__c ST_FYQuarter__c   Product_Family__c Quarter__c  \\\n", "0          6138.0  FY25          FY25Q2            Mac Mini         Q2   \n", "1          2419.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "2          2056.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "3          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "4          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "...           ...   ...             ...                 ...        ...   \n", "93029      2352.0  FY25          FY25Q1  iPad 10th Gen Wifi         Q1   \n", "93030      1291.0  FY24          FY24Q3         MacBook Air         Q3   \n", "93031      9676.0  FY24          FY24Q4         MacBook Pro         Q4   \n", "93032      3661.0  FY24          FY24Q4          Vision Pro         Q4   \n", "93033      2403.0  FY24          FY24Q4                iMac         Q4   \n", "\n", "      Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                      W08       00kIS000008xYNo                    MU9D3CH/A   \n", "1                      W11       00kIS0000031Owo                         Z1AV   \n", "2                      W11       00kIS000003154C                         Z1AU   \n", "3                      W04       00kIS0000078Bws                         Z1BD   \n", "4                      W04       00kIS0000078Bwt                         Z1BB   \n", "...                    ...                   ...                          ...   \n", "93029                  W12       00kIS000008wJkX                    MPQ83CH/A   \n", "93030                  W09       00kIS0000074ual                         Z1BP   \n", "93031                  W07       00kIS000007lKUp                         Z1AY   \n", "93032                  W02       00kIS0000077qYX                    MW8X3CH/A   \n", "93033                  W12       00kIS00000757Un                         Z19Q   \n", "\n", "      FPH_Level_1_Name__c FPH_Level_2_Name__c  ...        ESC_Store__c  \\\n", "0                     Mac            Mac Mini  ...  001IS000006XgTHYA0   \n", "1                     Mac         MacBook Pro  ...                None   \n", "2                     Mac         MacBook Pro  ...                None   \n", "3                     Mac         MacBook Air  ...                None   \n", "4                     Mac         MacBook Air  ...                None   \n", "...                   ...                 ...  ...                 ...   \n", "93029                iPad          iPad Entry  ...                None   \n", "93030                 Mac         MacBook Air  ...  0016F00004A7QLBQA3   \n", "93031                 Mac         MacBook Pro  ...  0016F00004A7QLBQA3   \n", "93032              Vision          Vision Pro  ...  0016F000048n9GDQAY   \n", "93033                 Mac                iMac  ...  0016F00004Fk41zQAB   \n", "\n", "      Opportunity_Type__c leasingornot__c  Penetrated_Account__r  \\\n", "0                Solution              No                   None   \n", "1              Office Use              No                   None   \n", "2              Office Use              No                   None   \n", "3              Office Use              No                   None   \n", "4              Office Use              No                   None   \n", "...                   ...             ...                    ...   \n", "93029            Solution              No                   None   \n", "93030            Solution              No                   None   \n", "93031            Solution              No                   None   \n", "93032             Gifting              No                   None   \n", "93033            Solution              No                   None   \n", "\n", "      Opportunity_ID__c            Name Probability JD_Appended__c  \\\n", "0       006IS0000050yyf            办公用品        75.0          False   \n", "1       0066F00001GUWhX     FY24 Q4 CPU       100.0          False   \n", "2       0066F00001GUWhX     FY24 Q4 CPU       100.0          False   \n", "3       0066F00001GUWhX     FY24 Q4 CPU       100.0          False   \n", "4       0066F00001GUWhX     FY24 Q4 CPU       100.0          False   \n", "...                 ...             ...         ...            ...   \n", "93029   006IS00000511GE  南通优护-JD-FY25Q1        75.0          False   \n", "93030   006IS000003giI0            办公用机       100.0          False   \n", "93031   006IS0000046Ct4            办公用机       100.0          False   \n", "93032   006IS0000044cJh              零售        25.0          False   \n", "93033   006IS000003gqRq            办公自用        25.0          False   \n", "\n", "                                       Apple_Reseller__r Business_Model__c  \n", "0      {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "1      {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "2      {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "3      {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "4      {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "...                                                  ...               ...  \n", "93029  {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "93030  {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "93031  {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "93032  {'attributes': {'type': 'User', 'url': '/servi...           Channel  \n", "93033  {'attributes': {'type': 'User', 'url': '/servi...              None  \n", "\n", "[93034 rows x 38 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 将嵌套的 OpportunityLineItems 展开到 DataFrame\n", "df_opportunity_line_items = pd.json_normalize(\n", "    all_records,\n", "    record_path=['OpportunityLineItems', 'records'],\n", "    meta=meta\n", "    \n", ")\n", "\n", "# 打印或保存 DataFrame\n", "df_opportunity_line_items\n", "# df_opportunity_line_items.to_csv('salesforce_data.csv', index=False)  # 可选：保存到 CSV 文件"]}, {"cell_type": "code", "execution_count": 21, "id": "895932f2-2be4-4168-ba88-229794eed97e", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:07.121627Z", "iopub.status.busy": "2025-02-24T01:39:07.121510Z", "iopub.status.idle": "2025-02-24T01:39:09.089257Z", "shell.execute_reply": "2025-02-24T01:39:09.088971Z"}, "scrolled": true, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>FPH_Level_1_Name__c</th>\n", "      <th>FPH_Level_2_Name__c</th>\n", "      <th>...</th>\n", "      <th>Enroll_Date__c</th>\n", "      <th>Acquisition_Group__c</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6138.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q2</td>\n", "      <td>Mac Mini</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00kIS000008xYNo</td>\n", "      <td>MU9D3CH/A</td>\n", "      <td>Mac</td>\n", "      <td>Mac Mini</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2419.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS0000031Owo</td>\n", "      <td>Z1AV</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2056.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS000003154C</td>\n", "      <td>Z1AU</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bws</td>\n", "      <td>Z1BD</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bwt</td>\n", "      <td>Z1BB</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93029</th>\n", "      <td>2352.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q1</td>\n", "      <td>iPad 10th Gen Wifi</td>\n", "      <td>Q1</td>\n", "      <td>W12</td>\n", "      <td>00kIS000008wJkX</td>\n", "      <td>MPQ83CH/A</td>\n", "      <td>iPad</td>\n", "      <td>iPad Entry</td>\n", "      <td>...</td>\n", "      <td>FY25Q2</td>\n", "      <td>Group4</td>\n", "      <td>True</td>\n", "      <td>云走廊（南京）软件信息有限公司</td>\n", "      <td>Released by Reseller</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93030</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93031</th>\n", "      <td>9676.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93032</th>\n", "      <td>3661.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>Vision</td>\n", "      <td>Vision Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93033</th>\n", "      <td>2403.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>Mac</td>\n", "      <td>iMac</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93034 rows × 119 columns</p>\n", "</div>"], "text/plain": ["       Revenue__c FY__c ST_FYQuarter__c   Product_Family__c Quarter__c  \\\n", "0          6138.0  FY25          FY25Q2            Mac Mini         Q2   \n", "1          2419.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "2          2056.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "3          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "4          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "...           ...   ...             ...                 ...        ...   \n", "93029      2352.0  FY25          FY25Q1  iPad 10th Gen Wifi         Q1   \n", "93030      1291.0  FY24          FY24Q3         MacBook Air         Q3   \n", "93031      9676.0  FY24          FY24Q4         MacBook Pro         Q4   \n", "93032      3661.0  FY24          FY24Q4          Vision Pro         Q4   \n", "93033      2403.0  FY24          FY24Q4                iMac         Q4   \n", "\n", "      Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                      W08       00kIS000008xYNo                    MU9D3CH/A   \n", "1                      W11       00kIS0000031Owo                         Z1AV   \n", "2                      W11       00kIS000003154C                         Z1AU   \n", "3                      W04       00kIS0000078Bws                         Z1BD   \n", "4                      W04       00kIS0000078Bwt                         Z1BB   \n", "...                    ...                   ...                          ...   \n", "93029                  W12       00kIS000008wJkX                    MPQ83CH/A   \n", "93030                  W09       00kIS0000074ual                         Z1BP   \n", "93031                  W07       00kIS000007lKUp                         Z1AY   \n", "93032                  W02       00kIS0000077qYX                    MW8X3CH/A   \n", "93033                  W12       00kIS00000757Un                         Z19Q   \n", "\n", "      FPH_Level_1_Name__c FPH_Level_2_Name__c  ... Enroll_Date__c  \\\n", "0                     Mac            Mac Mini  ...           None   \n", "1                     Mac         MacBook Pro  ...           None   \n", "2                     Mac         MacBook Pro  ...           None   \n", "3                     Mac         MacBook Air  ...           None   \n", "4                     Mac         MacBook Air  ...           None   \n", "...                   ...                 ...  ...            ...   \n", "93029                iPad          iPad Entry  ...         FY25Q2   \n", "93030                 Mac         MacBook Air  ...           None   \n", "93031                 Mac         MacBook Pro  ...           None   \n", "93032              Vision          Vision Pro  ...           None   \n", "93033                 Mac                iMac  ...           None   \n", "\n", "      Acquisition_Group__c NCR_Program__c  Reseller_for_acquisition__c  \\\n", "0                     None          False                         None   \n", "1                     None          False                         None   \n", "2                     None          False                         None   \n", "3                     None          False                         None   \n", "4                     None          False                         None   \n", "...                    ...            ...                          ...   \n", "93029               Group4           True              云走廊（南京）软件信息有限公司   \n", "93030                 None          False                         None   \n", "93031                 None          False                         None   \n", "93032                 None          False                         None   \n", "93033                 None          False                         None   \n", "\n", "                  Status__c attributes.type_account  \\\n", "0                      None                 Account   \n", "1                      None                 Account   \n", "2                      None                 Account   \n", "3                      None                 Account   \n", "4                      None                 Account   \n", "...                     ...                     ...   \n", "93029  Released by Reseller                 Account   \n", "93030                  None                 Account   \n", "93031                  None                 Account   \n", "93032                  None                 Account   \n", "93033                  None                 Account   \n", "\n", "                                  attributes.url_account  \\\n", "0      /services/data/v59.0/sobjects/Account/001IS000...   \n", "1      /services/data/v59.0/sobjects/Account/0016F000...   \n", "2      /services/data/v59.0/sobjects/Account/0016F000...   \n", "3      /services/data/v59.0/sobjects/Account/0016F000...   \n", "4      /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                  ...   \n", "93029  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93030  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93031  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93032  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93033  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "      Owner.attributes.type  \\\n", "0                      User   \n", "1                      User   \n", "2                      User   \n", "3                      User   \n", "4                      User   \n", "...                     ...   \n", "93029                  User   \n", "93030                  User   \n", "93031                  User   \n", "93032                  User   \n", "93033                  User   \n", "\n", "                                    Owner.attributes.url     Owner.Name  \n", "0      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "1      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "2      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "3      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "4      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "...                                                  ...            ...  \n", "93029  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "93030  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "93031  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "93032  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "93033  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "\n", "[93034 rows x 119 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Account'])\n", "\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Account']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_account'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items"]}, {"cell_type": "code", "execution_count": null, "id": "1222d470-72d6-4963-838c-712e49dfd580", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "id": "b01d3147-de43-43ea-b000-89df8296b3a4", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:09.091003Z", "iopub.status.busy": "2025-02-24T01:39:09.090859Z", "iopub.status.idle": "2025-02-24T01:39:09.566885Z", "shell.execute_reply": "2025-02-24T01:39:09.566533Z"}, "scrolled": true, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>FPH_Level_1_Name__c</th>\n", "      <th>FPH_Level_2_Name__c</th>\n", "      <th>...</th>\n", "      <th>Acquisition_Group__c</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6138.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q2</td>\n", "      <td>Mac Mini</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00kIS000008xYNo</td>\n", "      <td>MU9D3CH/A</td>\n", "      <td>Mac</td>\n", "      <td>Mac Mini</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2419.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS0000031Owo</td>\n", "      <td>Z1AV</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2056.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS000003154C</td>\n", "      <td>Z1AU</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bws</td>\n", "      <td>Z1BD</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bwt</td>\n", "      <td>Z1BB</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93029</th>\n", "      <td>2352.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q1</td>\n", "      <td>iPad 10th Gen Wifi</td>\n", "      <td>Q1</td>\n", "      <td>W12</td>\n", "      <td>00kIS000008wJkX</td>\n", "      <td>MPQ83CH/A</td>\n", "      <td>iPad</td>\n", "      <td>iPad Entry</td>\n", "      <td>...</td>\n", "      <td>Group4</td>\n", "      <td>True</td>\n", "      <td>云走廊（南京）软件信息有限公司</td>\n", "      <td>Released by Reseller</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93030</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93031</th>\n", "      <td>9676.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93032</th>\n", "      <td>3661.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>Vision</td>\n", "      <td>Vision Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93033</th>\n", "      <td>2403.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>Mac</td>\n", "      <td>iMac</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93034 rows × 119 columns</p>\n", "</div>"], "text/plain": ["       Revenue__c FY__c ST_FYQuarter__c   Product_Family__c Quarter__c  \\\n", "0          6138.0  FY25          FY25Q2            Mac Mini         Q2   \n", "1          2419.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "2          2056.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "3          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "4          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "...           ...   ...             ...                 ...        ...   \n", "93029      2352.0  FY25          FY25Q1  iPad 10th Gen Wifi         Q1   \n", "93030      1291.0  FY24          FY24Q3         MacBook Air         Q3   \n", "93031      9676.0  FY24          FY24Q4         MacBook Pro         Q4   \n", "93032      3661.0  FY24          FY24Q4          Vision Pro         Q4   \n", "93033      2403.0  FY24          FY24Q4                iMac         Q4   \n", "\n", "      Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                      W08       00kIS000008xYNo                    MU9D3CH/A   \n", "1                      W11       00kIS0000031Owo                         Z1AV   \n", "2                      W11       00kIS000003154C                         Z1AU   \n", "3                      W04       00kIS0000078Bws                         Z1BD   \n", "4                      W04       00kIS0000078Bwt                         Z1BB   \n", "...                    ...                   ...                          ...   \n", "93029                  W12       00kIS000008wJkX                    MPQ83CH/A   \n", "93030                  W09       00kIS0000074ual                         Z1BP   \n", "93031                  W07       00kIS000007lKUp                         Z1AY   \n", "93032                  W02       00kIS0000077qYX                    MW8X3CH/A   \n", "93033                  W12       00kIS00000757Un                         Z19Q   \n", "\n", "      FPH_Level_1_Name__c FPH_Level_2_Name__c  ... Acquisition_Group__c  \\\n", "0                     Mac            Mac Mini  ...                 None   \n", "1                     Mac         MacBook Pro  ...                 None   \n", "2                     Mac         MacBook Pro  ...                 None   \n", "3                     Mac         MacBook Air  ...                 None   \n", "4                     Mac         MacBook Air  ...                 None   \n", "...                   ...                 ...  ...                  ...   \n", "93029                iPad          iPad Entry  ...               Group4   \n", "93030                 Mac         MacBook Air  ...                 None   \n", "93031                 Mac         MacBook Pro  ...                 None   \n", "93032              Vision          Vision Pro  ...                 None   \n", "93033                 Mac                iMac  ...                 None   \n", "\n", "      NCR_Program__c Reseller_for_acquisition__c             Status__c  \\\n", "0              False                        None                  None   \n", "1              False                        None                  None   \n", "2              False                        None                  None   \n", "3              False                        None                  None   \n", "4              False                        None                  None   \n", "...              ...                         ...                   ...   \n", "93029           True             云走廊（南京）软件信息有限公司  Released by Reseller   \n", "93030          False                        None                  None   \n", "93031          False                        None                  None   \n", "93032          False                        None                  None   \n", "93033          False                        None                  None   \n", "\n", "      attributes.type_account  \\\n", "0                     Account   \n", "1                     Account   \n", "2                     Account   \n", "3                     Account   \n", "4                     Account   \n", "...                       ...   \n", "93029                 Account   \n", "93030                 Account   \n", "93031                 Account   \n", "93032                 Account   \n", "93033                 Account   \n", "\n", "                                  attributes.url_account  \\\n", "0      /services/data/v59.0/sobjects/Account/001IS000...   \n", "1      /services/data/v59.0/sobjects/Account/0016F000...   \n", "2      /services/data/v59.0/sobjects/Account/0016F000...   \n", "3      /services/data/v59.0/sobjects/Account/0016F000...   \n", "4      /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                  ...   \n", "93029  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93030  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93031  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93032  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93033  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "      Owner.attributes.type  \\\n", "0                      User   \n", "1                      User   \n", "2                      User   \n", "3                      User   \n", "4                      User   \n", "...                     ...   \n", "93029                  User   \n", "93030                  User   \n", "93031                  User   \n", "93032                  User   \n", "93033                  User   \n", "\n", "                                    Owner.attributes.url     Owner.Name  \\\n", "0      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "1      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "2      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "3      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "4      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "...                                                  ...            ...   \n", "93029  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93030  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93031  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93032  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93033  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "        Name_disti/t1  \n", "0      伟仕佳杰（重庆）科技有限公司  \n", "1      伟仕佳杰（重庆）科技有限公司  \n", "2      伟仕佳杰（重庆）科技有限公司  \n", "3      伟仕佳杰（重庆）科技有限公司  \n", "4      伟仕佳杰（重庆）科技有限公司  \n", "...               ...  \n", "93029   海南新博航数码科技有限公司  \n", "93030  伟仕佳杰（重庆）科技有限公司  \n", "93031  伟仕佳杰（重庆）科技有限公司  \n", "93032  伟仕佳杰（重庆）科技有限公司  \n", "93033  伟仕佳杰（重庆）科技有限公司  \n", "\n", "[93034 rows x 119 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Apple_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'Name_disti/t1'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Apple_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_disti/t1'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 23, "id": "ac201719-7833-408c-9d30-f9416455cc31", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:09.568582Z", "iopub.status.busy": "2025-02-24T01:39:09.568456Z", "iopub.status.idle": "2025-02-24T01:39:09.958639Z", "shell.execute_reply": "2025-02-24T01:39:09.958317Z"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>FPH_Level_1_Name__c</th>\n", "      <th>FPH_Level_2_Name__c</th>\n", "      <th>...</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "      <th>T2_Reseller</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6138.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q2</td>\n", "      <td>Mac Mini</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00kIS000008xYNo</td>\n", "      <td>MU9D3CH/A</td>\n", "      <td>Mac</td>\n", "      <td>Mac Mini</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>包头市智讯商贸有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2419.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS0000031Owo</td>\n", "      <td>Z1AV</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>厦门众恺信息科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2056.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS000003154C</td>\n", "      <td>Z1AU</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>厦门众恺信息科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bws</td>\n", "      <td>Z1BD</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>厦门众恺信息科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bwt</td>\n", "      <td>Z1BB</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>厦门众恺信息科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93029</th>\n", "      <td>2352.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q1</td>\n", "      <td>iPad 10th Gen Wifi</td>\n", "      <td>Q1</td>\n", "      <td>W12</td>\n", "      <td>00kIS000008wJkX</td>\n", "      <td>MPQ83CH/A</td>\n", "      <td>iPad</td>\n", "      <td>iPad Entry</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>云走廊（南京）软件信息有限公司</td>\n", "      <td>Released by Reseller</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93030</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93031</th>\n", "      <td>9676.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93032</th>\n", "      <td>3661.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>Vision</td>\n", "      <td>Vision Pro</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93033</th>\n", "      <td>2403.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>Mac</td>\n", "      <td>iMac</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93034 rows × 119 columns</p>\n", "</div>"], "text/plain": ["       Revenue__c FY__c ST_FYQuarter__c   Product_Family__c Quarter__c  \\\n", "0          6138.0  FY25          FY25Q2            Mac Mini         Q2   \n", "1          2419.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "2          2056.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "3          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "4          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "...           ...   ...             ...                 ...        ...   \n", "93029      2352.0  FY25          FY25Q1  iPad 10th Gen Wifi         Q1   \n", "93030      1291.0  FY24          FY24Q3         MacBook Air         Q3   \n", "93031      9676.0  FY24          FY24Q4         MacBook Pro         Q4   \n", "93032      3661.0  FY24          FY24Q4          Vision Pro         Q4   \n", "93033      2403.0  FY24          FY24Q4                iMac         Q4   \n", "\n", "      Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                      W08       00kIS000008xYNo                    MU9D3CH/A   \n", "1                      W11       00kIS0000031Owo                         Z1AV   \n", "2                      W11       00kIS000003154C                         Z1AU   \n", "3                      W04       00kIS0000078Bws                         Z1BD   \n", "4                      W04       00kIS0000078Bwt                         Z1BB   \n", "...                    ...                   ...                          ...   \n", "93029                  W12       00kIS000008wJkX                    MPQ83CH/A   \n", "93030                  W09       00kIS0000074ual                         Z1BP   \n", "93031                  W07       00kIS000007lKUp                         Z1AY   \n", "93032                  W02       00kIS0000077qYX                    MW8X3CH/A   \n", "93033                  W12       00kIS00000757Un                         Z19Q   \n", "\n", "      FPH_Level_1_Name__c FPH_Level_2_Name__c  ... NCR_Program__c  \\\n", "0                     Mac            Mac Mini  ...          False   \n", "1                     Mac         MacBook Pro  ...          False   \n", "2                     Mac         MacBook Pro  ...          False   \n", "3                     Mac         MacBook Air  ...          False   \n", "4                     Mac         MacBook Air  ...          False   \n", "...                   ...                 ...  ...            ...   \n", "93029                iPad          iPad Entry  ...           True   \n", "93030                 Mac         MacBook Air  ...          False   \n", "93031                 Mac         MacBook Pro  ...          False   \n", "93032              Vision          Vision Pro  ...          False   \n", "93033                 Mac                iMac  ...          False   \n", "\n", "      Reseller_for_acquisition__c             Status__c  \\\n", "0                            None                  None   \n", "1                            None                  None   \n", "2                            None                  None   \n", "3                            None                  None   \n", "4                            None                  None   \n", "...                           ...                   ...   \n", "93029             云走廊（南京）软件信息有限公司  Released by Reseller   \n", "93030                        None                  None   \n", "93031                        None                  None   \n", "93032                        None                  None   \n", "93033                        None                  None   \n", "\n", "       attributes.type_account  \\\n", "0                      Account   \n", "1                      Account   \n", "2                      Account   \n", "3                      Account   \n", "4                      Account   \n", "...                        ...   \n", "93029                  Account   \n", "93030                  Account   \n", "93031                  Account   \n", "93032                  Account   \n", "93033                  Account   \n", "\n", "                                  attributes.url_account  \\\n", "0      /services/data/v59.0/sobjects/Account/001IS000...   \n", "1      /services/data/v59.0/sobjects/Account/0016F000...   \n", "2      /services/data/v59.0/sobjects/Account/0016F000...   \n", "3      /services/data/v59.0/sobjects/Account/0016F000...   \n", "4      /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                  ...   \n", "93029  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93030  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93031  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93032  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93033  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "      Owner.attributes.type  \\\n", "0                      User   \n", "1                      User   \n", "2                      User   \n", "3                      User   \n", "4                      User   \n", "...                     ...   \n", "93029                  User   \n", "93030                  User   \n", "93031                  User   \n", "93032                  User   \n", "93033                  User   \n", "\n", "                                    Owner.attributes.url     Owner.Name  \\\n", "0      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "1      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "2      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "3      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "4      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "...                                                  ...            ...   \n", "93029  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93030  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93031  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93032  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93033  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "        Name_disti/t1   T2_Reseller  \n", "0      伟仕佳杰（重庆）科技有限公司   包头市智讯商贸有限公司  \n", "1      伟仕佳杰（重庆）科技有限公司  厦门众恺信息科技有限公司  \n", "2      伟仕佳杰（重庆）科技有限公司  厦门众恺信息科技有限公司  \n", "3      伟仕佳杰（重庆）科技有限公司  厦门众恺信息科技有限公司  \n", "4      伟仕佳杰（重庆）科技有限公司  厦门众恺信息科技有限公司  \n", "...               ...           ...  \n", "93029   海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "93030  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司  \n", "93031  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司  \n", "93032  伟仕佳杰（重庆）科技有限公司    山东亿达数码有限公司  \n", "93033  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司  \n", "\n", "[93034 rows x 119 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 T2_Reseller__r 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['T2_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'T2_Reseller'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['T2_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_T2_Reseller'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 24, "id": "574f3a6f-a9d8-4311-82c5-d66e5ae5cf03", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:09.960221Z", "iopub.status.busy": "2025-02-24T01:39:09.960106Z", "iopub.status.idle": "2025-02-24T01:39:10.270489Z", "shell.execute_reply": "2025-02-24T01:39:10.270131Z"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>FPH_Level_1_Name__c</th>\n", "      <th>FPH_Level_2_Name__c</th>\n", "      <th>...</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "      <th>T2_Reseller</th>\n", "      <th>Penetrated_Account</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6138.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q2</td>\n", "      <td>Mac Mini</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00kIS000008xYNo</td>\n", "      <td>MU9D3CH/A</td>\n", "      <td>Mac</td>\n", "      <td>Mac Mini</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>包头市智讯商贸有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2419.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS0000031Owo</td>\n", "      <td>Z1AV</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>厦门众恺信息科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2056.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W11</td>\n", "      <td>00kIS000003154C</td>\n", "      <td>Z1AU</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>厦门众恺信息科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bws</td>\n", "      <td>Z1BD</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>厦门众恺信息科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q4</td>\n", "      <td>W04</td>\n", "      <td>00kIS0000078Bwt</td>\n", "      <td>Z1BB</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>厦门众恺信息科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93029</th>\n", "      <td>2352.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q1</td>\n", "      <td>iPad 10th Gen Wifi</td>\n", "      <td>Q1</td>\n", "      <td>W12</td>\n", "      <td>00kIS000008wJkX</td>\n", "      <td>MPQ83CH/A</td>\n", "      <td>iPad</td>\n", "      <td>iPad Entry</td>\n", "      <td>...</td>\n", "      <td>云走廊（南京）软件信息有限公司</td>\n", "      <td>Released by Reseller</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93030</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Air</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93031</th>\n", "      <td>9676.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>Mac</td>\n", "      <td>MacBook Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93032</th>\n", "      <td>3661.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>Vision</td>\n", "      <td>Vision Pro</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93033</th>\n", "      <td>2403.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>Mac</td>\n", "      <td>iMac</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93034 rows × 119 columns</p>\n", "</div>"], "text/plain": ["       Revenue__c FY__c ST_FYQuarter__c   Product_Family__c Quarter__c  \\\n", "0          6138.0  FY25          FY25Q2            Mac Mini         Q2   \n", "1          2419.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "2          2056.0  FY24          FY24Q1         MacBook Pro         Q1   \n", "3          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "4          1291.0  FY24          FY24Q4         MacBook Air         Q4   \n", "...           ...   ...             ...                 ...        ...   \n", "93029      2352.0  FY25          FY25Q1  iPad 10th Gen Wifi         Q1   \n", "93030      1291.0  FY24          FY24Q3         MacBook Air         Q3   \n", "93031      9676.0  FY24          FY24Q4         MacBook Pro         Q4   \n", "93032      3661.0  FY24          FY24Q4          Vision Pro         Q4   \n", "93033      2403.0  FY24          FY24Q4                iMac         Q4   \n", "\n", "      Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                      W08       00kIS000008xYNo                    MU9D3CH/A   \n", "1                      W11       00kIS0000031Owo                         Z1AV   \n", "2                      W11       00kIS000003154C                         Z1AU   \n", "3                      W04       00kIS0000078Bws                         Z1BD   \n", "4                      W04       00kIS0000078Bwt                         Z1BB   \n", "...                    ...                   ...                          ...   \n", "93029                  W12       00kIS000008wJkX                    MPQ83CH/A   \n", "93030                  W09       00kIS0000074ual                         Z1BP   \n", "93031                  W07       00kIS000007lKUp                         Z1AY   \n", "93032                  W02       00kIS0000077qYX                    MW8X3CH/A   \n", "93033                  W12       00kIS00000757Un                         Z19Q   \n", "\n", "      FPH_Level_1_Name__c FPH_Level_2_Name__c  ...  \\\n", "0                     Mac            Mac Mini  ...   \n", "1                     Mac         MacBook Pro  ...   \n", "2                     Mac         MacBook Pro  ...   \n", "3                     Mac         MacBook Air  ...   \n", "4                     Mac         MacBook Air  ...   \n", "...                   ...                 ...  ...   \n", "93029                iPad          iPad Entry  ...   \n", "93030                 Mac         MacBook Air  ...   \n", "93031                 Mac         MacBook Pro  ...   \n", "93032              Vision          Vision Pro  ...   \n", "93033                 Mac                iMac  ...   \n", "\n", "      Reseller_for_acquisition__c             Status__c  \\\n", "0                            None                  None   \n", "1                            None                  None   \n", "2                            None                  None   \n", "3                            None                  None   \n", "4                            None                  None   \n", "...                           ...                   ...   \n", "93029             云走廊（南京）软件信息有限公司  Released by Reseller   \n", "93030                        None                  None   \n", "93031                        None                  None   \n", "93032                        None                  None   \n", "93033                        None                  None   \n", "\n", "      attributes.type_account  \\\n", "0                     Account   \n", "1                     Account   \n", "2                     Account   \n", "3                     Account   \n", "4                     Account   \n", "...                       ...   \n", "93029                 Account   \n", "93030                 Account   \n", "93031                 Account   \n", "93032                 Account   \n", "93033                 Account   \n", "\n", "                                  attributes.url_account  \\\n", "0      /services/data/v59.0/sobjects/Account/001IS000...   \n", "1      /services/data/v59.0/sobjects/Account/0016F000...   \n", "2      /services/data/v59.0/sobjects/Account/0016F000...   \n", "3      /services/data/v59.0/sobjects/Account/0016F000...   \n", "4      /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                  ...   \n", "93029  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93030  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93031  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93032  /services/data/v59.0/sobjects/Account/001IS000...   \n", "93033  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "      Owner.attributes.type  \\\n", "0                      User   \n", "1                      User   \n", "2                      User   \n", "3                      User   \n", "4                      User   \n", "...                     ...   \n", "93029                  User   \n", "93030                  User   \n", "93031                  User   \n", "93032                  User   \n", "93033                  User   \n", "\n", "                                    Owner.attributes.url     Owner.Name  \\\n", "0      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "1      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "2      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "3      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "4      /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "...                                                  ...            ...   \n", "93029  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93030  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93031  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93032  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "93033  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "        Name_disti/t1   T2_Reseller Penetrated_Account  \n", "0      伟仕佳杰（重庆）科技有限公司   包头市智讯商贸有限公司                NaN  \n", "1      伟仕佳杰（重庆）科技有限公司  厦门众恺信息科技有限公司                NaN  \n", "2      伟仕佳杰（重庆）科技有限公司  厦门众恺信息科技有限公司                NaN  \n", "3      伟仕佳杰（重庆）科技有限公司  厦门众恺信息科技有限公司                NaN  \n", "4      伟仕佳杰（重庆）科技有限公司  厦门众恺信息科技有限公司                NaN  \n", "...               ...           ...                ...  \n", "93029   海南新博航数码科技有限公司  北京京东耀弘贸易有限公司                NaN  \n", "93030  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司                NaN  \n", "93031  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司                NaN  \n", "93032  伟仕佳杰（重庆）科技有限公司    山东亿达数码有限公司                NaN  \n", "93033  伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司                NaN  \n", "\n", "[93034 rows x 119 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# 241209添加penetrated account 修正\n", "# 将 Penetrated_Account__r 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Penetrated_Account__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'Penetrated_Account'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Penetrated_Account__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_Penetrated_Account'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 25, "id": "dbf7958d-5833-4b75-bb44-bb99fff2169d", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.272142Z", "iopub.status.busy": "2025-02-24T01:39:10.272023Z", "iopub.status.idle": "2025-02-24T01:39:10.274457Z", "shell.execute_reply": "2025-02-24T01:39:10.274223Z"}, "tags": []}, "outputs": [], "source": ["# 删除列名中的 '__c' 并替换 '_' 为 ' '\n", "df_opportunity_line_items.columns = df_opportunity_line_items.columns.str.replace('__c$', '', regex=True).str.replace('_', ' ')"]}, {"cell_type": "code", "execution_count": 26, "id": "6548de6a-ff70-4439-ab02-d40aaf5e5805", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.275972Z", "iopub.status.busy": "2025-02-24T01:39:10.275801Z", "iopub.status.idle": "2025-02-24T01:39:10.277838Z", "shell.execute_reply": "2025-02-24T01:39:10.277601Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Revenue', 'FY', 'ST FYQuarter', 'Product Family', 'Quarter', 'Sell Through Week', 'Oppty Line Item ID', 'Marketing Part Number MPN', 'FPH Level 1 Name', 'FPH Level 2 Name', 'FPH Level 3 Name', 'FPH Level 4 Name', 'FPH Level 5 Name', 'Quantity', 'Line of business2', 'attributes.type original', 'attributes.url original', 'attributes', 'use case', 'purchase purpose', 'AEapproved', 'Project Type', 'Sold To ID', 'Opportunity Reseller Apple ID', 'Apple HQ ID', 'Opportunity Reseller Track', 'ESC Store', 'Opportunity Type', 'leasingornot', 'Opportunity ID', 'Name original', 'Probability', 'JD Appended', 'Business Model', 'Account Group', 'Group Province', 'Group City', 'Province', 'City', 'Sub Segment', 'Vertical Industry', 'zhanbaoshijian', 'Mac as Choice start time', 'FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY22Q1', 'FY25Q2 Top Account', 'FY25Q1 Top Account', 'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account', 'FY25Q2 AE', 'FY25Q1 AE', 'FY24Q4 AE', 'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE', 'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE', 'FY25Q2 AEM', 'FY25Q1 AEM', 'FY24Q4 AEM', 'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM', 'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM', 'FY21Q2 AE', 'FY21Q2 AEM', 'FY21Q3 AE', 'FY21Q3 AEM', 'FY21Q4 AE', 'FY21Q4 AEM', 'FY22Q1 AE', 'FY22Q1 AEM', 'Segment', 'Large Account', 'Total Mac Demand', 'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Industry Target Account', 'Source Detail', 'Sales Region', 'Name account', 'Account ID', 'Mac as Choice', 'Top Account Deep Dive', 'Enroll Date', 'Acquisition Group', 'NCR Program', 'Reseller for acquisition', 'Status', 'attributes.type account', 'attributes.url account', 'Owner.attributes.type', 'Owner.attributes.url', 'Owner.Name', 'Name disti/t1', 'T2 Reseller', 'Penetrated Account']\n"]}], "source": ["print(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 27, "id": "f2534630-014b-4769-bb99-248ef0f71315", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.279201Z", "iopub.status.busy": "2025-02-24T01:39:10.279109Z", "iopub.status.idle": "2025-02-24T01:39:10.282014Z", "shell.execute_reply": "2025-02-24T01:39:10.281783Z"}, "tags": []}, "outputs": [], "source": ["# 240812新增rename 'Name original':'Opportunity Name'\n", "# 241015新增一列use case 并rename为使用场景\n", "df_opportunity_line_items.rename(columns={'Name account':'Account Name','Owner.Name':'Account Owner','Apple HQ ID':'Apple ID','Project Type':'Deal type',\n", "    'Name disti/t1':'Disti/T1 Reseller','FY22Q1':'FY22Q1 Large Account','FPH Level 1 Name':'FPH Level 1 (Name)','FPH Level 2 Name':'FPH Level 2 (Name)',\n", "                                          'FPH Level 3 Name':'FPH Level 3 (Name)','FPH Level 4 Name':'FPH Level 4 (Name)','FPH Level 5 Name':'FPH Level 5 (Name)',\n", "                                          'purchase purpose':'采购用途','AEapproved':'AVP审核结果',\n", "                                          'leasingornot':'Leasing or Not','Line of business2':'Line of Business','zhanbaoshijian':'Mac as Choice加入时间',\n", "                                         'Mac as Choice start time':'Mac as Choice start time','Marketing Part Number MPN':'Marketing Part Number (MPN)','Probability':'Probability (%)',\n", "                                         'Quarter':'Product ST Quarter','Sell Through Week':'Product ST Week' ,'Revenue':'ProductLineRevenue','Province':'Province/Region',\n", "                                         'Opportunity Reseller Apple ID':'Reseller Apple ID','Opportunity Reseller Track':'Reseller Track','Name original':'Opportunity Name',\n", "                                         'Enroll Date':'NCR Enroll Date','Acquisition Group':'NCR Group','Reseller for acquisition':'NCR Reseller','Status':'NCR Status','use case':'使用场景',\n", "                                         }, inplace=True)"]}, {"cell_type": "code", "execution_count": 28, "id": "bbb2ad26-b36f-4faa-9438-a5e2f22f8c36", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.283415Z", "iopub.status.busy": "2025-02-24T01:39:10.283318Z", "iopub.status.idle": "2025-02-24T01:39:10.294157Z", "shell.execute_reply": "2025-02-24T01:39:10.293864Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df_header_filepath = (f'{homepath}/Library/CloudStorage/Box-Box/Planning\\ Team/Tableau\\ Auto-Refresh\\ Raw\\ Data/19\\ SalesForce\\ PPL/PPL_Data_AVP/PPL_Data_AVP_Greater_Than_FY24Q3.csv').replace('\\\\','')\n", "# # 对比header\n", "df_header = pd.read_csv(df_header_filepath).head(10)\n", "set(list(df_header)) - set(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 29, "id": "dcb686a5-e787-4f40-b64b-3fb97eda4540", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.295953Z", "iopub.status.busy": "2025-02-24T01:39:10.295847Z", "iopub.status.idle": "2025-02-24T01:39:10.362876Z", "shell.execute_reply": "2025-02-24T01:39:10.362514Z"}, "tags": []}, "outputs": [], "source": ["df_opportunity_line_items = df_opportunity_line_items[list(df_header)]"]}, {"cell_type": "code", "execution_count": 30, "id": "aca6fbc8-4b94-47a5-82d6-ffbf7d6c29a8", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.364816Z", "iopub.status.busy": "2025-02-24T01:39:10.364634Z", "iopub.status.idle": "2025-02-24T01:39:10.367342Z", "shell.execute_reply": "2025-02-24T01:39:10.367073Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["set(list(df_opportunity_line_items)) - set(list(df_header))"]}, {"cell_type": "code", "execution_count": null, "id": "853abd95-bdf5-4443-8159-0765c2bf9c4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 31, "id": "3938cf4a-3206-4c99-afcb-3ccfbe405f95", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.368891Z", "iopub.status.busy": "2025-02-24T01:39:10.368791Z", "iopub.status.idle": "2025-02-24T01:39:10.375282Z", "shell.execute_reply": "2025-02-24T01:39:10.375040Z"}, "tags": []}, "outputs": [], "source": ["# 将布尔列转换为 0 和 1\n", "bool_columns = df_opportunity_line_items.select_dtypes(include='bool').columns\n", "df_opportunity_line_items[bool_columns] = df_opportunity_line_items[bool_columns].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "9be1793a-e2f4-4247-a587-a326f4e5ccfa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 32, "id": "05a4bb15-dfa0-4a6c-8b8c-a6097ccebf58", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.376739Z", "iopub.status.busy": "2025-02-24T01:39:10.376663Z", "iopub.status.idle": "2025-02-24T01:39:10.381207Z", "shell.execute_reply": "2025-02-24T01:39:10.380843Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: \"['JD Appended', 'Owner.attributes.type', 'Owner.attributes.url', 'attributes', 'attributes.type account', 'attributes.type original', 'attributes.url account', 'attributes.url original'] not found in axis\"\n"]}], "source": ["#240812减少要删除的列 Name original\n", "# 要删除的列列表\n", "columns_to_remove = ['JD Appended','Owner.attributes.type','Owner.attributes.url',\n", "                     'attributes','attributes.type account','attributes.type original','attributes.url account','attributes.url original'\n", "]\n", "\n", "# 尝试删除列，并在失败时捕获异常\n", "try:\n", "    df_opportunity_line_items.drop(columns=columns_to_remove, inplace=True)\n", "    print(\"Columns removed successfully.\")\n", "except KeyError as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": 33, "id": "4f7fdaeb-bbd7-4de1-bc31-2b3298b42df0", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.382682Z", "iopub.status.busy": "2025-02-24T01:39:10.382560Z", "iopub.status.idle": "2025-02-24T01:39:10.395555Z", "shell.execute_reply": "2025-02-24T01:39:10.395291Z"}, "tags": []}, "outputs": [], "source": ["df_opportunity_line_items1 = df_opportunity_line_items[df_opportunity_line_items['Line of Business']=='Vision']"]}, {"cell_type": "code", "execution_count": 34, "id": "3e337560-e2bf-42d2-94ac-a245a9192702", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.396976Z", "iopub.status.busy": "2025-02-24T01:39:10.396876Z", "iopub.status.idle": "2025-02-24T01:39:10.406576Z", "shell.execute_reply": "2025-02-24T01:39:10.406290Z"}, "tags": []}, "outputs": [], "source": ["df_opportunity_line_items1.to_csv(df_header_filepath, index=False)"]}, {"cell_type": "code", "execution_count": 35, "id": "4ade7c72-b559-4e31-91ca-dd55827d25fd", "metadata": {"execution": {"iopub.execute_input": "2025-02-24T01:39:10.408016Z", "iopub.status.busy": "2025-02-24T01:39:10.407924Z", "iopub.status.idle": "2025-02-24T01:39:10.410335Z", "shell.execute_reply": "2025-02-24T01:39:10.410025Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["Index(['Mac as Choice', 'Top Account Deep Dive', 'FY21Q3 Large Account',\n", "       'FY21Q4 Large Account', 'FY22Q1 Large Account', 'FY22Q2 Top Account',\n", "       'FY22Q3 Top Account', 'FY22Q4 Top Account', 'FY23Q1 Top Account',\n", "       'FY23Q2 Top Account', 'FY23Q3 Top Account', 'FY23Q4 Top Account',\n", "       'FY24Q1 Top Account', 'FY24Q2 Top Account', 'FY24Q3 Top Account',\n", "       'Large Account', 'Industry Target Account'],\n", "      dtype='object')"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["bool_columns"]}, {"cell_type": "code", "execution_count": null, "id": "3e81c82b-6dec-435f-bd56-4cfbbfd270a2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}