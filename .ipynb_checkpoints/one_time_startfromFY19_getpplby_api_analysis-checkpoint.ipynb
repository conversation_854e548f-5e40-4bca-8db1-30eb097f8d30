{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5e037e7a-9d05-42a9-a862-7046cce37f59", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import datetime\n", "import os\n", "import json\n", "from string import Template\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "id": "e374c60b-90c0-4723-8b65-162d14c914b3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}