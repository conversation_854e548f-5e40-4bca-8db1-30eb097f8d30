{"cells": [{"cell_type": "code", "execution_count": null, "id": "34bc6ccf-3143-49b5-ad2c-e8d966938641", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=SELECT Account.Id,Account.FY21Q2_Identifier__c,Account.FY24Q3_Identifier__c,Account.FY22Q1__c,Account.FY21Q3_Large_Account__c,Account.FY21Q4_Large_Account__c,Account.FY24Q2_Top_Account__c,Account.FY22Q2_Top_Account__c,Account.FY22Q3_Top_Account__c,Account.FY22Q4_Top_Account__c,Account.FY23Q1_Top_Account__c,Account.FY23Q2_Top_Account__c,Account.FY23Q3_Top_Account__c,Account.FY23Q4_Top_Account__c,Account.FY24Q1_Top_Account__c,Account.FY24Q3_Top_Account__c,Account.FY24Q2_AE__c,Account.FY24Q2_AEM__c,Account.FY24Q3_AE__c,Account.FY24Q3_AEM__c,Account.Mac_as_Choice__c,Account.Mac_as_Choice_start_time__c,Account.SEI_maC__c,ACCOUNT.NAME,Account.SEI_Account_ID__c,Account.SEI_Cluster_ID__c,Account.Account_Cluster__c,Account.Is_Group__c,Account.Account_Group__c,Account.SEI_Group_ID__c,Account.Account_Group_ID__c,Account.Group_Sub_Segment__c,Account.Group_Vertical_Industry__c,Account.Province__c,Account.City__c,Account.Group_Province__c,Account.Group_City__c,(SELECT Name FROM Users),Account.Sub_Segment__c,Account.Vertical_Industry__c,Account.Top_Account_Deep_Dive__c FROM Account\n", "\"\n", "\n", "payload = {}\n", "headers = {\n", "  'Authorization': 'Bearer 00D20000000JPf6!AQ4AQM0ayAwkXjfBeDzmXrxjAYkVUjFwBQ_ejP.5ACFzGzWmJIqnhUQQb3gA0YNk6m4IxETM_Vzos3rReUbOXX1xViwJgoRG',\n", "  'Cookie': 'BrowserId=pVbaWozREe624-FNmz4TnA; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1'\n", "}\n", "\n", "response = requests.request(\"GET\", url, headers=headers, data=payload)\n", "\n", "print(response.text)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "077b3619-58ff-4b1e-954b-cfc0908d6273", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 7, "id": "4dfae93a-9fa5-456e-ae23-dbeb89bb8867", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 8, "id": "76f453fc-d0d5-4e27-be3a-2fdb10470f9d", "metadata": {}, "outputs": [], "source": ["sql_statement = \"\"\"\n", "SELECT Account.Id,Account.FY21Q2_Identifier__c,Account.FY24Q3_Identifier__c,Account.FY22Q1__c,\n", "Account.FY21Q3_Large_Account__c,Account.FY21Q4_Large_Account__c,Account.FY24Q2_Top_Account__c,\n", "Account.FY22Q2_Top_Account__c,Account.FY22Q3_Top_Account__c,Account.FY22Q4_Top_Account__c,\n", "Account.FY23Q1_Top_Account__c,Account.FY23Q2_Top_Account__c,Account.FY23Q3_Top_Account__c,\n", "Account.FY23Q4_Top_Account__c,Account.FY24Q1_Top_Account__c,Account.FY24Q3_Top_Account__c,\n", "Account.FY24Q2_AE__c,Account.FY24Q2_AEM__c,Account.FY24Q3_AE__c,Account.FY24Q3_AEM__c,\n", "Account.<PERSON>_as_Choice__c,Account.Mac_as_Choice_start_time__c,Account.SEI_maC__c,ACCOUNT.NAME,\n", "Account.SEI_Account_ID__c,Account.SEI_Cluster_ID__c,Account.Account_Cluster__c,\n", "Account.Is_Group__c,Account.Account_Group__c,Account.SEI_Group_ID__c,Account.Account_Group_ID__c,\n", "Account.Group_Sub_Segment__c,Account.Group_Vertical_Industry__c,Account.Province__c,\n", "Account.City__c,Account.Group_Province__c,Account.Group_City__c,Owner.Name,Account.Sub_Segment__c,\n", "Account.Vertical_Industry__c,Account.Top_Account_Deep_Dive__c FROM Account \n", "limit 10\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 11, "id": "7554141a-1bdb-4058-808d-f77efdbcaf4c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=SELECT Account.Id,Account.FY21Q2_Identifier__c,Account.FY24Q3_Identifier__c,Account.FY22Q1__c, Account.FY21Q3_Large_Account__c,Account.FY21Q4_Large_Account__c,Account.FY24Q2_Top_Account__c, Account.FY22Q2_Top_Account__c,Account.FY22Q3_Top_Account__c,Account.FY22Q4_Top_Account__c, Account.FY23Q1_Top_Account__c,Account.FY23Q2_Top_Account__c,Account.FY23Q3_Top_Account__c, Account.FY23Q4_Top_Account__c,Account.FY24Q1_Top_Account__c,Account.FY24Q3_Top_Account__c, Account.FY24Q2_AE__c,Account.FY24Q2_AEM__c,Account.FY24Q3_AE__c,Account.FY24Q3_AEM__c, Account.Mac_as_Choice__c,Account.Mac_as_Choice_start_time__c,Account.SEI_maC__c,ACCOUNT.NAME, Account.SEI_Account_ID__c,Account.SEI_Cluster_ID__c,Account.Account_Cluster__c, Account.Is_Group__c,Account.Account_Group__c,Account.SEI_Group_ID__c,Account.Account_Group_ID__c, Account.Group_Sub_Segment__c,Account.Group_Vertical_Industry__c,Account.Province__c, Account.City__c,Account.Group_Province__c,Account.Group_City__c,Owner.Name,Account.Sub_Segment__c, Account.Vertical_Industry__c,Account.Top_Account_Deep_Dive__c FROM Account limit 10\n"]}], "source": ["# 移除回车和多余的空格\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")\n", "\n", "# 构建 API 请求的 URL\n", "url = f\"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q={sql_statement_single_line}\"\n", "\n", "# 打印 URL 检查是否正确\n", "print(url)"]}, {"cell_type": "code", "execution_count": 1, "id": "d6b6e665-687f-473c-9949-53879a0ab195", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["IOPub data rate exceeded.\n", "The Jupyter server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--ServerApp.iopub_data_rate_limit`.\n", "\n", "Current values:\n", "ServerApp.iopub_data_rate_limit=1000000.0 (bytes/sec)\n", "ServerApp.rate_limit_window=3.0 (secs)\n", "\n"]}], "source": ["# import requests\n", "# import json\n", "\n", "# Initial URL and headers\n", "# url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "headers = {\n", "  'Authorization': f'Bearer 00D20000000JPf6!AQ4AQM0ayAwkXjfBeDzmXrxjAYkVUjFwBQ_ejP.5ACFzGzWmJIqnhUQQb3gA0YNk6m4IxETM_Vzos3rReUbOXX1xViwJgoRG',\n", "  'Cookie': 'BrowserId=pVbaWozREe624-FNmz4TnA; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.request(\"GET\", url, headers=headers)\n", "    data = response.json()\n", "    return data\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# Optionally, save to a file\n", "with open('salesforce_data.json', 'w') as f:\n", "    json.dump(all_records, f, indent=4)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "88d456c6-3155-4215-88a7-6d6574ee78f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data has been saved to salesforce_data.csv\n"]}], "source": ["# Convert to DataFrame\n", "df = pd.json_normalize(all_records)\n", "\n", "# Save to CSV\n", "df.to_csv('salesforce_data.csv', index=False)\n", "\n", "print(\"Data has been saved to salesforce_data.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "d4466a54-e048-4e50-b31b-7ae748e02fc4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}