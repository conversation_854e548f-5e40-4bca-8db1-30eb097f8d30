{"cells": [{"cell_type": "code", "execution_count": 34, "id": "bffc08c3-8517-491e-b2ee-97d97a688962", "metadata": {}, "outputs": [], "source": ["import os\n", "import datetime\n", "import configparser\n", "import requests\n", "import json\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 35, "id": "bd75d82a-d8e0-453d-a822-aff1c3addcf0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 36, "id": "b00c9ef0-1f6d-4e7a-bf35-fe84bd6a9211", "metadata": {}, "outputs": [], "source": ["from acquire_mapping import Mapping"]}, {"cell_type": "code", "execution_count": 37, "id": "0257f3a9-3d08-44ca-bb34-94ad1e5ab353", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 38, "id": "ff3bfce2-8b17-4965-a068-f6be08355b51", "metadata": {}, "outputs": [], "source": ["fiscaldate_mapping = Mapping.fiscaldate\n", "fiscaldate_mapping2 = fiscaldate_mapping.copy()[['Date','week_in_fiscal_quarter','fiscal_qtr_year_name']]\n", "fiscaldate_mapping2['week_in_fiscal_quarter'] = fiscaldate_mapping2['week_in_fiscal_quarter'].str.slice(1)\n", "fiscaldate_mapping2.rename(columns={'Date':'fiscal_dt'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 39, "id": "56e1853b-0edf-474f-9860-3cb40474ff23", "metadata": {}, "outputs": [], "source": ["now = datetime.datetime.now().strftime('%Y-%m-%d')\n", "now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"]}, {"cell_type": "code", "execution_count": 40, "id": "2a5750fc-96d8-4091-afd5-d302a21c4e78", "metadata": {}, "outputs": [], "source": ["fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_qtr_year_name'].unique().tolist()[0]\n", "week_in_fiscal_quarter = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['week_in_fiscal_quarter'].unique().tolist()[0][1:]\n", "fiscal_week_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_week_year_name'].unique().tolist()[0]\n", "# 如果是单个数字，则在前面添加 '0'\n", "if week_in_fiscal_quarter.isdigit() and len(week_in_fiscal_quarter) == 1:\n", "    week_in_fiscal_quarter2 = '0' + week_in_fiscal_quarter\n", "else:\n", "    week_in_fiscal_quarter2 = week_in_fiscal_quarter\n", "\n", "yqw_addzero = fiscal_qtr_year_name + 'W' + week_in_fiscal_quarter2\n", "next_fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date'] >= now]['fiscal_qtr_year_name'].unique()[1]"]}, {"cell_type": "code", "execution_count": 41, "id": "330d730e-de9d-418a-8730-8017b8c19ca1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY25Q3', 'FY25Q2', 'FY25Q1', 'FY24Q4', 'FY24Q3', 'FY24Q2', 'FY24Q1', 'FY23Q4', 'FY23Q3', 'FY23Q2', 'FY23Q1', 'FY22Q4', 'FY22Q3', 'FY22Q2']\n"]}], "source": ["def generate_fiscal_quarters(start_fiscal, end_fiscal):\n", "    start_year = int(start_fiscal[2:4])\n", "    start_qtr = int(start_fiscal[5])\n", "    end_year = int(end_fiscal[2:4])\n", "    end_qtr = int(end_fiscal[5])\n", "\n", "    fiscal_quarters = []\n", "    \n", "    current_year = start_year\n", "    current_qtr = start_qtr\n", "\n", "    while (current_year > end_year) or (current_year == end_year and current_qtr >= end_qtr):\n", "        fiscal_quarters.append(f\"FY{current_year:02d}Q{current_qtr}\")\n", "        if current_qtr == 1:\n", "            current_qtr = 4\n", "            current_year -= 1\n", "        else:\n", "            current_qtr -= 1\n", "\n", "    return fiscal_quarters\n", "\n", "# Example usage\n", "# fiscal_qtr_year_name = \"FY24Q4\"\n", "end_fiscal = \"FY22Q2\"\n", "\n", "fiscal_quarters_list = generate_fiscal_quarters(fiscal_qtr_year_name, end_fiscal)\n", "print(fiscal_quarters_list)"]}, {"cell_type": "code", "execution_count": 42, "id": "16949f44-14c1-4fe6-8777-8dee59ae5fe6", "metadata": {}, "outputs": [], "source": ["# Generate fiscal_quarters_list_ta ae aem\n", "fiscal_quarters_list_ta = [f\"Account.{fq}_Top_Account__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_ae = [f\"Account.{fq}_AE__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_aem = [f\"Account.{fq}_AEM__c\" for fq in fiscal_quarters_list]"]}, {"cell_type": "code", "execution_count": 43, "id": "14a241dd-de3c-4f66-a9c7-1d22af5e0331", "metadata": {}, "outputs": [], "source": ["# Combine all the lists into one\n", "all_fiscal_quarters_columns = fiscal_quarters_list_ta + fiscal_quarters_list_ae + fiscal_quarters_list_aem"]}, {"cell_type": "code", "execution_count": 44, "id": "fbebb1de-4648-4531-bec3-752b2ddc5ff7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQEAQK9gQC_opopejJaH_EA1PEGW91.x2iqJ4jAloEi2lNjxOwAKMya0qOQXQhNMRiH3BgHsxUNNDPhK7LAsUJDWo.JxOgi8'"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 202, "id": "371c91af-eb1e-48ed-ba9d-06f07d1548d2", "metadata": {}, "outputs": [], "source": ["# 241015新增一列use case 并rename为使用场景\n", "sql_statement = \"\"\"\n", "SELECT \n", "    Opportunity.use_case__c, \n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "\n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__r.Name, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "\n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "\n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c,\n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__r.Name, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Opportunity.Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.OPPORTUNITY_ID__c,\n", "    Opportunity.Name,\n", "    Opportunity.Probability,\n", "    Opportunity.JD_Appended__c,\n", "    Opportunity.Account.Mac_as_Choice__c,\n", "    Opportunity.Account.Top_Account_Deep_Dive__c,\n", "    Opportunity.Apple_Reseller__r.Name,\n", "    \n", "    Account.Enroll_Date__c,\n", "    Account.Acquisition_Group__c,\n", "    Account.NCR_Program__c,\n", "    Account.Reseller_for_acquisition__c,\n", "    Account.Status__c,\n", "    \n", "    (SELECT \n", "        Revenue__c, \n", "        FY__c, \n", "        ST_FYQuarter__c, \n", "        Product_Family__c,\n", "        Quarter__c, \n", "        Sell_Through_Week__c, \n", "        Oppty_Line_Item_ID__c, \n", "        Marketing_Part_Number_MPN__c,\n", "        Quantity,\n", "        Line_of_business2__c\n", "     FROM OpportunityLineItems\n", "     where Marketing_Part_Number_MPN__c = 'MC9X4CH/A' and ST_FYQuarter__c = 'FY25Q3' )\n", "FROM Opportunity\n", "WHERE   Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT'\n", "and Id IN (\n", "        SELECT OpportunityId \n", "        FROM OpportunityLineItem \n", "        WHERE FY__c > 'FY24' \n", "    )\n", "    \n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 203, "id": "ffcfbc02-4cc9-4b1b-8091-b4a2a1119e7e", "metadata": {}, "outputs": [], "source": ["# Insert the fiscal quarters columns after 'Account.FY22Q1__c,'\n", "insert_point = sql_statement.find(\"Account.FY22Q1__c,\") + len(\"Account.FY22Q1__c,\")\n", "sql_statement = sql_statement[:insert_point] + \"\\n    \" + \",\\n    \".join(all_fiscal_quarters_columns) + \",\" + sql_statement[insert_point:]"]}, {"cell_type": "code", "execution_count": 204, "id": "9dadc1c4-2006-49f0-995e-18c65bffdc8b", "metadata": {}, "outputs": [], "source": ["\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")"]}, {"cell_type": "code", "execution_count": 205, "id": "58f58cec-f923-4c69-a5d4-f840a6ae25c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"SELECT Opportunity.use_case__c, Account.Account_Group__c, Account.Group_Province__c, Account.Group_City__c, Account.Province__c, Account.City__c, Opportunity.Project_Type__c, Opportunity.Sold_To_ID__c, Opportunity.Opportunity_Reseller_Apple_ID__c, Opportunity.T2_Reseller__r.Name, Opportunity.Apple_HQ_ID__c, Opportunity.Opportunity_Reseller_Track__c, Opportunity.ESC_Store__c, Account.Sub_Segment__c, Account.Vertical_Industry__c, Account.zhan<PERSON><PERSON><PERSON>an__c, Account.Mac_as_Choice_start_time__c, Account.FY21Q3_Large_Account__c, Account.FY21Q4_Large_Account__c, Account.FY22Q1__c, Account.FY25Q3_Top_Account__c, Account.FY25Q2_Top_Account__c, Account.FY25Q1_Top_Account__c, Account.FY24Q4_Top_Account__c, Account.FY24Q3_Top_Account__c, Account.FY24Q2_Top_Account__c, Account.FY24Q1_Top_Account__c, Account.FY23Q4_Top_Account__c, Account.FY23Q3_Top_Account__c, Account.FY23Q2_Top_Account__c, Account.FY23Q1_Top_Account__c, Account.FY22Q4_Top_Account__c, Account.FY22Q3_Top_Account__c, Account.FY22Q2_Top_Account__c, Account.FY25Q3_AE__c, Account.FY25Q2_AE__c, Account.FY25Q1_AE__c, Account.FY24Q4_AE__c, Account.FY24Q3_AE__c, Account.FY24Q2_AE__c, Account.FY24Q1_AE__c, Account.FY23Q4_AE__c, Account.FY23Q3_AE__c, Account.FY23Q2_AE__c, Account.FY23Q1_AE__c, Account.FY22Q4_AE__c, Account.FY22Q3_AE__c, Account.FY22Q2_AE__c, Account.FY25Q3_AEM__c, Account.FY25Q2_AEM__c, Account.FY25Q1_AEM__c, Account.FY24Q4_AEM__c, Account.FY24Q3_AEM__c, Account.FY24Q2_AEM__c, Account.FY24Q1_AEM__c, Account.FY23Q4_AEM__c, Account.FY23Q3_AEM__c, Account.FY23Q2_AEM__c, Account.FY23Q1_AEM__c, Account.FY22Q4_AEM__c, Account.FY22Q3_AEM__c, Account.FY22Q2_AEM__c, Account.FY21Q2_AE__c, Account.FY21Q2_AEM__c, Account.FY21Q3_AE__c, Account.FY21Q3_AEM__c, Account.FY21Q4_AE__c, Account.FY21Q4_AEM__c, Account.FY22Q1_AE__c, Account.FY22Q1_AEM__c, Opportunity.Opportunity_Type__c, Account.Segment__c, Account.Large_Account__c, Account.Total_Mac_Demand__c, Account.PC_Install_Base__c, Account.FY22_Fcst__c, Account.FY23_Fcst__c, Opportunity.leasingornot__c, Account.Industry_Target_Account__c, Opportunity.Penetrated_Account__r.Name, Account.Source_Detail__c, Account.Sales_Region__c, Account.Name, Opportunity.Account.Owner.Name, Account.Account_ID__c, Opportunity.OPPORTUNITY_ID__c, Opportunity.Name, Opportunity.Probability, Opportunity.JD_Appended__c, Opportunity.Account.Mac_as_Choice__c, Opportunity.Account.Top_Account_Deep_Dive__c, Opportunity.Apple_Reseller__r.Name, Account.Enroll_Date__c, Account.Acquisition_Group__c, Account.NCR_Program__c, Account.Reseller_for_acquisition__c, Account.Status__c, (SELECT Revenue__c, FY__c, ST_FYQuarter__c, Product_Family__c, Quarter__c, Sell_Through_Week__c, Oppty_Line_Item_ID__c, Marketing_Part_Number_MPN__c, Quantity, Line_of_business2__c FROM OpportunityLineItems where Marketing_Part_Number_MPN__c = 'MC9X4CH/A' and ST_FYQuarter__c = 'FY25Q3' ) FROM Opportunity WHERE Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT' and Id IN ( SELECT OpportunityId FROM OpportunityLineItem WHERE FY__c > 'FY24' )\""]}, "execution_count": 205, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_statement_single_line"]}, {"cell_type": "code", "execution_count": 206, "id": "cd52a5b1-40f5-4b20-8507-506dba08b0b4", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-999\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-1999\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-2987\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-3986\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-4978\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-5971\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-6967\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-7967\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-8964\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4Cwc6QFALWAC5-9959\n"]}], "source": ["\n", "# for check keyong\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"00D20000000JPf6!AQ4AQDJU3_dhyicWbI4oh7DhiRhiAGomTWxU5SPWEc.RW2p1LaiZUlKCMx68rVWu1gSvVcFfbH3gEPUBrFLup0rQw576pYml\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "\n", "\n", "\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data_fordebug.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 208, "id": "26b2f484-9a4f-4d67-9df8-29e1f77db699", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data saved to salesforce_data.json\n"]}], "source": ["\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# !Optionally, save to a file\n", "with open('salesforce_data_fordebug.json', 'w') as f:\n", "    json.dump(all_records, f, indent=4)\n", "\n", "print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 168, "id": "28cd0594-e510-4874-9091-dc7cdb4dbe90", "metadata": {}, "outputs": [], "source": ["\n", "# # 将 JSON 数据导出并美化\n", "# with open('ppl.json', 'w', encoding='utf-8') as json_file:\n", "#     json.dump(all_records, json_file, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": 169, "id": "5c640dcf-a3d9-41d5-bbc9-fa4cb4661e8c", "metadata": {}, "outputs": [{"data": {"text/plain": ["['attributes',\n", " 'use_case__c',\n", " 'Account',\n", " 'Project_Type__c',\n", " 'Sold_To_ID__c',\n", " 'Opportunity_Reseller_Apple_ID__c',\n", " 'T2_Reseller__r',\n", " 'Apple_HQ_ID__c',\n", " 'Opportunity_Reseller_Track__c',\n", " 'ESC_Store__c',\n", " 'Opportunity_Type__c',\n", " 'leasingornot__c',\n", " 'Penetrated_Account__r',\n", " 'Opportunity_ID__c',\n", " 'Name',\n", " 'Probability',\n", " 'JD_Appended__c',\n", " 'Apple_Reseller__r']"]}, "execution_count": 169, "metadata": {}, "output_type": "execute_result"}], "source": ["meta = list(all_records[0].keys())\n", "# 移除 'OpportunityLineItems' 元素\n", "if 'OpportunityLineItems' in meta:\n", "    meta.remove('OpportunityLineItems')\n", "#     #241209kaifa\n", "# if 'Penetrated_Account__r' in meta:\n", "#     meta.remove('Penetrated_Account__r')\n", "      \n", "\n", "# 打印结果\n", "meta"]}, {"cell_type": "code", "execution_count": 145, "id": "c36d5345-d8a9-4302-ae28-b15d33dd1679", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'NoneType' object is not iterable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[145], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# 将嵌套的 OpportunityLineItems 展开到 DataFrame\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m df_opportunity_line_items \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjson_normalize\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      3\u001b[0m \u001b[43m    \u001b[49m\u001b[43mall_records\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      4\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrecord_path\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mOpportunityLineItems\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mrecords\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      5\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmeta\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmeta\u001b[49m\n\u001b[1;32m      6\u001b[0m \u001b[43m    \u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# 打印或保存 DataFrame\u001b[39;00m\n\u001b[1;32m     10\u001b[0m df_opportunity_line_items\n", "File \u001b[0;32m~/anaconda3/envs/ML/lib/python3.8/site-packages/pandas/io/json/_normalize.py:519\u001b[0m, in \u001b[0;36mjson_normalize\u001b[0;34m(data, record_path, meta, meta_prefix, record_prefix, errors, sep, max_level)\u001b[0m\n\u001b[1;32m    516\u001b[0m                 meta_vals[key]\u001b[38;5;241m.\u001b[39mappend(meta_val)\n\u001b[1;32m    517\u001b[0m             records\u001b[38;5;241m.\u001b[39mextend(recs)\n\u001b[0;32m--> 519\u001b[0m \u001b[43m_recursive_extract\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrecord_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m{\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    521\u001b[0m result \u001b[38;5;241m=\u001b[39m DataFrame(records)\n\u001b[1;32m    523\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m record_prefix \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/envs/ML/lib/python3.8/site-packages/pandas/io/json/_normalize.py:498\u001b[0m, in \u001b[0;36mjson_normalize.<locals>._recursive_extract\u001b[0;34m(data, path, seen_meta, level)\u001b[0m\n\u001b[1;32m    495\u001b[0m             \u001b[38;5;28;01mif\u001b[39;00m level \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mlen\u001b[39m(val):\n\u001b[1;32m    496\u001b[0m                 seen_meta[key] \u001b[38;5;241m=\u001b[39m _pull_field(obj, val[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m])\n\u001b[0;32m--> 498\u001b[0m         \u001b[43m_recursive_extract\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobj\u001b[49m\u001b[43m[\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseen_meta\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlevel\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    499\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    500\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m obj \u001b[38;5;129;01min\u001b[39;00m data:\n", "File \u001b[0;32m~/anaconda3/envs/ML/lib/python3.8/site-packages/pandas/io/json/_normalize.py:500\u001b[0m, in \u001b[0;36mjson_normalize.<locals>._recursive_extract\u001b[0;34m(data, path, seen_meta, level)\u001b[0m\n\u001b[1;32m    498\u001b[0m         _recursive_extract(obj[path[\u001b[38;5;241m0\u001b[39m]], path[\u001b[38;5;241m1\u001b[39m:], seen_meta, level\u001b[38;5;241m=\u001b[39mlevel \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m    499\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 500\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m obj \u001b[38;5;129;01min\u001b[39;00m data:\n\u001b[1;32m    501\u001b[0m         recs \u001b[38;5;241m=\u001b[39m _pull_records(obj, path[\u001b[38;5;241m0\u001b[39m])\n\u001b[1;32m    502\u001b[0m         recs \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m    503\u001b[0m             nested_to_record(r, sep\u001b[38;5;241m=\u001b[39msep, max_level\u001b[38;5;241m=\u001b[39mmax_level)\n\u001b[1;32m    504\u001b[0m             \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(r, \u001b[38;5;28mdict\u001b[39m)\n\u001b[1;32m    505\u001b[0m             \u001b[38;5;28;01melse\u001b[39;00m r\n\u001b[1;32m    506\u001b[0m             \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m recs\n\u001b[1;32m    507\u001b[0m         ]\n", "\u001b[0;31mTypeError\u001b[0m: 'NoneType' object is not iterable"]}], "source": ["\n", "\n", "# 将嵌套的 OpportunityLineItems 展开到 DataFrame\n", "df_opportunity_line_items = pd.json_normalize(\n", "    all_records,\n", "    record_path=['OpportunityLineItems', 'records'],\n", "    meta=meta\n", "    \n", ")\n", "\n", "# 打印或保存 DataFrame\n", "df_opportunity_line_items\n", "# df_opportunity_line_items.to_csv('salesforce_data.csv', index=False)  # 可选：保存到 CSV 文件"]}, {"cell_type": "code", "execution_count": 155, "id": "********-2d13-4306-8f12-b4bcd97c8262", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"data": {"text/plain": ["[{'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS0000046aqLYAQ'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS0000046aqL',\n", "  'Name': 'JD-FY25Q1 runrate',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': None},\n", " {'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS000004NZf9YAG'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS000004NZf9',\n", "  'Name': 'JD-手表- FY25Q1',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': None},\n", " {'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS000004NZguYAG'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS000004NZgu',\n", "  'Name': 'JD-手机常备-FY25Q1',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': None},\n", " {'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS000004R8RxYAK'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS000004R8Rx',\n", "  'Name': 'JD-手机常备-FY25Q2',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': None},\n", " {'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS000004RNaNYAW'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS000004RNaN',\n", "  'Name': 'JD-手表- FY25Q2',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': None},\n", " {'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS000004RPmBYAW'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS000004RPmB',\n", "  'Name': 'JD-FY25Q2 runrate',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': None},\n", " {'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS00000521yzYAA'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS00000521yz',\n", "  'Name': 'JD-手机常备-FY25Q3',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': None},\n", " {'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS00000523XqYAI'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS00000523Xq',\n", "  'Name': 'JD-手表- FY25Q3',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': None},\n", " {'attributes': {'type': 'Opportunity',\n", "   'url': '/services/data/v59.0/sobjects/Opportunity/006IS00000523n0YAA'},\n", "  'use_case__c': None,\n", "  'Account': {'attributes': {'type': 'Account',\n", "    'url': '/services/data/v59.0/sobjects/Account/0016F00003yUG6pQAG'},\n", "   'Account_Group__c': '海南新博航数码科技有限公司',\n", "   'Group_Province__c': 'Hainan',\n", "   'Group_City__c': 'Cheng<PERSON><PERSON>',\n", "   'Province__c': 'Hainan',\n", "   'City__c': '<PERSON><PERSON><PERSON>',\n", "   'Sub_Segment__c': 'Others',\n", "   'Vertical_Industry__c': 'Others',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c': None,\n", "   'Mac_as_Choice_start_time__c': None,\n", "   'FY21Q3_Large_Account__c': False,\n", "   'FY21Q4_Large_Account__c': False,\n", "   'FY22Q1__c': False,\n", "   'FY25Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY25Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY25Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q3_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY24Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY24Q1_Top_Account__c': <PERSON><PERSON><PERSON>,\n", "   'FY23Q4_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q2_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY23Q1_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q4_Top_Account__c': False,\n", "   'FY22Q3_Top_Account__c': <PERSON>als<PERSON>,\n", "   'FY22Q2_Top_Account__c': False,\n", "   'FY25Q3_AE__c': None,\n", "   'FY25Q2_AE__c': None,\n", "   'FY25Q1_AE__c': None,\n", "   'FY24Q4_AE__c': None,\n", "   'FY24Q3_AE__c': None,\n", "   'FY24Q2_AE__c': None,\n", "   'FY24Q1_AE__c': None,\n", "   'FY23Q4_AE__c': None,\n", "   'FY23Q3_AE__c': None,\n", "   'FY23Q2_AE__c': None,\n", "   'FY23Q1_AE__c': None,\n", "   'FY22Q4_AE__c': None,\n", "   'FY22Q3_AE__c': None,\n", "   'FY22Q2_AE__c': None,\n", "   'FY25Q3_AEM__c': None,\n", "   'FY25Q2_AEM__c': None,\n", "   'FY25Q1_AEM__c': None,\n", "   'FY24Q4_AEM__c': None,\n", "   'FY24Q3_AEM__c': None,\n", "   'FY24Q2_AEM__c': None,\n", "   'FY24Q1_AEM__c': None,\n", "   'FY23Q4_AEM__c': None,\n", "   'FY23Q3_AEM__c': None,\n", "   'FY23Q2_AEM__c': None,\n", "   'FY23Q1_AEM__c': None,\n", "   'FY22Q4_AEM__c': None,\n", "   'FY22Q3_AEM__c': None,\n", "   'FY22Q2_AEM__c': None,\n", "   'FY21Q2_AE__c': None,\n", "   'FY21Q2_AEM__c': None,\n", "   'FY21Q3_AE__c': None,\n", "   'FY21Q3_AEM__c': None,\n", "   'FY21Q4_AE__c': None,\n", "   'FY21Q4_AEM__c': None,\n", "   'FY22Q1_AE__c': None,\n", "   'FY22Q1_AEM__c': None,\n", "   'Segment__c': None,\n", "   'Large_Account__c': <PERSON><PERSON><PERSON>,\n", "   'Total_Mac_Demand__c': None,\n", "   'PC_Install_Base__c': None,\n", "   'FY22_Fcst__c': None,\n", "   'FY23_Fcst__c': None,\n", "   'Industry_Target_Account__c': False,\n", "   'Source_Detail__c': None,\n", "   'Sales_Region__c': 'China',\n", "   'Name': '海南新博航数码科技有限公司',\n", "   'Owner': {'attributes': {'type': 'User',\n", "     'url': '/services/data/v59.0/sobjects/User/***********bETKAA2'},\n", "    'Name': '<PERSON><PERSON>'},\n", "   'Account_ID__c': '0016F00003yUG6p',\n", "   'Mac_as_Choice__c': False,\n", "   'Top_Account_Deep_Dive__c': <PERSON><PERSON><PERSON>,\n", "   'Enroll_Date__c': None,\n", "   'Acquisition_Group__c': None,\n", "   'NCR_Program__c': False,\n", "   'Reseller_for_acquisition__c': None,\n", "   'Status__c': None},\n", "  'Project_Type__c': 'Runrate',\n", "  'Sold_To_ID__c': '1444369',\n", "  'Opportunity_Reseller_Apple_ID__c': '3475138',\n", "  'T2_Reseller__r': {'attributes': {'type': 'T2_Reseller__c',\n", "    'url': '/services/data/v59.0/sobjects/T2_Reseller__c/a1R6F00000PBfe0UAD'},\n", "   'Name': '北京京东耀弘贸易有限公司'},\n", "  'Apple_HQ_ID__c': '3474288',\n", "  'Opportunity_Reseller_Track__c': 'ENT',\n", "  'ESC_Store__c': None,\n", "  'Opportunity_Type__c': None,\n", "  'leasingornot__c': None,\n", "  'Penetrated_Account__r': None,\n", "  'Opportunity_ID__c': '006IS00000523n0',\n", "  'Name': 'JD-FY25Q3 runrate',\n", "  'Probability': 75.0,\n", "  'JD_Appended__c': False,\n", "  'Apple_Reseller__r': {'attributes': {'type': 'User',\n", "    'url': '/services/data/v59.0/sobjects/User/0056F00000FIN1xQAH'},\n", "   'Name': '海南新博航数码科技有限公司'},\n", "  'OpportunityLineItems': {'totalSize': 9,\n", "   'done': True,\n", "   'records': [{'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kIS000008y47QYAQ'},\n", "     'Revenue__c': 60736.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W01',\n", "     'Oppty_Line_Item_ID__c': '00kIS000008y47Q',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 104.0,\n", "     'Line_of_business2__c': 'iPad'},\n", "    {'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kfS0000003HocQAE'},\n", "     'Revenue__c': 30952.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W10',\n", "     'Oppty_Line_Item_ID__c': '00kfS0000003Hoc',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 53.0,\n", "     'Line_of_business2__c': 'iPad'},\n", "    {'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kfS0000007VdYQAU'},\n", "     'Revenue__c': 1752.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W06',\n", "     'Oppty_Line_Item_ID__c': '00kfS0000007VdY',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 3.0,\n", "     'Line_of_business2__c': 'iPad'},\n", "    {'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kfS000000AM1IQAW'},\n", "     'Revenue__c': 49056.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W07',\n", "     'Oppty_Line_Item_ID__c': '00kfS000000AM1I',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 84.0,\n", "     'Line_of_business2__c': 'iPad'},\n", "    {'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kfS000000DOqNQAW'},\n", "     'Revenue__c': 48472.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W08',\n", "     'Oppty_Line_Item_ID__c': '00kfS000000DOqN',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 83.0,\n", "     'Line_of_business2__c': 'iPad'},\n", "    {'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kfS000000G5FSQA0'},\n", "     'Revenue__c': 31536.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W09',\n", "     'Oppty_Line_Item_ID__c': '00kfS000000G5FS',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 54.0,\n", "     'Line_of_business2__c': 'iPad'},\n", "    {'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kfS000000Lod6QAC'},\n", "     'Revenue__c': 1752.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W04',\n", "     'Oppty_Line_Item_ID__c': '00kfS000000Lod6',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 3.0,\n", "     'Line_of_business2__c': 'iPad'},\n", "    {'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kfS000000MxjuQAC'},\n", "     'Revenue__c': 21608.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W11',\n", "     'Oppty_Line_Item_ID__c': '00kfS000000Mxju',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 37.0,\n", "     'Line_of_business2__c': 'iPad'},\n", "    {'attributes': {'type': 'OpportunityLineItem',\n", "      'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00kfS000000QOGkQAO'},\n", "     'Revenue__c': 117384.0,\n", "     'FY__c': 'FY25',\n", "     'ST_FYQuarter__c': 'FY25Q3',\n", "     'Product_Family__c': 'iPad Air 11in (5th Gen) WiFi',\n", "     'Quarter__c': 'Q3',\n", "     'Sell_Through_Week__c': 'W12',\n", "     'Oppty_Line_Item_ID__c': '00kfS000000QOGk',\n", "     'Marketing_Part_Number_MPN__c': 'MC9X4CH/A',\n", "     'Quantity': 201.0,\n", "     'Line_of_business2__c': 'iPad'}]}}]"]}, "execution_count": 155, "metadata": {}, "output_type": "execute_result"}], "source": ["all_records"]}, {"cell_type": "code", "execution_count": 170, "id": "9066810e-317a-464d-b6f9-c0aec45f5637", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["包含 OpportunityLineItems 的记录数: 1\n", "包含 OpportunityLineItems 且有 records 的记录数: 1\n", "包含 OpportunityLineItems 但没有 records 的记录数: 0\n", "没有 OpportunityLineItems 的记录数: 8\n"]}], "source": ["has_opportunity_lineitems = 0\n", "has_records = 0\n", "missing_opportunity_lineitems = 0\n", "missing_records = 0\n", "\n", "for rec in all_records:\n", "    if 'OpportunityLineItems' in rec and rec['OpportunityLineItems']:\n", "        has_opportunity_lineitems += 1\n", "        if 'records' in rec['OpportunityLineItems'] and rec['OpportunityLineItems']['records']:\n", "            has_records += 1\n", "        else:\n", "            missing_records += 1\n", "    else:\n", "        missing_opportunity_lineitems += 1\n", "\n", "print(f\"包含 OpportunityLineItems 的记录数: {has_opportunity_lineitems}\")\n", "print(f\"包含 OpportunityLineItems 且有 records 的记录数: {has_records}\")\n", "print(f\"包含 OpportunityLineItems 但没有 records 的记录数: {missing_records}\")\n", "print(f\"没有 OpportunityLineItems 的记录数: {missing_opportunity_lineitems}\")\n"]}, {"cell_type": "code", "execution_count": 171, "id": "fbcf5f50-10ec-4ec8-b606-7194fe372eab", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["存在 8 条记录没有 OpportunityLineItems 或 records。\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Opportunity_Reseller_Track__c</th>\n", "      <th>ESC_Store__c</th>\n", "      <th>Opportunity_Type__c</th>\n", "      <th>leasingornot__c</th>\n", "      <th>Penetrated_Account__r</th>\n", "      <th>Opportunity_ID__c</th>\n", "      <th>Name</th>\n", "      <th>Probability</th>\n", "      <th>JD_Appended__c</th>\n", "      <th>Apple_Reseller__r</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>60736.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W01</td>\n", "      <td>00kIS000008y47Q</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>104.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30952.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS0000003Hoc</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>53.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1752.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W06</td>\n", "      <td>00kfS0000007VdY</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>49056.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W07</td>\n", "      <td>00kfS000000AM1I</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>84.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>48472.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W08</td>\n", "      <td>00kfS000000DOqN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>83.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>31536.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kfS000000G5FS</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>54.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1752.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W04</td>\n", "      <td>00kfS000000Lod6</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>21608.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W11</td>\n", "      <td>00kfS000000Mxju</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>37.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>117384.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000QOGk</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>201.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>006IS00000523n0</td>\n", "      <td>JD-FY25Q3 runrate</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9 rows × 30 columns</p>\n", "</div>"], "text/plain": ["   Revenue__c FY__c ST_FYQuarter__c             Product_Family__c Quarter__c  \\\n", "0     60736.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "1     30952.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "2      1752.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "3     49056.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "4     48472.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "5     31536.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "6      1752.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "7     21608.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "8    117384.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "\n", "  Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                  W01       00kIS000008y47Q                    MC9X4CH/A   \n", "1                  W10       00kfS0000003Hoc                    MC9X4CH/A   \n", "2                  W06       00kfS0000007VdY                    MC9X4CH/A   \n", "3                  W07       00kfS000000AM1I                    MC9X4CH/A   \n", "4                  W08       00kfS000000DOqN                    MC9X4CH/A   \n", "5                  W09       00kfS000000G5FS                    MC9X4CH/A   \n", "6                  W04       00kfS000000Lod6                    MC9X4CH/A   \n", "7                  W11       00kfS000000Mxju                    MC9X4CH/A   \n", "8                  W12       00kfS000000QOGk                    MC9X4CH/A   \n", "\n", "   Quantity Line_of_business2__c  ... Opportunity_Reseller_Track__c  \\\n", "0     104.0                 iPad  ...                           ENT   \n", "1      53.0                 iPad  ...                           ENT   \n", "2       3.0                 iPad  ...                           ENT   \n", "3      84.0                 iPad  ...                           ENT   \n", "4      83.0                 iPad  ...                           ENT   \n", "5      54.0                 iPad  ...                           ENT   \n", "6       3.0                 iPad  ...                           ENT   \n", "7      37.0                 iPad  ...                           ENT   \n", "8     201.0                 iPad  ...                           ENT   \n", "\n", "  ESC_Store__c Opportunity_Type__c leasingornot__c Penetrated_Account__r  \\\n", "0         None                None            None                  None   \n", "1         None                None            None                  None   \n", "2         None                None            None                  None   \n", "3         None                None            None                  None   \n", "4         None                None            None                  None   \n", "5         None                None            None                  None   \n", "6         None                None            None                  None   \n", "7         None                None            None                  None   \n", "8         None                None            None                  None   \n", "\n", "  Opportunity_ID__c               Name Probability JD_Appended__c  \\\n", "0   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "1   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "2   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "3   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "4   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "5   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "6   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "7   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "8   006IS00000523n0  JD-FY25Q3 runrate        75.0          False   \n", "\n", "                                   Apple_Reseller__r  \n", "0  {'attributes': {'type': 'User', 'url': '/servi...  \n", "1  {'attributes': {'type': 'User', 'url': '/servi...  \n", "2  {'attributes': {'type': 'User', 'url': '/servi...  \n", "3  {'attributes': {'type': 'User', 'url': '/servi...  \n", "4  {'attributes': {'type': 'User', 'url': '/servi...  \n", "5  {'attributes': {'type': 'User', 'url': '/servi...  \n", "6  {'attributes': {'type': 'User', 'url': '/servi...  \n", "7  {'attributes': {'type': 'User', 'url': '/servi...  \n", "8  {'attributes': {'type': 'User', 'url': '/servi...  \n", "\n", "[9 rows x 30 columns]"]}, "execution_count": 171, "metadata": {}, "output_type": "execute_result"}], "source": ["# 先检查一下哪些记录缺少 OpportunityLineItems\n", "missing_items = [rec for rec in all_records if not rec.get('OpportunityLineItems') or not rec['OpportunityLineItems'].get('records')]\n", "\n", "print(f\"存在 {len(missing_items)} 条记录没有 OpportunityLineItems 或 records。\")\n", "\n", "# 过滤掉这些记录\n", "valid_records = [rec for rec in all_records if rec.get('OpportunityLineItems') and rec['OpportunityLineItems'].get('records')]\n", "\n", "# 正常展开\n", "df_opportunity_line_items = pd.json_normalize(\n", "    valid_records,\n", "    record_path=['OpportunityLineItems', 'records'],\n", "    meta=meta\n", ")\n", "\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 172, "id": "895932f2-2be4-4168-ba88-229794eed97e", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Enroll_Date__c</th>\n", "      <th>Acquisition_Group__c</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>60736.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W01</td>\n", "      <td>00kIS000008y47Q</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>104.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30952.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS0000003Hoc</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>53.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1752.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W06</td>\n", "      <td>00kfS0000007VdY</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>49056.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W07</td>\n", "      <td>00kfS000000AM1I</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>84.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>48472.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W08</td>\n", "      <td>00kfS000000DOqN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>83.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>31536.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kfS000000G5FS</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>54.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1752.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W04</td>\n", "      <td>00kfS000000Lod6</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>21608.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W11</td>\n", "      <td>00kfS000000Mxju</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>37.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>117384.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000QOGk</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>201.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9 rows × 114 columns</p>\n", "</div>"], "text/plain": ["   Revenue__c FY__c ST_FYQuarter__c             Product_Family__c Quarter__c  \\\n", "0     60736.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "1     30952.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "2      1752.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "3     49056.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "4     48472.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "5     31536.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "6      1752.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "7     21608.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "8    117384.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "\n", "  Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                  W01       00kIS000008y47Q                    MC9X4CH/A   \n", "1                  W10       00kfS0000003Hoc                    MC9X4CH/A   \n", "2                  W06       00kfS0000007VdY                    MC9X4CH/A   \n", "3                  W07       00kfS000000AM1I                    MC9X4CH/A   \n", "4                  W08       00kfS000000DOqN                    MC9X4CH/A   \n", "5                  W09       00kfS000000G5FS                    MC9X4CH/A   \n", "6                  W04       00kfS000000Lod6                    MC9X4CH/A   \n", "7                  W11       00kfS000000Mxju                    MC9X4CH/A   \n", "8                  W12       00kfS000000QOGk                    MC9X4CH/A   \n", "\n", "   Quantity Line_of_business2__c  ... Enroll_Date__c Acquisition_Group__c  \\\n", "0     104.0                 iPad  ...           None                 None   \n", "1      53.0                 iPad  ...           None                 None   \n", "2       3.0                 iPad  ...           None                 None   \n", "3      84.0                 iPad  ...           None                 None   \n", "4      83.0                 iPad  ...           None                 None   \n", "5      54.0                 iPad  ...           None                 None   \n", "6       3.0                 iPad  ...           None                 None   \n", "7      37.0                 iPad  ...           None                 None   \n", "8     201.0                 iPad  ...           None                 None   \n", "\n", "  NCR_Program__c Reseller_for_acquisition__c Status__c  \\\n", "0          False                        None      None   \n", "1          False                        None      None   \n", "2          False                        None      None   \n", "3          False                        None      None   \n", "4          False                        None      None   \n", "5          False                        None      None   \n", "6          False                        None      None   \n", "7          False                        None      None   \n", "8          False                        None      None   \n", "\n", "  attributes.type_account                             attributes.url_account  \\\n", "0                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "1                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "2                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "3                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "4                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "5                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "6                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "7                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "8                 Account  /services/data/v59.0/sobjects/Account/0016F000...   \n", "\n", "  Owner.attributes.type                               Owner.attributes.url  \\\n", "0                  User  /services/data/v59.0/sobjects/User/***********...   \n", "1                  User  /services/data/v59.0/sobjects/User/***********...   \n", "2                  User  /services/data/v59.0/sobjects/User/***********...   \n", "3                  User  /services/data/v59.0/sobjects/User/***********...   \n", "4                  User  /services/data/v59.0/sobjects/User/***********...   \n", "5                  User  /services/data/v59.0/sobjects/User/***********...   \n", "6                  User  /services/data/v59.0/sobjects/User/***********...   \n", "7                  User  /services/data/v59.0/sobjects/User/***********...   \n", "8                  User  /services/data/v59.0/sobjects/User/***********...   \n", "\n", "    Owner.Name  \n", "0  <PERSON><PERSON>  \n", "1  <PERSON><PERSON>  \n", "2  <PERSON><PERSON>  \n", "3  <PERSON><PERSON>  \n", "4  <PERSON><PERSON>  \n", "5  <PERSON><PERSON>  \n", "6  <PERSON><PERSON>  \n", "7  <PERSON><PERSON>  \n", "8  <PERSON><PERSON>  \n", "\n", "[9 rows x 114 columns]"]}, "execution_count": 172, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Account'])\n", "\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Account']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_account'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items"]}, {"cell_type": "code", "execution_count": null, "id": "1222d470-72d6-4963-838c-712e49dfd580", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 173, "id": "b01d3147-de43-43ea-b000-89df8296b3a4", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Acquisition_Group__c</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>60736.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W01</td>\n", "      <td>00kIS000008y47Q</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>104.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30952.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS0000003Hoc</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>53.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1752.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W06</td>\n", "      <td>00kfS0000007VdY</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>49056.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W07</td>\n", "      <td>00kfS000000AM1I</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>84.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>48472.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W08</td>\n", "      <td>00kfS000000DOqN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>83.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>31536.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kfS000000G5FS</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>54.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1752.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W04</td>\n", "      <td>00kfS000000Lod6</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>21608.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W11</td>\n", "      <td>00kfS000000Mxju</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>37.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>117384.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000QOGk</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>201.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9 rows × 114 columns</p>\n", "</div>"], "text/plain": ["   Revenue__c FY__c ST_FYQuarter__c             Product_Family__c Quarter__c  \\\n", "0     60736.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "1     30952.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "2      1752.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "3     49056.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "4     48472.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "5     31536.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "6      1752.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "7     21608.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "8    117384.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "\n", "  Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                  W01       00kIS000008y47Q                    MC9X4CH/A   \n", "1                  W10       00kfS0000003Hoc                    MC9X4CH/A   \n", "2                  W06       00kfS0000007VdY                    MC9X4CH/A   \n", "3                  W07       00kfS000000AM1I                    MC9X4CH/A   \n", "4                  W08       00kfS000000DOqN                    MC9X4CH/A   \n", "5                  W09       00kfS000000G5FS                    MC9X4CH/A   \n", "6                  W04       00kfS000000Lod6                    MC9X4CH/A   \n", "7                  W11       00kfS000000Mxju                    MC9X4CH/A   \n", "8                  W12       00kfS000000QOGk                    MC9X4CH/A   \n", "\n", "   Quantity Line_of_business2__c  ... Acquisition_Group__c NCR_Program__c  \\\n", "0     104.0                 iPad  ...                 None          False   \n", "1      53.0                 iPad  ...                 None          False   \n", "2       3.0                 iPad  ...                 None          False   \n", "3      84.0                 iPad  ...                 None          False   \n", "4      83.0                 iPad  ...                 None          False   \n", "5      54.0                 iPad  ...                 None          False   \n", "6       3.0                 iPad  ...                 None          False   \n", "7      37.0                 iPad  ...                 None          False   \n", "8     201.0                 iPad  ...                 None          False   \n", "\n", "  Reseller_for_acquisition__c Status__c attributes.type_account  \\\n", "0                        None      None                 Account   \n", "1                        None      None                 Account   \n", "2                        None      None                 Account   \n", "3                        None      None                 Account   \n", "4                        None      None                 Account   \n", "5                        None      None                 Account   \n", "6                        None      None                 Account   \n", "7                        None      None                 Account   \n", "8                        None      None                 Account   \n", "\n", "                              attributes.url_account Owner.attributes.type  \\\n", "0  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "1  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "2  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "3  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "4  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "5  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "6  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "7  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "8  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "\n", "                                Owner.attributes.url   Owner.Name  \\\n", "0  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "1  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "2  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "3  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "4  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "5  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "6  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "7  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "8  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "\n", "   Name_disti/t1  \n", "0  海南新博航数码科技有限公司  \n", "1  海南新博航数码科技有限公司  \n", "2  海南新博航数码科技有限公司  \n", "3  海南新博航数码科技有限公司  \n", "4  海南新博航数码科技有限公司  \n", "5  海南新博航数码科技有限公司  \n", "6  海南新博航数码科技有限公司  \n", "7  海南新博航数码科技有限公司  \n", "8  海南新博航数码科技有限公司  \n", "\n", "[9 rows x 114 columns]"]}, "execution_count": 173, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Apple_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'Name_disti/t1'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Apple_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_disti/t1'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 174, "id": "ac201719-7833-408c-9d30-f9416455cc31", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "      <th>T2_Reseller</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>60736.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W01</td>\n", "      <td>00kIS000008y47Q</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>104.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30952.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS0000003Hoc</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>53.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1752.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W06</td>\n", "      <td>00kfS0000007VdY</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>49056.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W07</td>\n", "      <td>00kfS000000AM1I</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>84.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>48472.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W08</td>\n", "      <td>00kfS000000DOqN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>83.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>31536.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kfS000000G5FS</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>54.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1752.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W04</td>\n", "      <td>00kfS000000Lod6</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>21608.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W11</td>\n", "      <td>00kfS000000Mxju</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>37.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>117384.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000QOGk</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>201.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>海南新博航数码科技有限公司</td>\n", "      <td>北京京东耀弘贸易有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9 rows × 114 columns</p>\n", "</div>"], "text/plain": ["   Revenue__c FY__c ST_FYQuarter__c             Product_Family__c Quarter__c  \\\n", "0     60736.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "1     30952.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "2      1752.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "3     49056.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "4     48472.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "5     31536.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "6      1752.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "7     21608.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "8    117384.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi         Q3   \n", "\n", "  Sell_Through_Week__c Oppty_Line_Item_ID__c Marketing_Part_Number_MPN__c  \\\n", "0                  W01       00kIS000008y47Q                    MC9X4CH/A   \n", "1                  W10       00kfS0000003Hoc                    MC9X4CH/A   \n", "2                  W06       00kfS0000007VdY                    MC9X4CH/A   \n", "3                  W07       00kfS000000AM1I                    MC9X4CH/A   \n", "4                  W08       00kfS000000DOqN                    MC9X4CH/A   \n", "5                  W09       00kfS000000G5FS                    MC9X4CH/A   \n", "6                  W04       00kfS000000Lod6                    MC9X4CH/A   \n", "7                  W11       00kfS000000Mxju                    MC9X4CH/A   \n", "8                  W12       00kfS000000QOGk                    MC9X4CH/A   \n", "\n", "   Quantity Line_of_business2__c  ... NCR_Program__c  \\\n", "0     104.0                 iPad  ...          False   \n", "1      53.0                 iPad  ...          False   \n", "2       3.0                 iPad  ...          False   \n", "3      84.0                 iPad  ...          False   \n", "4      83.0                 iPad  ...          False   \n", "5      54.0                 iPad  ...          False   \n", "6       3.0                 iPad  ...          False   \n", "7      37.0                 iPad  ...          False   \n", "8     201.0                 iPad  ...          False   \n", "\n", "  Reseller_for_acquisition__c Status__c attributes.type_account  \\\n", "0                        None      None                 Account   \n", "1                        None      None                 Account   \n", "2                        None      None                 Account   \n", "3                        None      None                 Account   \n", "4                        None      None                 Account   \n", "5                        None      None                 Account   \n", "6                        None      None                 Account   \n", "7                        None      None                 Account   \n", "8                        None      None                 Account   \n", "\n", "                              attributes.url_account Owner.attributes.type  \\\n", "0  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "1  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "2  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "3  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "4  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "5  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "6  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "7  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "8  /services/data/v59.0/sobjects/Account/0016F000...                  User   \n", "\n", "                                Owner.attributes.url   Owner.Name  \\\n", "0  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "1  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "2  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "3  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "4  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "5  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "6  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "7  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "8  /services/data/v59.0/sobjects/User/***********...  <PERSON><PERSON>   \n", "\n", "   Name_disti/t1   T2_Reseller  \n", "0  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "1  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "2  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "3  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "4  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "5  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "6  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "7  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "8  海南新博航数码科技有限公司  北京京东耀弘贸易有限公司  \n", "\n", "[9 rows x 114 columns]"]}, "execution_count": 174, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 T2_Reseller__r 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['T2_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'T2_Reseller'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['T2_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_T2_Reseller'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 175, "id": "574f3a6f-a9d8-4311-82c5-d66e5ae5cf03", "metadata": {"tags": []}, "outputs": [{"ename": "KeyError", "evalue": "\"None of [Index(['Name'], dtype='object')] are in the [columns]\"", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[175], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# 241209添加penetrated account 修正\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m# 将 Penetrated_Account__r 列中的 JSON 数据进一步展开\u001b[39;00m\n\u001b[1;32m      3\u001b[0m df_account_details \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mjson_normalize(df_opportunity_line_items[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mPenetrated_Account__r\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[0;32m----> 4\u001b[0m df_account_details \u001b[38;5;241m=\u001b[39m \u001b[43mdf_account_details\u001b[49m\u001b[43m[\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mName\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\n\u001b[1;32m      5\u001b[0m df_account_details\u001b[38;5;241m.\u001b[39mrename(columns\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mName\u001b[39m\u001b[38;5;124m'\u001b[39m:\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mPenetrated_Account\u001b[39m\u001b[38;5;124m'\u001b[39m}, inplace\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/ML/lib/python3.8/site-packages/pandas/core/frame.py:3767\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3765\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n\u001b[1;32m   3766\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[0;32m-> 3767\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_indexer_strict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcolumns\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m[\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m   3769\u001b[0m \u001b[38;5;66;03m# take() does not accept boolean indexers\u001b[39;00m\n\u001b[1;32m   3770\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(indexer, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mbool\u001b[39m:\n", "File \u001b[0;32m~/anaconda3/envs/ML/lib/python3.8/site-packages/pandas/core/indexes/base.py:5877\u001b[0m, in \u001b[0;36mIndex._get_indexer_strict\u001b[0;34m(self, key, axis_name)\u001b[0m\n\u001b[1;32m   5874\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   5875\u001b[0m     keyarr, indexer, new_indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reindex_non_unique(keyarr)\n\u001b[0;32m-> 5877\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_if_missing\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkeyarr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   5879\u001b[0m keyarr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtake(indexer)\n\u001b[1;32m   5880\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, Index):\n\u001b[1;32m   5881\u001b[0m     \u001b[38;5;66;03m# GH 42790 - Preserve name from an Index\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/ML/lib/python3.8/site-packages/pandas/core/indexes/base.py:5938\u001b[0m, in \u001b[0;36mIndex._raise_if_missing\u001b[0;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[1;32m   5936\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m use_interval_msg:\n\u001b[1;32m   5937\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[0;32m-> 5938\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNone of [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m] are in the [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maxis_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   5940\u001b[0m not_found \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(ensure_index(key)[missing_mask\u001b[38;5;241m.\u001b[39mnonzero()[\u001b[38;5;241m0\u001b[39m]]\u001b[38;5;241m.\u001b[39munique())\n\u001b[1;32m   5941\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnot_found\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not in index\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: \"None of [Index(['Name'], dtype='object')] are in the [columns]\""]}], "source": ["# 241209添加penetrated account 修正\n", "# 将 Penetrated_Account__r 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Penetrated_Account__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'Penetrated_Account'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Penetrated_Account__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_Penetrated_Account'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 176, "id": "dbf7958d-5833-4b75-bb44-bb99fff2169d", "metadata": {}, "outputs": [], "source": ["# 删除列名中的 '__c' 并替换 '_' 为 ' '\n", "df_opportunity_line_items.columns = df_opportunity_line_items.columns.str.replace('__c$', '', regex=True).str.replace('_', ' ')"]}, {"cell_type": "code", "execution_count": 177, "id": "6548de6a-ff70-4439-ab02-d40aaf5e5805", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Revenue', 'FY', 'ST FYQuarter', 'Product Family', 'Quarter', 'Sell Through Week', 'Oppty Line Item ID', 'Marketing Part Number MPN', 'Quantity', 'Line of business2', 'attributes.type original', 'attributes.url original', 'attributes', 'use case', 'Project Type', 'Sold To ID', 'Opportunity Reseller Apple ID', 'Apple HQ ID', 'Opportunity Reseller Track', 'ESC Store', 'Opportunity Type', 'leasingornot', 'Penetrated Account  r', 'Opportunity ID', 'Name original', 'Probability', 'JD Appended', 'Account Group', 'Group Province', 'Group City', 'Province', 'City', 'Sub Segment', 'Vertical Industry', 'zhan<PERSON><PERSON>jian', 'Mac as Choice start time', 'FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY22Q1', 'FY25Q3 Top Account', 'FY25Q2 Top Account', 'FY25Q1 Top Account', 'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account', 'FY25Q3 AE', 'FY25Q2 AE', 'FY25Q1 AE', 'FY24Q4 AE', 'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE', 'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE', 'FY25Q3 AEM', 'FY25Q2 AEM', 'FY25Q1 AEM', 'FY24Q4 AEM', 'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM', 'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM', 'FY21Q2 AE', 'FY21Q2 AEM', 'FY21Q3 AE', 'FY21Q3 AEM', 'FY21Q4 AE', 'FY21Q4 AEM', 'FY22Q1 AE', 'FY22Q1 AEM', 'Segment', 'Large Account', 'Total Mac Demand', 'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Industry Target Account', 'Source Detail', 'Sales Region', 'Name account', 'Account ID', 'Mac as Choice', 'Top Account Deep Dive', 'Enroll Date', 'Acquisition Group', 'NCR Program', 'Reseller for acquisition', 'Status', 'attributes.type account', 'attributes.url account', 'Owner.attributes.type', 'Owner.attributes.url', 'Owner.Name', 'Name disti/t1', 'T2 Reseller']\n"]}], "source": ["print(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 178, "id": "f2534630-014b-4769-bb99-248ef0f71315", "metadata": {}, "outputs": [], "source": ["# 240812新增rename 'Name original':'Opportunity Name'\n", "# 241015新增一列use case 并rename为使用场景\n", "df_opportunity_line_items.rename(columns={'Name account':'Account Name','Owner.Name':'Account Owner','Apple HQ ID':'Apple ID','Project Type':'Deal type',\n", "    'Name disti/t1':'Disti/T1 Reseller','FY22Q1':'FY22Q1 Large Account',\n", "                                          'leasingornot':'Leasing or Not','Line of business2':'Line of Business','zhanbaoshijian':'Mac as Choice加入时间',\n", "                                         'Mac as Choice start time':'Mac as Choice start time','Marketing Part Number MPN':'Marketing Part Number (MPN)','Probability':'Probability (%)',\n", "                                         'Quarter':'Product ST Quarter','Sell Through Week':'Product ST Week' ,'Revenue':'ProductLineRevenue','Province':'Province/Region',\n", "                                         'Opportunity Reseller Apple ID':'Reseller Apple ID','Opportunity Reseller Track':'Reseller Track','Name original':'Opportunity Name',\n", "                                         'Enroll Date':'NCR Enroll Date','Acquisition Group':'NCR Group','Reseller for acquisition':'NCR Reseller','Status':'NCR Status','use case':'使用场景',\n", "                                         }, inplace=True)"]}, {"cell_type": "code", "execution_count": 179, "id": "bbb2ad26-b36f-4faa-9438-a5e2f22f8c36", "metadata": {}, "outputs": [], "source": ["df_header_filepath = (f'{homepath}/Library/CloudStorage/Box-Box/Planning\\ Team/Tableau\\ Auto-Refresh\\ Raw\\ Data/19\\ SalesForce\\ PPL/PPL_by_Accountname_FY21_CQ_byapi.csv').replace('\\\\','')\n", "# 对比header\n", "# df_header = pd.read_csv(df_header_filepath).head(10)\n", "# set(list(df_header)) - set(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 180, "id": "3938cf4a-3206-4c99-afcb-3ccfbe405f95", "metadata": {}, "outputs": [], "source": ["# 将布尔列转换为 0 和 1\n", "bool_columns = df_opportunity_line_items.select_dtypes(include='bool').columns\n", "df_opportunity_line_items[bool_columns] = df_opportunity_line_items[bool_columns].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "9be1793a-e2f4-4247-a587-a326f4e5ccfa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 181, "id": "05a4bb15-dfa0-4a6c-8b8c-a6097ccebf58", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns removed successfully.\n"]}], "source": ["#240812减少要删除的列 Name original\n", "# 要删除的列列表\n", "columns_to_remove = ['JD Appended','Owner.attributes.type','Owner.attributes.url',\n", "                     'attributes','attributes.type account','attributes.type original','attributes.url account','attributes.url original'\n", "]\n", "\n", "# 尝试删除列，并在失败时捕获异常\n", "try:\n", "    df_opportunity_line_items.drop(columns=columns_to_remove, inplace=True)\n", "    print(\"Columns removed successfully.\")\n", "except KeyError as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": 182, "id": "5ca5dfc1-df9e-4368-bce2-1e790a53ee39", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["622.0"]}, "execution_count": 182, "metadata": {}, "output_type": "execute_result"}], "source": ["df_opportunity_line_items[(df_opportunity_line_items['Account Name']=='海南新博航数码科技有限公司')&\n", "                          (df_opportunity_line_items['ST FYQuarter']=='FY25Q3')&\n", "                          (df_opportunity_line_items['Marketing Part Number (MPN)']=='MC9X4CH/A')                          \n", "                         ]['Quantity'].sum()"]}, {"cell_type": "code", "execution_count": 183, "id": "95c77fa7-5708-4843-b62f-6044adfd53fc", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["array(['W01', 'W10', 'W06', 'W07', 'W08', 'W09', 'W04', 'W11', 'W12'],\n", "      dtype=object)"]}, "execution_count": 183, "metadata": {}, "output_type": "execute_result"}], "source": ["df_opportunity_line_items[(df_opportunity_line_items['Account Name']=='海南新博航数码科技有限公司')&\n", "                          (df_opportunity_line_items['ST FYQuarter']=='FY25Q3')&\n", "                          (df_opportunity_line_items['Marketing Part Number (MPN)']=='MC9X4CH/A')                          \n", "                         ]['Product ST Week'].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "3e337560-e2bf-42d2-94ac-a245a9192702", "metadata": {"tags": []}, "outputs": [], "source": ["df_opportunity_line_items.to_csv(df_header_filepath, index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "4ade7c72-b559-4e31-91ca-dd55827d25fd", "metadata": {"tags": []}, "outputs": [], "source": ["bool_columns"]}, {"cell_type": "code", "execution_count": null, "id": "3e81c82b-6dec-435f-bd56-4cfbbfd270a2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}