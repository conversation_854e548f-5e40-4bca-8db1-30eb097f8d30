{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bffc08c3-8517-491e-b2ee-97d97a688962", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.442284Z", "iopub.status.busy": "2025-06-02T23:30:32.441976Z", "iopub.status.idle": "2025-06-02T23:30:32.511433Z", "shell.execute_reply": "2025-06-02T23:30:32.511111Z"}}, "outputs": [], "source": ["import os\n", "import datetime\n", "import configparser\n", "import requests\n", "import json\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "id": "bd75d82a-d8e0-453d-a822-aff1c3addcf0", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.513486Z", "iopub.status.busy": "2025-06-02T23:30:32.513250Z", "iopub.status.idle": "2025-06-02T23:30:32.700655Z", "shell.execute_reply": "2025-06-02T23:30:32.699775Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "b00c9ef0-1f6d-4e7a-bf35-fe84bd6a9211", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.704824Z", "iopub.status.busy": "2025-06-02T23:30:32.704465Z", "iopub.status.idle": "2025-06-02T23:30:32.711300Z", "shell.execute_reply": "2025-06-02T23:30:32.710705Z"}}, "outputs": [], "source": ["from acquire_mapping import Mapping"]}, {"cell_type": "code", "execution_count": 4, "id": "0257f3a9-3d08-44ca-bb34-94ad1e5ab353", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.714298Z", "iopub.status.busy": "2025-06-02T23:30:32.714067Z", "iopub.status.idle": "2025-06-02T23:30:32.718968Z", "shell.execute_reply": "2025-06-02T23:30:32.717057Z"}}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 5, "id": "ff3bfce2-8b17-4965-a068-f6be08355b51", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.721756Z", "iopub.status.busy": "2025-06-02T23:30:32.721371Z", "iopub.status.idle": "2025-06-02T23:30:32.749287Z", "shell.execute_reply": "2025-06-02T23:30:32.748088Z"}}, "outputs": [], "source": ["fiscaldate_mapping = Mapping.fiscaldate\n", "fiscaldate_mapping2 = fiscaldate_mapping.copy()[['Date','week_in_fiscal_quarter','fiscal_qtr_year_name']]\n", "fiscaldate_mapping2['week_in_fiscal_quarter'] = fiscaldate_mapping2['week_in_fiscal_quarter'].str.slice(1)\n", "fiscaldate_mapping2.rename(columns={'Date':'fiscal_dt'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "56e1853b-0edf-474f-9860-3cb40474ff23", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.752605Z", "iopub.status.busy": "2025-06-02T23:30:32.752377Z", "iopub.status.idle": "2025-06-02T23:30:32.755513Z", "shell.execute_reply": "2025-06-02T23:30:32.754965Z"}}, "outputs": [], "source": ["now = datetime.datetime.now().strftime('%Y-%m-%d')\n", "now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"]}, {"cell_type": "code", "execution_count": 7, "id": "2a5750fc-96d8-4091-afd5-d302a21c4e78", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.758226Z", "iopub.status.busy": "2025-06-02T23:30:32.757995Z", "iopub.status.idle": "2025-06-02T23:30:32.776960Z", "shell.execute_reply": "2025-06-02T23:30:32.776332Z"}}, "outputs": [], "source": ["fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_qtr_year_name'].unique().tolist()[0]\n", "week_in_fiscal_quarter = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['week_in_fiscal_quarter'].unique().tolist()[0][1:]\n", "fiscal_week_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_week_year_name'].unique().tolist()[0]\n", "# 如果是单个数字，则在前面添加 '0'\n", "if week_in_fiscal_quarter.isdigit() and len(week_in_fiscal_quarter) == 1:\n", "    week_in_fiscal_quarter2 = '0' + week_in_fiscal_quarter\n", "else:\n", "    week_in_fiscal_quarter2 = week_in_fiscal_quarter\n", "\n", "yqw_addzero = fiscal_qtr_year_name + 'W' + week_in_fiscal_quarter2\n", "next_fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date'] >= now]['fiscal_qtr_year_name'].unique()[1]"]}, {"cell_type": "code", "execution_count": 8, "id": "330d730e-de9d-418a-8730-8017b8c19ca1", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.779997Z", "iopub.status.busy": "2025-06-02T23:30:32.779754Z", "iopub.status.idle": "2025-06-02T23:30:32.786965Z", "shell.execute_reply": "2025-06-02T23:30:32.785062Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY25Q3', 'FY25Q2', 'FY25Q1', 'FY24Q4', 'FY24Q3', 'FY24Q2', 'FY24Q1', 'FY23Q4', 'FY23Q3', 'FY23Q2', 'FY23Q1', 'FY22Q4', 'FY22Q3', 'FY22Q2']\n"]}], "source": ["def generate_fiscal_quarters(start_fiscal, end_fiscal):\n", "    start_year = int(start_fiscal[2:4])\n", "    start_qtr = int(start_fiscal[5])\n", "    end_year = int(end_fiscal[2:4])\n", "    end_qtr = int(end_fiscal[5])\n", "\n", "    fiscal_quarters = []\n", "    \n", "    current_year = start_year\n", "    current_qtr = start_qtr\n", "\n", "    while (current_year > end_year) or (current_year == end_year and current_qtr >= end_qtr):\n", "        fiscal_quarters.append(f\"FY{current_year:02d}Q{current_qtr}\")\n", "        if current_qtr == 1:\n", "            current_qtr = 4\n", "            current_year -= 1\n", "        else:\n", "            current_qtr -= 1\n", "\n", "    return fiscal_quarters\n", "\n", "# Example usage\n", "# fiscal_qtr_year_name = \"FY24Q4\"\n", "end_fiscal = \"FY22Q2\"\n", "\n", "fiscal_quarters_list = generate_fiscal_quarters(fiscal_qtr_year_name, end_fiscal)\n", "print(fiscal_quarters_list)"]}, {"cell_type": "code", "execution_count": 9, "id": "16949f44-14c1-4fe6-8777-8dee59ae5fe6", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.788638Z", "iopub.status.busy": "2025-06-02T23:30:32.788533Z", "iopub.status.idle": "2025-06-02T23:30:32.790747Z", "shell.execute_reply": "2025-06-02T23:30:32.790526Z"}}, "outputs": [], "source": ["# Generate fiscal_quarters_list_ta ae aem\n", "fiscal_quarters_list_ta = [f\"Account.{fq}_Top_Account__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_ae = [f\"Account.{fq}_AE__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_aem = [f\"Account.{fq}_AEM__c\" for fq in fiscal_quarters_list]"]}, {"cell_type": "code", "execution_count": 10, "id": "14a241dd-de3c-4f66-a9c7-1d22af5e0331", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.792176Z", "iopub.status.busy": "2025-06-02T23:30:32.792080Z", "iopub.status.idle": "2025-06-02T23:30:32.793769Z", "shell.execute_reply": "2025-06-02T23:30:32.793510Z"}}, "outputs": [], "source": ["# Combine all the lists into one\n", "all_fiscal_quarters_columns = fiscal_quarters_list_ta + fiscal_quarters_list_ae + fiscal_quarters_list_aem"]}, {"cell_type": "code", "execution_count": 11, "id": "fbebb1de-4648-4531-bec3-752b2ddc5ff7", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.795154Z", "iopub.status.busy": "2025-06-02T23:30:32.795058Z", "iopub.status.idle": "2025-06-02T23:30:32.798502Z", "shell.execute_reply": "2025-06-02T23:30:32.798278Z"}}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQEAQEh_UP_FInMAw87cKMiOrJsQ5o1.j4_m6A.ewO4avwD2.55P9Kgq05JNxYmXrMjQ6Kap_9joWdJyfFos3rbBDUarnlRr'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 12, "id": "371c91af-eb1e-48ed-ba9d-06f07d1548d2", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.799767Z", "iopub.status.busy": "2025-06-02T23:30:32.799679Z", "iopub.status.idle": "2025-06-02T23:30:32.802123Z", "shell.execute_reply": "2025-06-02T23:30:32.801898Z"}}, "outputs": [], "source": ["# 241015新增一列use case 并rename为使用场景\n", "sql_statement = \"\"\"\n", "SELECT \n", "    Opportunity.use_case__c, \n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "\n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__r.Name, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "\n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "\n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c,\n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__r.Name, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Opportunity.Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.OPPORTUNITY_ID__c,\n", "    Opportunity.Name,\n", "    Opportunity.Probability,\n", "    Opportunity.JD_Appended__c,\n", "    Opportunity.Account.Mac_as_Choice__c,\n", "    Opportunity.Account.Top_Account_Deep_Dive__c,\n", "    Opportunity.Apple_Reseller__r.Name,\n", "    \n", "    Account.Enroll_Date__c,\n", "    Account.Acquisition_Group__c,\n", "    Account.NCR_Program__c,\n", "    Account.Reseller_for_acquisition__c,\n", "    Account.Status__c,\n", "    \n", "    (SELECT \n", "        Revenue__c, \n", "        FY__c, \n", "        ST_FYQuarter__c, \n", "        Product_Family__c,\n", "        Quarter__c, \n", "        Sell_Through_Week__c, \n", "        Oppty_Line_Item_ID__c, \n", "        Marketing_Part_Number_MPN__c,\n", "        Quantity,\n", "        Line_of_business2__c\n", "     FROM OpportunityLineItems)\n", "FROM Opportunity\n", "WHERE  Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT'\n", "and Id IN (\n", "        SELECT OpportunityId \n", "        FROM OpportunityLineItem \n", "        WHERE FY__c > 'FY20' \n", "    )\n", "    \n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "ffcfbc02-4cc9-4b1b-8091-b4a2a1119e7e", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.803260Z", "iopub.status.busy": "2025-06-02T23:30:32.803172Z", "iopub.status.idle": "2025-06-02T23:30:32.805116Z", "shell.execute_reply": "2025-06-02T23:30:32.804889Z"}}, "outputs": [], "source": ["# Insert the fiscal quarters columns after 'Account.FY22Q1__c,'\n", "insert_point = sql_statement.find(\"Account.FY22Q1__c,\") + len(\"Account.FY22Q1__c,\")\n", "sql_statement = sql_statement[:insert_point] + \"\\n    \" + \",\\n    \".join(all_fiscal_quarters_columns) + \",\" + sql_statement[insert_point:]"]}, {"cell_type": "code", "execution_count": 14, "id": "9dadc1c4-2006-49f0-995e-18c65bffdc8b", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.806245Z", "iopub.status.busy": "2025-06-02T23:30:32.806165Z", "iopub.status.idle": "2025-06-02T23:30:32.807804Z", "shell.execute_reply": "2025-06-02T23:30:32.807577Z"}}, "outputs": [], "source": ["\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")"]}, {"cell_type": "code", "execution_count": 15, "id": "58f58cec-f923-4c69-a5d4-f840a6ae25c6", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.808919Z", "iopub.status.busy": "2025-06-02T23:30:32.808836Z", "iopub.status.idle": "2025-06-02T23:30:32.810813Z", "shell.execute_reply": "2025-06-02T23:30:32.810542Z"}}, "outputs": [{"data": {"text/plain": ["\"SELECT Opportunity.use_case__c, Account.Account_Group__c, Account.Group_Province__c, Account.Group_City__c, Account.Province__c, Account.City__c, Opportunity.Project_Type__c, Opportunity.Sold_To_ID__c, Opportunity.Opportunity_Reseller_Apple_ID__c, Opportunity.T2_Reseller__r.Name, Opportunity.Apple_HQ_ID__c, Opportunity.Opportunity_Reseller_Track__c, Opportunity.ESC_Store__c, Account.Sub_Segment__c, Account.Vertical_Industry__c, Account.zhan<PERSON><PERSON><PERSON>an__c, Account.Mac_as_Choice_start_time__c, Account.FY21Q3_Large_Account__c, Account.FY21Q4_Large_Account__c, Account.FY22Q1__c, Account.FY25Q3_Top_Account__c, Account.FY25Q2_Top_Account__c, Account.FY25Q1_Top_Account__c, Account.FY24Q4_Top_Account__c, Account.FY24Q3_Top_Account__c, Account.FY24Q2_Top_Account__c, Account.FY24Q1_Top_Account__c, Account.FY23Q4_Top_Account__c, Account.FY23Q3_Top_Account__c, Account.FY23Q2_Top_Account__c, Account.FY23Q1_Top_Account__c, Account.FY22Q4_Top_Account__c, Account.FY22Q3_Top_Account__c, Account.FY22Q2_Top_Account__c, Account.FY25Q3_AE__c, Account.FY25Q2_AE__c, Account.FY25Q1_AE__c, Account.FY24Q4_AE__c, Account.FY24Q3_AE__c, Account.FY24Q2_AE__c, Account.FY24Q1_AE__c, Account.FY23Q4_AE__c, Account.FY23Q3_AE__c, Account.FY23Q2_AE__c, Account.FY23Q1_AE__c, Account.FY22Q4_AE__c, Account.FY22Q3_AE__c, Account.FY22Q2_AE__c, Account.FY25Q3_AEM__c, Account.FY25Q2_AEM__c, Account.FY25Q1_AEM__c, Account.FY24Q4_AEM__c, Account.FY24Q3_AEM__c, Account.FY24Q2_AEM__c, Account.FY24Q1_AEM__c, Account.FY23Q4_AEM__c, Account.FY23Q3_AEM__c, Account.FY23Q2_AEM__c, Account.FY23Q1_AEM__c, Account.FY22Q4_AEM__c, Account.FY22Q3_AEM__c, Account.FY22Q2_AEM__c, Account.FY21Q2_AE__c, Account.FY21Q2_AEM__c, Account.FY21Q3_AE__c, Account.FY21Q3_AEM__c, Account.FY21Q4_AE__c, Account.FY21Q4_AEM__c, Account.FY22Q1_AE__c, Account.FY22Q1_AEM__c, Opportunity.Opportunity_Type__c, Account.Segment__c, Account.Large_Account__c, Account.Total_Mac_Demand__c, Account.PC_Install_Base__c, Account.FY22_Fcst__c, Account.FY23_Fcst__c, Opportunity.leasingornot__c, Account.Industry_Target_Account__c, Opportunity.Penetrated_Account__r.Name, Account.Source_Detail__c, Account.Sales_Region__c, Account.Name, Opportunity.Account.Owner.Name, Account.Account_ID__c, Opportunity.OPPORTUNITY_ID__c, Opportunity.Name, Opportunity.Probability, Opportunity.JD_Appended__c, Opportunity.Account.Mac_as_Choice__c, Opportunity.Account.Top_Account_Deep_Dive__c, Opportunity.Apple_Reseller__r.Name, Account.Enroll_Date__c, Account.Acquisition_Group__c, Account.NCR_Program__c, Account.Reseller_for_acquisition__c, Account.Status__c, (SELECT Revenue__c, FY__c, ST_FYQuarter__c, Product_Family__c, Quarter__c, Sell_Through_Week__c, Oppty_Line_Item_ID__c, Marketing_Part_Number_MPN__c, Quantity, Line_of_business2__c FROM OpportunityLineItems) FROM Opportunity WHERE Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT' and Id IN ( SELECT OpportunityId FROM OpportunityLineItem WHERE FY__c > 'FY20' )\""]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_statement_single_line"]}, {"cell_type": "code", "execution_count": 16, "id": "cd52a5b1-40f5-4b20-8507-506dba08b0b4", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:30:32.811986Z", "iopub.status.busy": "2025-06-02T23:30:32.811897Z", "iopub.status.idle": "2025-06-02T23:34:46.378312Z", "shell.execute_reply": "2025-06-02T23:34:46.377654Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-45\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-184\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-333\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-435\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-453\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-493\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-571\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-736\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-931\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1114\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1267\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1331\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1342\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1350\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1371\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1378\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1404\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1479\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1626\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1669\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1747\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1889\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-1986\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-2195\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-2333\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-2460\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-2607\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-2728\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-2896\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3036\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3107\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3148\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3264\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3345\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3450\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3497\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3533\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3641\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3734\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3757\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3832\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-3923\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4029\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4068\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4117\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4226\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4300\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4499\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4601\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4639\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-4830\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-5020\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-5202\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-5385\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-5537\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-5699\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-5780\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-5875\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-6024\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-6169\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-6361\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-6557\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-6691\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-6864\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-6971\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-7115\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-7231\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-7402\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-7588\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-7752\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-7929\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-8064\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-8175\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-8313\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-8466\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-8678\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-8806\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-8900\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-9080\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-9199\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-9373\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-9494\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-9646\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-9669\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-9788\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-9969\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-10163\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-10309\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-10485\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-10664\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-10905\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-11088\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-11291\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-11479\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-11693\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-11895\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-12015\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-12193\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-12297\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-12375\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-12445\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-12642\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-12849\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-12978\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-13180\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-13349\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-13549\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-13684\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-13906\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-14064\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-14209\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-14418\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-14584\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-14791\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-14991\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-15184\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-15229\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-15425\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-15657\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-15807\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-16063\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-16213\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-16406\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-16593\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-16809\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-16987\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17057\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17253\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17500\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17671\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17861\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17863\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17864\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17865\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17866\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17867\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17868\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17869\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17870\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17871\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17872\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17873\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17874\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17875\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17877\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17879\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17880\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17883\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17884\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17886\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17887\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17888\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17889\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17890\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17892\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17893\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17897\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17901\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17905\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-17947\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-18039\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-18248\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-18481\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-18687\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-18894\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-19102\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-19287\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-19416\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-19647\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-19750\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-19963\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-20172\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-20382\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-20575\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-20831\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-21038\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-21171\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-21418\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-21664\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-21811\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-22069\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-22338\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-22574\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-22807\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-22992\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23138\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23281\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23289\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23295\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23400\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23436\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23438\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23440\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23442\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23443\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23444\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23447\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23494\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23516\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23634\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23814\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-23994\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-24121\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-24350\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-24601\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-24747\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-24751\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-24753\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-24855\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-25112\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-25367\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-25576\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-25740\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-25892\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-26153\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-26451\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-26732\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-27044\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-27246\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-27365\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-27371\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-27376\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-27569\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-27664\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-27854\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-28104\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-28204\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-28362\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-28461\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-28589\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-28790\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-28945\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-29200\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-29207\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-29228\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-29432\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-29625\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-29801\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-29994\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-30132\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-30316\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-30526\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-30741\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-30838\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-31067\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-31178\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-31346\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-31419\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-31642\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-31864\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-31902\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-32151\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-32410\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-32467\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-32566\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-32757\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-33007\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-33326\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-33581\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-33805\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-33864\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-33987\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-34296\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-34484\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-34721\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-34937\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35102\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35375\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35448\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35499\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35549\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35573\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35707\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35854\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35922\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-35989\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-36044\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-36148\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-36315\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-36487\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-36619\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-36762\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-36908\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37039\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37180\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37247\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37397\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37539\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37609\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37698\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37741\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-37931\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-38036\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-38091\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-38262\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-38376\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-38566\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-38727\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-38994\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-39328\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-39561\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-39827\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-40119\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-40361\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-40590\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-40844\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-41040\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-41165\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-41368\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-41672\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-41986\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-42344\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-42539\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-42795\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-43094\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-43339\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-43658\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-43971\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-44288\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-44599\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-44921\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-45231\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx4HLBJBeclfA4B-45570\n"]}], "source": ["\n", "# for check keyong\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"00D20000000JPf6!AQ4AQDJU3_dhyicWbI4oh7DhiRhiAGomTWxU5SPWEc.RW2p1LaiZUlKCMx68rVWu1gSvVcFfbH3gEPUBrFLup0rQw576pYml\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "\n", "\n", "\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data_fordebug.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "5f384cda-79bd-4c8b-ab51-7efefce2b707", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "26b2f484-9a4f-4d67-9df8-29e1f77db699", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:34:46.381860Z", "iopub.status.busy": "2025-06-02T23:34:46.381601Z", "iopub.status.idle": "2025-06-02T23:34:51.909964Z", "shell.execute_reply": "2025-06-02T23:34:51.909701Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data saved to salesforce_data.json\n"]}], "source": ["\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# !Optionally, save to a file\n", "with open('salesforce_data_fordebug.json', 'w') as f:\n", "    json.dump(all_records, f, indent=4)\n", "\n", "print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "28cd0594-e510-4874-9091-dc7cdb4dbe90", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:34:51.911466Z", "iopub.status.busy": "2025-06-02T23:34:51.911383Z", "iopub.status.idle": "2025-06-02T23:34:51.912855Z", "shell.execute_reply": "2025-06-02T23:34:51.912654Z"}}, "outputs": [], "source": ["\n", "# # 将 JSON 数据导出并美化\n", "# with open('ppl.json', 'w', encoding='utf-8') as json_file:\n", "#     json.dump(all_records, json_file, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": 19, "id": "5c640dcf-a3d9-41d5-bbc9-fa4cb4661e8c", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:34:51.914161Z", "iopub.status.busy": "2025-06-02T23:34:51.914078Z", "iopub.status.idle": "2025-06-02T23:34:51.916524Z", "shell.execute_reply": "2025-06-02T23:34:51.916311Z"}}, "outputs": [{"data": {"text/plain": ["['attributes',\n", " 'use_case__c',\n", " 'Account',\n", " 'Project_Type__c',\n", " 'Sold_To_ID__c',\n", " 'Opportunity_Reseller_Apple_ID__c',\n", " 'T2_Reseller__r',\n", " 'Apple_HQ_ID__c',\n", " 'Opportunity_Reseller_Track__c',\n", " 'ESC_Store__c',\n", " 'Opportunity_Type__c',\n", " 'leasingornot__c',\n", " 'Penetrated_Account__r',\n", " 'Opportunity_ID__c',\n", " 'Name',\n", " 'Probability',\n", " 'JD_Appended__c',\n", " 'Apple_Reseller__r']"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["meta = list(all_records[0].keys())\n", "# 移除 'OpportunityLineItems' 元素\n", "if 'OpportunityLineItems' in meta:\n", "    meta.remove('OpportunityLineItems')\n", "#     #241209kaifa\n", "# if 'Penetrated_Account__r' in meta:\n", "#     meta.remove('Penetrated_Account__r')\n", "      \n", "\n", "# 打印结果\n", "meta"]}, {"cell_type": "code", "execution_count": 20, "id": "c36d5345-d8a9-4302-ae28-b15d33dd1679", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:34:51.917741Z", "iopub.status.busy": "2025-06-02T23:34:51.917656Z", "iopub.status.idle": "2025-06-02T23:34:55.702793Z", "shell.execute_reply": "2025-06-02T23:34:55.702483Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Opportunity_Reseller_Track__c</th>\n", "      <th>ESC_Store__c</th>\n", "      <th>Opportunity_Type__c</th>\n", "      <th>leasingornot__c</th>\n", "      <th>Penetrated_Account__r</th>\n", "      <th>Opportunity_ID__c</th>\n", "      <th>Name</th>\n", "      <th>Probability</th>\n", "      <th>JD_Appended__c</th>\n", "      <th>Apple_Reseller__r</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>611310.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xlgh</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>246.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237lioQAA</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000052jOW</td>\n", "      <td>FY25Q3设备采购</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>304128.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W13</td>\n", "      <td>00kIS000008xoMr</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>144.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237lioQAA</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000052jOW</td>\n", "      <td>FY25Q3设备采购</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>69696.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W05</td>\n", "      <td>00kIS000008xzwF</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>33.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237lioQAA</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000052jOW</td>\n", "      <td>FY25Q3设备采购</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>248500.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xzwG</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>100.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237lioQAA</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000052jOW</td>\n", "      <td>FY25Q3设备采购</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>247104.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008y06V</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>117.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237lioQAA</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000052jOW</td>\n", "      <td>FY25Q3设备采购</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266329</th>\n", "      <td>2920.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000drUN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>001IS000006XgSxYAK</td>\n", "      <td>Gifting</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006fS000000WJyp</td>\n", "      <td>EC</td>\n", "      <td>25.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266330</th>\n", "      <td>2322.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcp</td>\n", "      <td>MW0X3CH/A</td>\n", "      <td>2.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006fS000000WJAn</td>\n", "      <td>办公</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266331</th>\n", "      <td>3483.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcq</td>\n", "      <td>MC6U4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006fS000000WJAn</td>\n", "      <td>办公</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266332</th>\n", "      <td>3976.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePwF</td>\n", "      <td>MX313CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006fS000000WJ7Z</td>\n", "      <td>办公</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266333</th>\n", "      <td>978.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000eSVR</td>\n", "      <td>MW0W3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006fS000000WJ2j</td>\n", "      <td>办公</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>266334 rows × 30 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c             Product_Family__c  \\\n", "0         611310.0  FY25          FY25Q3                   MacBook Pro   \n", "1         304128.0  FY25          FY25Q3                   MacBook Pro   \n", "2          69696.0  FY25          FY25Q3                   MacBook Pro   \n", "3         248500.0  FY25          FY25Q3                   MacBook Pro   \n", "4         247104.0  FY25          FY25Q3                   MacBook Pro   \n", "...            ...   ...             ...                           ...   \n", "266329      2920.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi   \n", "266330      2322.0  FY25          FY25Q3                   MacBook Air   \n", "266331      3483.0  FY25          FY25Q3                   MacBook Air   \n", "266332      3976.0  FY25          FY25Q3                   MacBook Pro   \n", "266333       978.0  FY25          FY25Q3                   MacBook Air   \n", "\n", "       Quarter__c Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0              Q3                  W02       00kIS000008xlgh   \n", "1              Q3                  W13       00kIS000008xoMr   \n", "2              Q3                  W05       00kIS000008xzwF   \n", "3              Q3                  W02       00kIS000008xzwG   \n", "4              Q3                  W02       00kIS000008y06V   \n", "...           ...                  ...                   ...   \n", "266329         Q3                  W12       00kfS000000drUN   \n", "266330         Q3                  W10       00kfS000000ePcp   \n", "266331         Q3                  W10       00kfS000000ePcq   \n", "266332         Q3                  W10       00kfS000000ePwF   \n", "266333         Q3                  W10       00kfS000000eSVR   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MX2T3CH/A     246.0                  CPU  ...   \n", "1                         MX2E3CH/A     144.0                  CPU  ...   \n", "2                         MX2E3CH/A      33.0                  CPU  ...   \n", "3                         MX2T3CH/A     100.0                  CPU  ...   \n", "4                         MX2E3CH/A     117.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "266329                    MC9X4CH/A       5.0                 iPad  ...   \n", "266330                    MW0X3CH/A       2.0                  CPU  ...   \n", "266331                    MC6U4CH/A       3.0                  CPU  ...   \n", "266332                    MX313CH/A       1.0                  CPU  ...   \n", "266333                    MW0W3CH/A       1.0                  CPU  ...   \n", "\n", "       Opportunity_Reseller_Track__c        ESC_Store__c Opportunity_Type__c  \\\n", "0                                ENT  0016F0000237lioQAA            Solution   \n", "1                                ENT  0016F0000237lioQAA            Solution   \n", "2                                ENT  0016F0000237lioQAA            Solution   \n", "3                                ENT  0016F0000237lioQAA            Solution   \n", "4                                ENT  0016F0000237lioQAA            Solution   \n", "...                              ...                 ...                 ...   \n", "266329                           ENT  001IS000006XgSxYAK             Gifting   \n", "266330                           ENT                None            Solution   \n", "266331                           ENT                None            Solution   \n", "266332                           ENT                None            Solution   \n", "266333                           ENT                None            Solution   \n", "\n", "       leasingornot__c Penetrated_Account__r Opportunity_ID__c        Name  \\\n", "0                   No                  None   006IS0000052jOW  FY25Q3设备采购   \n", "1                   No                  None   006IS0000052jOW  FY25Q3设备采购   \n", "2                   No                  None   006IS0000052jOW  FY25Q3设备采购   \n", "3                   No                  None   006IS0000052jOW  FY25Q3设备采购   \n", "4                   No                  None   006IS0000052jOW  FY25Q3设备采购   \n", "...                ...                   ...               ...         ...   \n", "266329              No                  None   006fS000000WJyp          EC   \n", "266330              No                  None   006fS000000WJAn          办公   \n", "266331              No                  None   006fS000000WJAn          办公   \n", "266332              No                  None   006fS000000WJ7Z          办公   \n", "266333              No                  None   006fS000000WJ2j          办公   \n", "\n", "       Probability JD_Appended__c  \\\n", "0             75.0          False   \n", "1             75.0          False   \n", "2             75.0          False   \n", "3             75.0          False   \n", "4             75.0          False   \n", "...            ...            ...   \n", "266329        25.0          False   \n", "266330        75.0          False   \n", "266331        75.0          False   \n", "266332        75.0          False   \n", "266333        75.0          False   \n", "\n", "                                        Apple_Reseller__r  \n", "0       {'attributes': {'type': 'User', 'url': '/servi...  \n", "1       {'attributes': {'type': 'User', 'url': '/servi...  \n", "2       {'attributes': {'type': 'User', 'url': '/servi...  \n", "3       {'attributes': {'type': 'User', 'url': '/servi...  \n", "4       {'attributes': {'type': 'User', 'url': '/servi...  \n", "...                                                   ...  \n", "266329  {'attributes': {'type': 'User', 'url': '/servi...  \n", "266330  {'attributes': {'type': 'User', 'url': '/servi...  \n", "266331  {'attributes': {'type': 'User', 'url': '/servi...  \n", "266332  {'attributes': {'type': 'User', 'url': '/servi...  \n", "266333  {'attributes': {'type': 'User', 'url': '/servi...  \n", "\n", "[266334 rows x 30 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 将嵌套的 OpportunityLineItems 展开到 DataFrame\n", "df_opportunity_line_items = pd.json_normalize(\n", "    all_records,\n", "    record_path=['OpportunityLineItems', 'records'],\n", "    meta=meta\n", "    \n", ")\n", "\n", "# 打印或保存 DataFrame\n", "df_opportunity_line_items\n", "# df_opportunity_line_items.to_csv('salesforce_data.csv', index=False)  # 可选：保存到 CSV 文件"]}, {"cell_type": "code", "execution_count": 21, "id": "895932f2-2be4-4168-ba88-229794eed97e", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:34:55.704352Z", "iopub.status.busy": "2025-06-02T23:34:55.704249Z", "iopub.status.idle": "2025-06-02T23:35:00.761506Z", "shell.execute_reply": "2025-06-02T23:35:00.761191Z"}, "scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Enroll_Date__c</th>\n", "      <th>Acquisition_Group__c</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>611310.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xlgh</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>246.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>304128.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W13</td>\n", "      <td>00kIS000008xoMr</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>144.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>69696.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W05</td>\n", "      <td>00kIS000008xzwF</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>33.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>248500.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xzwG</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>100.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>247104.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008y06V</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>117.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266329</th>\n", "      <td>2920.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000drUN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266330</th>\n", "      <td>2322.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcp</td>\n", "      <td>MW0X3CH/A</td>\n", "      <td>2.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266331</th>\n", "      <td>3483.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcq</td>\n", "      <td>MC6U4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266332</th>\n", "      <td>3976.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePwF</td>\n", "      <td>MX313CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266333</th>\n", "      <td>978.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000eSVR</td>\n", "      <td>MW0W3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>266334 rows × 114 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c             Product_Family__c  \\\n", "0         611310.0  FY25          FY25Q3                   MacBook Pro   \n", "1         304128.0  FY25          FY25Q3                   MacBook Pro   \n", "2          69696.0  FY25          FY25Q3                   MacBook Pro   \n", "3         248500.0  FY25          FY25Q3                   MacBook Pro   \n", "4         247104.0  FY25          FY25Q3                   MacBook Pro   \n", "...            ...   ...             ...                           ...   \n", "266329      2920.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi   \n", "266330      2322.0  FY25          FY25Q3                   MacBook Air   \n", "266331      3483.0  FY25          FY25Q3                   MacBook Air   \n", "266332      3976.0  FY25          FY25Q3                   MacBook Pro   \n", "266333       978.0  FY25          FY25Q3                   MacBook Air   \n", "\n", "       Quarter__c Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0              Q3                  W02       00kIS000008xlgh   \n", "1              Q3                  W13       00kIS000008xoMr   \n", "2              Q3                  W05       00kIS000008xzwF   \n", "3              Q3                  W02       00kIS000008xzwG   \n", "4              Q3                  W02       00kIS000008y06V   \n", "...           ...                  ...                   ...   \n", "266329         Q3                  W12       00kfS000000drUN   \n", "266330         Q3                  W10       00kfS000000ePcp   \n", "266331         Q3                  W10       00kfS000000ePcq   \n", "266332         Q3                  W10       00kfS000000ePwF   \n", "266333         Q3                  W10       00kfS000000eSVR   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MX2T3CH/A     246.0                  CPU  ...   \n", "1                         MX2E3CH/A     144.0                  CPU  ...   \n", "2                         MX2E3CH/A      33.0                  CPU  ...   \n", "3                         MX2T3CH/A     100.0                  CPU  ...   \n", "4                         MX2E3CH/A     117.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "266329                    MC9X4CH/A       5.0                 iPad  ...   \n", "266330                    MW0X3CH/A       2.0                  CPU  ...   \n", "266331                    MC6U4CH/A       3.0                  CPU  ...   \n", "266332                    MX313CH/A       1.0                  CPU  ...   \n", "266333                    MW0W3CH/A       1.0                  CPU  ...   \n", "\n", "       Enroll_Date__c Acquisition_Group__c NCR_Program__c  \\\n", "0                None                 None          False   \n", "1                None                 None          False   \n", "2                None                 None          False   \n", "3                None                 None          False   \n", "4                None                 None          False   \n", "...               ...                  ...            ...   \n", "266329           None                 None          False   \n", "266330           None                 None          False   \n", "266331           None                 None          False   \n", "266332           None                 None          False   \n", "266333           None                 None          False   \n", "\n", "       Reseller_for_acquisition__c Status__c attributes.type_account  \\\n", "0                             None      None                 Account   \n", "1                             None      None                 Account   \n", "2                             None      None                 Account   \n", "3                             None      None                 Account   \n", "4                             None      None                 Account   \n", "...                            ...       ...                     ...   \n", "266329                        None      None                 Account   \n", "266330                        None      None                 Account   \n", "266331                        None      None                 Account   \n", "266332                        None      None                 Account   \n", "266333                        None      None                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/********...   \n", "1       /services/data/v59.0/sobjects/Account/********...   \n", "2       /services/data/v59.0/sobjects/Account/********...   \n", "3       /services/data/v59.0/sobjects/Account/********...   \n", "4       /services/data/v59.0/sobjects/Account/********...   \n", "...                                                   ...   \n", "266329  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266330  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266331  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266332  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266333  /services/data/v59.0/sobjects/Account/001fS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "266329                  User   \n", "266330                  User   \n", "266331                  User   \n", "266332                  User   \n", "266333                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \n", "0       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>  \n", "1       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>  \n", "2       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>  \n", "3       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>  \n", "4       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>  \n", "...                                                   ...            ...  \n", "266329  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "266330  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "266331  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "266332  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "266333  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "\n", "[266334 rows x 114 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Account'])\n", "\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Account']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_account'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items"]}, {"cell_type": "code", "execution_count": null, "id": "1222d470-72d6-4963-838c-712e49dfd580", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "id": "b01d3147-de43-43ea-b000-89df8296b3a4", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:00.763191Z", "iopub.status.busy": "2025-06-02T23:35:00.763081Z", "iopub.status.idle": "2025-06-02T23:35:01.955956Z", "shell.execute_reply": "2025-06-02T23:35:01.955654Z"}, "scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Acquisition_Group__c</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>611310.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xlgh</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>246.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>304128.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W13</td>\n", "      <td>00kIS000008xoMr</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>144.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>69696.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W05</td>\n", "      <td>00kIS000008xzwF</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>33.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>248500.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xzwG</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>100.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>247104.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008y06V</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>117.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266329</th>\n", "      <td>2920.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000drUN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266330</th>\n", "      <td>2322.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcp</td>\n", "      <td>MW0X3CH/A</td>\n", "      <td>2.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266331</th>\n", "      <td>3483.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcq</td>\n", "      <td>MC6U4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266332</th>\n", "      <td>3976.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePwF</td>\n", "      <td>MX313CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266333</th>\n", "      <td>978.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000eSVR</td>\n", "      <td>MW0W3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>266334 rows × 114 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c             Product_Family__c  \\\n", "0         611310.0  FY25          FY25Q3                   MacBook Pro   \n", "1         304128.0  FY25          FY25Q3                   MacBook Pro   \n", "2          69696.0  FY25          FY25Q3                   MacBook Pro   \n", "3         248500.0  FY25          FY25Q3                   MacBook Pro   \n", "4         247104.0  FY25          FY25Q3                   MacBook Pro   \n", "...            ...   ...             ...                           ...   \n", "266329      2920.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi   \n", "266330      2322.0  FY25          FY25Q3                   MacBook Air   \n", "266331      3483.0  FY25          FY25Q3                   MacBook Air   \n", "266332      3976.0  FY25          FY25Q3                   MacBook Pro   \n", "266333       978.0  FY25          FY25Q3                   MacBook Air   \n", "\n", "       Quarter__c Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0              Q3                  W02       00kIS000008xlgh   \n", "1              Q3                  W13       00kIS000008xoMr   \n", "2              Q3                  W05       00kIS000008xzwF   \n", "3              Q3                  W02       00kIS000008xzwG   \n", "4              Q3                  W02       00kIS000008y06V   \n", "...           ...                  ...                   ...   \n", "266329         Q3                  W12       00kfS000000drUN   \n", "266330         Q3                  W10       00kfS000000ePcp   \n", "266331         Q3                  W10       00kfS000000ePcq   \n", "266332         Q3                  W10       00kfS000000ePwF   \n", "266333         Q3                  W10       00kfS000000eSVR   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MX2T3CH/A     246.0                  CPU  ...   \n", "1                         MX2E3CH/A     144.0                  CPU  ...   \n", "2                         MX2E3CH/A      33.0                  CPU  ...   \n", "3                         MX2T3CH/A     100.0                  CPU  ...   \n", "4                         MX2E3CH/A     117.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "266329                    MC9X4CH/A       5.0                 iPad  ...   \n", "266330                    MW0X3CH/A       2.0                  CPU  ...   \n", "266331                    MC6U4CH/A       3.0                  CPU  ...   \n", "266332                    MX313CH/A       1.0                  CPU  ...   \n", "266333                    MW0W3CH/A       1.0                  CPU  ...   \n", "\n", "       Acquisition_Group__c NCR_Program__c Reseller_for_acquisition__c  \\\n", "0                      None          False                        None   \n", "1                      None          False                        None   \n", "2                      None          False                        None   \n", "3                      None          False                        None   \n", "4                      None          False                        None   \n", "...                     ...            ...                         ...   \n", "266329                 None          False                        None   \n", "266330                 None          False                        None   \n", "266331                 None          False                        None   \n", "266332                 None          False                        None   \n", "266333                 None          False                        None   \n", "\n", "       Status__c attributes.type_account  \\\n", "0           None                 Account   \n", "1           None                 Account   \n", "2           None                 Account   \n", "3           None                 Account   \n", "4           None                 Account   \n", "...          ...                     ...   \n", "266329      None                 Account   \n", "266330      None                 Account   \n", "266331      None                 Account   \n", "266332      None                 Account   \n", "266333      None                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/********...   \n", "1       /services/data/v59.0/sobjects/Account/********...   \n", "2       /services/data/v59.0/sobjects/Account/********...   \n", "3       /services/data/v59.0/sobjects/Account/********...   \n", "4       /services/data/v59.0/sobjects/Account/********...   \n", "...                                                   ...   \n", "266329  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266330  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266331  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266332  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266333  /services/data/v59.0/sobjects/Account/001fS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "266329                  User   \n", "266330                  User   \n", "266331                  User   \n", "266332                  User   \n", "266333                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \\\n", "0       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "1       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "2       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "3       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "4       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "...                                                   ...            ...   \n", "266329  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266330  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266331  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266332  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266333  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "         Name_disti/t1  \n", "0         上海索电数码科技有限公司  \n", "1         上海索电数码科技有限公司  \n", "2         上海索电数码科技有限公司  \n", "3         上海索电数码科技有限公司  \n", "4         上海索电数码科技有限公司  \n", "...                ...  \n", "266329  伟仕佳杰（重庆）科技有限公司  \n", "266330  伟仕佳杰（重庆）科技有限公司  \n", "266331  伟仕佳杰（重庆）科技有限公司  \n", "266332  伟仕佳杰（重庆）科技有限公司  \n", "266333  伟仕佳杰（重庆）科技有限公司  \n", "\n", "[266334 rows x 114 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Apple_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'Name_disti/t1'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Apple_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_disti/t1'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 23, "id": "ac201719-7833-408c-9d30-f9416455cc31", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:01.957481Z", "iopub.status.busy": "2025-06-02T23:35:01.957383Z", "iopub.status.idle": "2025-06-02T23:35:02.814314Z", "shell.execute_reply": "2025-06-02T23:35:02.814012Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>NCR_Program__c</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "      <th>T2_Reseller</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>611310.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xlgh</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>246.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>304128.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W13</td>\n", "      <td>00kIS000008xoMr</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>144.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>69696.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W05</td>\n", "      <td>00kIS000008xzwF</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>33.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>248500.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xzwG</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>100.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>247104.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008y06V</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>117.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266329</th>\n", "      <td>2920.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000drUN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266330</th>\n", "      <td>2322.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcp</td>\n", "      <td>MW0X3CH/A</td>\n", "      <td>2.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>重庆配之家科技发展有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266331</th>\n", "      <td>3483.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcq</td>\n", "      <td>MC6U4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>重庆配之家科技发展有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266332</th>\n", "      <td>3976.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePwF</td>\n", "      <td>MX313CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>重庆配之家科技发展有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266333</th>\n", "      <td>978.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000eSVR</td>\n", "      <td>MW0W3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>重庆配之家科技发展有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>266334 rows × 114 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c             Product_Family__c  \\\n", "0         611310.0  FY25          FY25Q3                   MacBook Pro   \n", "1         304128.0  FY25          FY25Q3                   MacBook Pro   \n", "2          69696.0  FY25          FY25Q3                   MacBook Pro   \n", "3         248500.0  FY25          FY25Q3                   MacBook Pro   \n", "4         247104.0  FY25          FY25Q3                   MacBook Pro   \n", "...            ...   ...             ...                           ...   \n", "266329      2920.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi   \n", "266330      2322.0  FY25          FY25Q3                   MacBook Air   \n", "266331      3483.0  FY25          FY25Q3                   MacBook Air   \n", "266332      3976.0  FY25          FY25Q3                   MacBook Pro   \n", "266333       978.0  FY25          FY25Q3                   MacBook Air   \n", "\n", "       Quarter__c Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0              Q3                  W02       00kIS000008xlgh   \n", "1              Q3                  W13       00kIS000008xoMr   \n", "2              Q3                  W05       00kIS000008xzwF   \n", "3              Q3                  W02       00kIS000008xzwG   \n", "4              Q3                  W02       00kIS000008y06V   \n", "...           ...                  ...                   ...   \n", "266329         Q3                  W12       00kfS000000drUN   \n", "266330         Q3                  W10       00kfS000000ePcp   \n", "266331         Q3                  W10       00kfS000000ePcq   \n", "266332         Q3                  W10       00kfS000000ePwF   \n", "266333         Q3                  W10       00kfS000000eSVR   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MX2T3CH/A     246.0                  CPU  ...   \n", "1                         MX2E3CH/A     144.0                  CPU  ...   \n", "2                         MX2E3CH/A      33.0                  CPU  ...   \n", "3                         MX2T3CH/A     100.0                  CPU  ...   \n", "4                         MX2E3CH/A     117.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "266329                    MC9X4CH/A       5.0                 iPad  ...   \n", "266330                    MW0X3CH/A       2.0                  CPU  ...   \n", "266331                    MC6U4CH/A       3.0                  CPU  ...   \n", "266332                    MX313CH/A       1.0                  CPU  ...   \n", "266333                    MW0W3CH/A       1.0                  CPU  ...   \n", "\n", "       NCR_Program__c Reseller_for_acquisition__c Status__c  \\\n", "0               False                        None      None   \n", "1               False                        None      None   \n", "2               False                        None      None   \n", "3               False                        None      None   \n", "4               False                        None      None   \n", "...               ...                         ...       ...   \n", "266329          False                        None      None   \n", "266330          False                        None      None   \n", "266331          False                        None      None   \n", "266332          False                        None      None   \n", "266333          False                        None      None   \n", "\n", "       attributes.type_account  \\\n", "0                      Account   \n", "1                      Account   \n", "2                      Account   \n", "3                      Account   \n", "4                      Account   \n", "...                        ...   \n", "266329                 Account   \n", "266330                 Account   \n", "266331                 Account   \n", "266332                 Account   \n", "266333                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/********...   \n", "1       /services/data/v59.0/sobjects/Account/********...   \n", "2       /services/data/v59.0/sobjects/Account/********...   \n", "3       /services/data/v59.0/sobjects/Account/********...   \n", "4       /services/data/v59.0/sobjects/Account/********...   \n", "...                                                   ...   \n", "266329  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266330  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266331  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266332  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266333  /services/data/v59.0/sobjects/Account/001fS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "266329                  User   \n", "266330                  User   \n", "266331                  User   \n", "266332                  User   \n", "266333                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \\\n", "0       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "1       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "2       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "3       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "4       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "...                                                   ...            ...   \n", "266329  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266330  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266331  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266332  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266333  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "         Name_disti/t1    T2_Reseller  \n", "0         上海索电数码科技有限公司            NaN  \n", "1         上海索电数码科技有限公司            NaN  \n", "2         上海索电数码科技有限公司            NaN  \n", "3         上海索电数码科技有限公司            NaN  \n", "4         上海索电数码科技有限公司            NaN  \n", "...                ...            ...  \n", "266329  伟仕佳杰（重庆）科技有限公司     山东亿达数码有限公司  \n", "266330  伟仕佳杰（重庆）科技有限公司  重庆配之家科技发展有限公司  \n", "266331  伟仕佳杰（重庆）科技有限公司  重庆配之家科技发展有限公司  \n", "266332  伟仕佳杰（重庆）科技有限公司  重庆配之家科技发展有限公司  \n", "266333  伟仕佳杰（重庆）科技有限公司  重庆配之家科技发展有限公司  \n", "\n", "[266334 rows x 114 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 T2_Reseller__r 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['T2_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'T2_Reseller'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['T2_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_T2_Reseller'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 24, "id": "574f3a6f-a9d8-4311-82c5-d66e5ae5cf03", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:02.815808Z", "iopub.status.busy": "2025-06-02T23:35:02.815706Z", "iopub.status.idle": "2025-06-02T23:35:03.553835Z", "shell.execute_reply": "2025-06-02T23:35:03.553547Z"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Reseller_for_acquisition__c</th>\n", "      <th>Status__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "      <th>T2_Reseller</th>\n", "      <th>Penetrated_Account</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>611310.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xlgh</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>246.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>304128.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W13</td>\n", "      <td>00kIS000008xoMr</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>144.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>69696.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W05</td>\n", "      <td>00kIS000008xzwF</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>33.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>248500.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008xzwG</td>\n", "      <td>MX2T3CH/A</td>\n", "      <td>100.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>247104.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W02</td>\n", "      <td>00kIS000008y06V</td>\n", "      <td>MX2E3CH/A</td>\n", "      <td>117.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/********...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>上海索电数码科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266329</th>\n", "      <td>2920.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>iPad Air 11in (5th Gen) WiFi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00kfS000000drUN</td>\n", "      <td>MC9X4CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266330</th>\n", "      <td>2322.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcp</td>\n", "      <td>MW0X3CH/A</td>\n", "      <td>2.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>重庆配之家科技发展有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266331</th>\n", "      <td>3483.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePcq</td>\n", "      <td>MC6U4CH/A</td>\n", "      <td>3.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>重庆配之家科技发展有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266332</th>\n", "      <td>3976.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000ePwF</td>\n", "      <td>MX313CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>重庆配之家科技发展有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266333</th>\n", "      <td>978.0</td>\n", "      <td>FY25</td>\n", "      <td>FY25Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kfS000000eSVR</td>\n", "      <td>MW0W3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001fS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>重庆配之家科技发展有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>266334 rows × 114 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c             Product_Family__c  \\\n", "0         611310.0  FY25          FY25Q3                   MacBook Pro   \n", "1         304128.0  FY25          FY25Q3                   MacBook Pro   \n", "2          69696.0  FY25          FY25Q3                   MacBook Pro   \n", "3         248500.0  FY25          FY25Q3                   MacBook Pro   \n", "4         247104.0  FY25          FY25Q3                   MacBook Pro   \n", "...            ...   ...             ...                           ...   \n", "266329      2920.0  FY25          FY25Q3  iPad Air 11in (5th Gen) WiFi   \n", "266330      2322.0  FY25          FY25Q3                   MacBook Air   \n", "266331      3483.0  FY25          FY25Q3                   MacBook Air   \n", "266332      3976.0  FY25          FY25Q3                   MacBook Pro   \n", "266333       978.0  FY25          FY25Q3                   MacBook Air   \n", "\n", "       Quarter__c Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0              Q3                  W02       00kIS000008xlgh   \n", "1              Q3                  W13       00kIS000008xoMr   \n", "2              Q3                  W05       00kIS000008xzwF   \n", "3              Q3                  W02       00kIS000008xzwG   \n", "4              Q3                  W02       00kIS000008y06V   \n", "...           ...                  ...                   ...   \n", "266329         Q3                  W12       00kfS000000drUN   \n", "266330         Q3                  W10       00kfS000000ePcp   \n", "266331         Q3                  W10       00kfS000000ePcq   \n", "266332         Q3                  W10       00kfS000000ePwF   \n", "266333         Q3                  W10       00kfS000000eSVR   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MX2T3CH/A     246.0                  CPU  ...   \n", "1                         MX2E3CH/A     144.0                  CPU  ...   \n", "2                         MX2E3CH/A      33.0                  CPU  ...   \n", "3                         MX2T3CH/A     100.0                  CPU  ...   \n", "4                         MX2E3CH/A     117.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "266329                    MC9X4CH/A       5.0                 iPad  ...   \n", "266330                    MW0X3CH/A       2.0                  CPU  ...   \n", "266331                    MC6U4CH/A       3.0                  CPU  ...   \n", "266332                    MX313CH/A       1.0                  CPU  ...   \n", "266333                    MW0W3CH/A       1.0                  CPU  ...   \n", "\n", "       Reseller_for_acquisition__c Status__c attributes.type_account  \\\n", "0                             None      None                 Account   \n", "1                             None      None                 Account   \n", "2                             None      None                 Account   \n", "3                             None      None                 Account   \n", "4                             None      None                 Account   \n", "...                            ...       ...                     ...   \n", "266329                        None      None                 Account   \n", "266330                        None      None                 Account   \n", "266331                        None      None                 Account   \n", "266332                        None      None                 Account   \n", "266333                        None      None                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/********...   \n", "1       /services/data/v59.0/sobjects/Account/********...   \n", "2       /services/data/v59.0/sobjects/Account/********...   \n", "3       /services/data/v59.0/sobjects/Account/********...   \n", "4       /services/data/v59.0/sobjects/Account/********...   \n", "...                                                   ...   \n", "266329  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266330  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266331  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266332  /services/data/v59.0/sobjects/Account/001fS000...   \n", "266333  /services/data/v59.0/sobjects/Account/001fS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "266329                  User   \n", "266330                  User   \n", "266331                  User   \n", "266332                  User   \n", "266333                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \\\n", "0       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "1       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "2       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "3       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "4       /services/data/v59.0/sobjects/User/***********...   <PERSON><PERSON><PERSON>   \n", "...                                                   ...            ...   \n", "266329  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266330  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266331  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266332  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "266333  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "         Name_disti/t1    T2_Reseller Penetrated_Account  \n", "0         上海索电数码科技有限公司            NaN                NaN  \n", "1         上海索电数码科技有限公司            NaN                NaN  \n", "2         上海索电数码科技有限公司            NaN                NaN  \n", "3         上海索电数码科技有限公司            NaN                NaN  \n", "4         上海索电数码科技有限公司            NaN                NaN  \n", "...                ...            ...                ...  \n", "266329  伟仕佳杰（重庆）科技有限公司     山东亿达数码有限公司                NaN  \n", "266330  伟仕佳杰（重庆）科技有限公司  重庆配之家科技发展有限公司                NaN  \n", "266331  伟仕佳杰（重庆）科技有限公司  重庆配之家科技发展有限公司                NaN  \n", "266332  伟仕佳杰（重庆）科技有限公司  重庆配之家科技发展有限公司                NaN  \n", "266333  伟仕佳杰（重庆）科技有限公司  重庆配之家科技发展有限公司                NaN  \n", "\n", "[266334 rows x 114 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# 241209添加penetrated account 修正\n", "# 将 Penetrated_Account__r 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Penetrated_Account__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'Penetrated_Account'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Penetrated_Account__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_Penetrated_Account'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": 25, "id": "dbf7958d-5833-4b75-bb44-bb99fff2169d", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:03.555298Z", "iopub.status.busy": "2025-06-02T23:35:03.555196Z", "iopub.status.idle": "2025-06-02T23:35:03.557432Z", "shell.execute_reply": "2025-06-02T23:35:03.557175Z"}}, "outputs": [], "source": ["# 删除列名中的 '__c' 并替换 '_' 为 ' '\n", "df_opportunity_line_items.columns = df_opportunity_line_items.columns.str.replace('__c$', '', regex=True).str.replace('_', ' ')"]}, {"cell_type": "code", "execution_count": 26, "id": "6548de6a-ff70-4439-ab02-d40aaf5e5805", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:03.558746Z", "iopub.status.busy": "2025-06-02T23:35:03.558659Z", "iopub.status.idle": "2025-06-02T23:35:03.560476Z", "shell.execute_reply": "2025-06-02T23:35:03.560238Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Revenue', 'FY', 'ST FYQuarter', 'Product Family', 'Quarter', 'Sell Through Week', 'Oppty Line Item ID', 'Marketing Part Number MPN', 'Quantity', 'Line of business2', 'attributes.type original', 'attributes.url original', 'attributes', 'use case', 'Project Type', 'Sold To ID', 'Opportunity Reseller Apple ID', 'Apple HQ ID', 'Opportunity Reseller Track', 'ESC Store', 'Opportunity Type', 'leasingornot', 'Opportunity ID', 'Name original', 'Probability', 'JD Appended', 'Account Group', 'Group Province', 'Group City', 'Province', 'City', 'Sub Segment', 'Vertical Industry', 'zhan<PERSON><PERSON>jian', 'Mac as Choice start time', 'FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY22Q1', 'FY25Q3 Top Account', 'FY25Q2 Top Account', 'FY25Q1 Top Account', 'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account', 'FY25Q3 AE', 'FY25Q2 AE', 'FY25Q1 AE', 'FY24Q4 AE', 'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE', 'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE', 'FY25Q3 AEM', 'FY25Q2 AEM', 'FY25Q1 AEM', 'FY24Q4 AEM', 'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM', 'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM', 'FY21Q2 AE', 'FY21Q2 AEM', 'FY21Q3 AE', 'FY21Q3 AEM', 'FY21Q4 AE', 'FY21Q4 AEM', 'FY22Q1 AE', 'FY22Q1 AEM', 'Segment', 'Large Account', 'Total Mac Demand', 'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Industry Target Account', 'Source Detail', 'Sales Region', 'Name account', 'Account ID', 'Mac as Choice', 'Top Account Deep Dive', 'Enroll Date', 'Acquisition Group', 'NCR Program', 'Reseller for acquisition', 'Status', 'attributes.type account', 'attributes.url account', 'Owner.attributes.type', 'Owner.attributes.url', 'Owner.Name', 'Name disti/t1', 'T2 Reseller', 'Penetrated Account']\n"]}], "source": ["print(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 27, "id": "f2534630-014b-4769-bb99-248ef0f71315", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:03.561675Z", "iopub.status.busy": "2025-06-02T23:35:03.561587Z", "iopub.status.idle": "2025-06-02T23:35:03.564140Z", "shell.execute_reply": "2025-06-02T23:35:03.563940Z"}}, "outputs": [], "source": ["# 240812新增rename 'Name original':'Opportunity Name'\n", "# 241015新增一列use case 并rename为使用场景\n", "df_opportunity_line_items.rename(columns={'Name account':'Account Name','Owner.Name':'Account Owner','Apple HQ ID':'Apple ID','Project Type':'Deal type',\n", "    'Name disti/t1':'Disti/T1 Reseller','FY22Q1':'FY22Q1 Large Account',\n", "                                          'leasingornot':'Leasing or Not','Line of business2':'Line of Business','zhanbaoshijian':'Mac as Choice加入时间',\n", "                                         'Mac as Choice start time':'Mac as Choice start time','Marketing Part Number MPN':'Marketing Part Number (MPN)','Probability':'Probability (%)',\n", "                                         'Quarter':'Product ST Quarter','Sell Through Week':'Product ST Week' ,'Revenue':'ProductLineRevenue','Province':'Province/Region',\n", "                                         'Opportunity Reseller Apple ID':'Reseller Apple ID','Opportunity Reseller Track':'Reseller Track','Name original':'Opportunity Name',\n", "                                         'Enroll Date':'NCR Enroll Date','Acquisition Group':'NCR Group','Reseller for acquisition':'NCR Reseller','Status':'NCR Status','use case':'使用场景',\n", "                                         }, inplace=True)"]}, {"cell_type": "code", "execution_count": 28, "id": "bbb2ad26-b36f-4faa-9438-a5e2f22f8c36", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:03.565327Z", "iopub.status.busy": "2025-06-02T23:35:03.565241Z", "iopub.status.idle": "2025-06-02T23:35:03.566982Z", "shell.execute_reply": "2025-06-02T23:35:03.566762Z"}}, "outputs": [], "source": ["df_header_filepath = (f'{homepath}/Library/CloudStorage/Box-Box/Planning\\ Team/Tableau\\ Auto-Refresh\\ Raw\\ Data/19\\ SalesForce\\ PPL/PPL_by_Accountname_FY21_CQ_byapi.csv').replace('\\\\','')\n", "# 对比header\n", "# df_header = pd.read_csv(df_header_filepath).head(10)\n", "# set(list(df_header)) - set(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 29, "id": "3938cf4a-3206-4c99-afcb-3ccfbe405f95", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:03.568187Z", "iopub.status.busy": "2025-06-02T23:35:03.568103Z", "iopub.status.idle": "2025-06-02T23:35:03.580948Z", "shell.execute_reply": "2025-06-02T23:35:03.580663Z"}}, "outputs": [], "source": ["# 将布尔列转换为 0 和 1\n", "bool_columns = df_opportunity_line_items.select_dtypes(include='bool').columns\n", "df_opportunity_line_items[bool_columns] = df_opportunity_line_items[bool_columns].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "9be1793a-e2f4-4247-a587-a326f4e5ccfa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 30, "id": "05a4bb15-dfa0-4a6c-8b8c-a6097ccebf58", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:03.582347Z", "iopub.status.busy": "2025-06-02T23:35:03.582275Z", "iopub.status.idle": "2025-06-02T23:35:03.837783Z", "shell.execute_reply": "2025-06-02T23:35:03.837501Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns removed successfully.\n"]}], "source": ["#240812减少要删除的列 Name original\n", "# 要删除的列列表\n", "columns_to_remove = ['JD Appended','Owner.attributes.type','Owner.attributes.url',\n", "                     'attributes','attributes.type account','attributes.type original','attributes.url account','attributes.url original'\n", "]\n", "\n", "# 尝试删除列，并在失败时捕获异常\n", "try:\n", "    df_opportunity_line_items.drop(columns=columns_to_remove, inplace=True)\n", "    print(\"Columns removed successfully.\")\n", "except KeyError as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": 31, "id": "3e337560-e2bf-42d2-94ac-a245a9192702", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:03.839269Z", "iopub.status.busy": "2025-06-02T23:35:03.839190Z", "iopub.status.idle": "2025-06-02T23:35:06.221516Z", "shell.execute_reply": "2025-06-02T23:35:06.221104Z"}, "tags": []}, "outputs": [], "source": ["df_opportunity_line_items.to_csv(df_header_filepath, index=False)"]}, {"cell_type": "code", "execution_count": 32, "id": "4ade7c72-b559-4e31-91ca-dd55827d25fd", "metadata": {"execution": {"iopub.execute_input": "2025-06-02T23:35:06.223248Z", "iopub.status.busy": "2025-06-02T23:35:06.223141Z", "iopub.status.idle": "2025-06-02T23:35:06.225419Z", "shell.execute_reply": "2025-06-02T23:35:06.225194Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["Index(['FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY22Q1 Large Account',\n", "       'FY25Q3 Top Account', 'FY25Q2 Top Account', 'FY25Q1 Top Account',\n", "       'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account',\n", "       'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account',\n", "       'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account',\n", "       'FY22Q3 Top Account', 'FY22Q2 Top Account', 'Large Account',\n", "       'Industry Target Account', 'Mac as Choice', 'Top Account Deep Dive',\n", "       'NCR Program'],\n", "      dtype='object')"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["bool_columns"]}, {"cell_type": "code", "execution_count": null, "id": "3e81c82b-6dec-435f-bd56-4cfbbfd270a2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}