{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9fe2d7fc-5aae-474d-89e5-b6d4a7b9cbb9", "metadata": {}, "outputs": [], "source": ["import os\n", "import configparser\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "9c2b9cbb-425e-4d44-bb97-f58149959be8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "1012df55-d7a4-4175-a52b-f1ee872255da", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 4, "id": "14da3bbd-dd89-4ed9-a158-53b1d557162e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQ4AQGeDDxWoMfNGG2aPz5s8wGh_so5RzUmOOpq.QShq_GE5Gc3pf9fCjxVjFsu7DHAD7kJSKSI8BoJrPSiGMN0UXdYbHInM'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 5, "id": "c6a9098b-ed8f-4fc2-aef9-637139ae8f08", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Objects used in the report: {'OPPORTUNITY_NAME', 'ACCOUNT_ID', 'Opportunity', 'PROBABILITY', 'Account', 'ACCOUNT_NAME', 'OPPORTUNITY_ID', 'QUANTITY', 'ACCOUNT_OWNER', 'User', 'OpportunityLineItem', 'FAMILY'}\n", "Fields used in the report: ['Account.Account_Group__c', 'OpportunityLineItem.Revenue__c', 'User.Sales_Region__c', 'Account.Group_Province__c', 'Account.Group_City__c', 'ACCOUNT_NAME', 'Account.Province__c', 'Account.City__c', 'Opportunity.Project_Type__c', 'Opportunity.Apple_Reseller__c', 'Opportunity.Opportunity_Reseller_Apple_ID__c', 'Opportunity.T2_Reseller__c', 'Opportunity.Apple_HQ_ID__c', 'Opportunity.Opportunity_Reseller_Track__c', 'Opportunity.ESC_Store__c', 'Account.Sub_Segment__c', 'Account.Vertical_Industry__c', 'Account.Mac_as_Choice__c', 'Account.zhanbaoshijian__c', 'Account.Mac_as_Choice_start_time__c', 'Account.Top_Account_Deep_Dive__c', 'Account.FY21Q3_Large_Account__c', 'Account.FY21Q4_Large_Account__c', 'Account.FY22Q1__c', 'Account.FY22Q2_Top_Account__c', 'Account.FY22Q3_Top_Account__c', 'Account.FY22Q4_Top_Account__c', 'Account.FY23Q1_Top_Account__c', 'Account.FY23Q2_Top_Account__c', 'Account.FY23Q3_Top_Account__c', 'Account.FY23Q4_Top_Account__c', 'Account.FY24Q1_Top_Account__c', 'Account.FY24Q2_Top_Account__c', 'Account.FY24Q3_Top_Account__c', 'Account.FY21Q2_AE__c', 'Account.FY21Q2_AEM__c', 'Account.FY21Q3_AE__c', 'Account.FY21Q3_AEM__c', 'Account.FY21Q4_AE__c', 'Account.FY21Q4_AEM__c', 'Account.FY22Q1_AE__c', 'Account.FY22Q1_AEM__c', 'Account.FY22Q2_AE__c', 'Account.FY22Q2_AEM__c', 'Account.FY22Q3_AE__c', 'Account.FY22Q3_AEM__c', 'Account.FY22Q4_AE__c', 'Account.FY22Q4_AEM__c', 'Account.FY23Q1_AE__c', 'Account.FY23Q1_AEM__c', 'Account.FY23Q2_AE__c', 'Account.FY23Q2_AEM__c', 'Account.FY23Q3_AE__c', 'Account.FY23Q3_AEM__c', 'Account.FY23Q4_AE__c', 'Account.FY23Q4_AEM__c', 'Account.FY24Q1_AE__c', 'Account.FY24Q1_AEM__c', 'Account.FY24Q2_AE__c', 'Account.FY24Q2_AEM__c', 'Account.FY24Q3_AEM__c', 'Account.FY24Q3_AE__c', 'ACCOUNT_OWNER', 'QUANTITY', 'ACCOUNT_ID', 'FAMILY', 'OpportunityLineItem.FY__c', 'OpportunityLineItem.ST_FYQuarter__c', 'OpportunityLineItem.Product_Family__c', 'OpportunityLineItem.Quarter__c', 'OpportunityLineItem.Sell_Through_Week__c', 'Opportunity.Opportunity_Type__c', 'PROBABILITY', 'Account.Segment__c', 'Account.Large_Account__c', 'OpportunityLineItem.Marketing_Part_Number_MPN__c', 'Account.PC_Install_Base__c', 'Account.Source_Detail__c', 'Account.Total_Mac_Demand__c', 'Account.Industry_Target_Account__c', 'Opportunity.Penetrated_Account__c', 'Opportunity.leasingornot__c', 'Account.FY22_Fcst__c', 'Account.FY23_Fcst__c', 'Account.Sales_Region__c', 'OPPORTUNITY_ID', 'OpportunityLineItem.Oppty_Line_Item_ID__c', 'Opportunity.Sold_To_ID__c', 'OPPORTUNITY_NAME']\n"]}], "source": ["\n", "\n", "def get_report_metadata(base_url, report_id, access_token):\n", "    url = f\"{base_url}/services/data/v59.0/analytics/reports/{report_id}/describe\"\n", "    headers = {\n", "        'Authorization': f'<PERSON><PERSON> {access_token}'\n", "    }\n", "\n", "    response = requests.get(url, headers=headers)\n", "    if response.status_code == 200:\n", "        report_metadata = response.json()\n", "        return report_metadata\n", "    else:\n", "        print(f\"Failed to get report metadata. Status code: {response.status_code}\")\n", "        return None\n", "\n", "def extract_objects_and_fields(report_metadata):\n", "    objects = set()\n", "    fields = []\n", "\n", "    # Extract detail columns\n", "    detail_columns = report_metadata.get('reportMetadata', {}).get('detailColumns', [])\n", "    for column in detail_columns:\n", "        # Split the field name to get the object and field\n", "        parts = column.split('.')\n", "        if len(parts) == 2:\n", "            objects.add(parts[0])\n", "        elif len(parts) == 1:\n", "            objects.add(parts[0])  # Treat single-part names as objects\n", "        fields.append(column)  # Always add the column to fields\n", "\n", "    return objects, fields\n", "\n", "# Replace these values with your actual Salesforce instance URL, report ID, and access token\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "# report_id = \"00OIS000001TEOk\"\n", "report_id = \"00OIS000001TPCY\"\n", "# access_token = \"your_access_token_here\"  # Use your actual access token\n", "\n", "report_metadata = get_report_metadata(base_url, report_id, access_token)\n", "if report_metadata:\n", "    objects, fields = extract_objects_and_fields(report_metadata)\n", "    print(f\"Objects used in the report: {objects}\")\n", "    print(f\"Fields used in the report: {fields}\")\n", "else:\n", "    print(\"Failed to get report metadata.\")"]}, {"cell_type": "code", "execution_count": 6, "id": "a80eb8e4-e8cb-457f-8db7-1fb8b366967e", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["89"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(fields)"]}, {"cell_type": "code", "execution_count": 190, "id": "1dbb7d8c-2856-48f1-8f66-a804b92432ad", "metadata": {}, "outputs": [], "source": ["\n", "# 已被修改, 弃用\n", "# def get_report_metadata(base_url, report_id, access_token):\n", "#     url = f\"{base_url}/services/data/v59.0/analytics/reports/{report_id}/describe\"\n", "#     headers = {\n", "#         'Authorization': f'<PERSON><PERSON> {access_token}'\n", "#     }\n", "\n", "#     response = requests.get(url, headers=headers)\n", "#     if response.status_code == 200:\n", "#         report_metadata = response.json()\n", "#         return report_metadata\n", "#     else:\n", "#         print(f\"Failed to get report metadata. Status code: {response.status_code}\")\n", "#         return None\n", "\n", "# def extract_objects_and_fields(report_metadata):\n", "#     objects = set()\n", "#     fields = []\n", "\n", "#     # Extract detail columns\n", "#     detail_columns = report_metadata.get('reportMetadata', {}).get('detailColumns', [])\n", "#     for column in detail_columns:\n", "#         # Split the field name to get the object and field\n", "#         parts = column.split('.')\n", "#         if len(parts) == 2:\n", "#             objects.add(parts[0])\n", "#             fields.append(column)\n", "\n", "#     return objects, fields\n", "\n", "# # Replace these values with your actual Salesforce instance URL, report ID, and access token\n", "# base_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "# report_id = \"00OIS000001TEOk\"\n", "# # access_token = access_token\n", "\n", "# report_metadata = get_report_metadata(base_url, report_id, access_token)\n", "# if report_metadata:\n", "#     objects, fields = extract_objects_and_fields(report_metadata)\n", "#     print(f\"Objects used in the report: {objects}\")\n", "#     print(f\"Fields used in the report: {fields}\")\n", "# else:\n", "#     print(\"Failed to get report metadata.\")\n"]}, {"cell_type": "code", "execution_count": 191, "id": "13971090-3a2e-4f80-a044-9810278b0b39", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "# 已被修改, 弃用\n", "# import requests\n", "# import json\n", "\n", "# def get_report_metadata(base_url, report_id, access_token):\n", "#     url = f\"{base_url}/services/data/v59.0/analytics/reports/{report_id}/describe\"\n", "#     headers = {\n", "#         'Authorization': f'<PERSON><PERSON> {access_token}'\n", "#     }\n", "\n", "#     response = requests.get(url, headers=headers)\n", "#     if response.status_code == 200:\n", "#         report_metadata = response.json()\n", "#         return report_metadata\n", "#     else:\n", "#         print(f\"Failed to get report metadata. Status code: {response.status_code}\")\n", "#         return None\n", "\n", "# def extract_objects_and_fields(report_metadata):\n", "#     objects = set()\n", "#     fields = []\n", "\n", "#     # Extract detail columns\n", "#     detail_columns = report_metadata.get('reportMetadata', {}).get('detailColumns', [])\n", "#     for column in detail_columns:\n", "#         # Split the field name to get the object and field\n", "#         parts = column.split('.')\n", "#         if len(parts) == 2:\n", "#             objects.add(parts[0])\n", "#             fields.append(column)\n", "\n", "#     # Add additional fields manually\n", "#     additional_fields = {\n", "#         'Account.Name': 'Account',\n", "#         'Account.OwnerId': 'Account',\n", "#         'OpportunityLineItem.Quantity': 'OpportunityLineItem',\n", "#         'Account.Id': 'Account',\n", "#         'Account.Line_of_Business__c': 'Account',  # Assuming it's a custom field\n", "#         'Opportunity.Id': 'Opportunity',\n", "#         'Opportunity.Name': 'Opportunity',\n", "#         'Opportunity.Probability': 'Opportunity'\n", "#     }\n", "\n", "#     for field, obj in additional_fields.items():\n", "#         if field not in fields:\n", "#             fields.append(field)\n", "#             objects.add(obj)\n", "\n", "#     return objects, fields\n", "\n", "# # Replace these values with your actual Salesforce instance URL, report ID, and access token\n", "# base_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "# report_id = \"00OIS000001TEOk\"\n", "# # access_token = \"your_access_token_here\"  # Replace with your actual access token\n", "\n", "# report_metadata = get_report_metadata(base_url, report_id, access_token)\n", "# if report_metadata:\n", "#     objects, fields = extract_objects_and_fields(report_metadata)\n", "#     print(f\"Objects used in the report: {objects}\")\n", "#     print(f\"Fields used in the report: {fields}\")\n", "# else:\n", "#     print(\"Failed to get report metadata.\")"]}, {"cell_type": "code", "execution_count": 69, "id": "5fdabd9e-cf34-4eb2-b9ea-e13b8b90be99", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "# # for check keyong\n", "# # Initial URL and headers\n", "# base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# # access_token = \"YOUR_ACCESS_TOKEN\"  # Replace with your actual access token\n", "# headers = {\n", "#     'Authorization': f'Bear<PERSON> {access_token}',\n", "#     'Content-Type': 'application/json'\n", "# }\n", "\n", "# def get_salesforce_data(url):\n", "#     response = requests.get(url, headers=headers)\n", "#     response.raise_for_status()  # Ensure we raise an error for bad responses\n", "#     data = response.json()\n", "#     return data\n", "\n", "# # SOQL query\n", "\n", "\n", "# sql_statement = \"\"\"\n", "# SELECT \n", "#     Account.Account_Group__c, \n", "#     Account.Group_Province__c, \n", "#     Account.Group_City__c, \n", "#     Account.Province__c, \n", "#     Account.City__c, \n", "#     Opportunity.Project_Type__c, \n", "#     Opportunity.Apple_Reseller__c, \n", "#     Opportunity.Sold_To_ID__c, \n", "#     Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "#     Opportunity.T2_Reseller__c, \n", "#     Opportunity.Apple_HQ_ID__c, \n", "#     Opportunity.Opportunity_Reseller_Track__c, \n", "#     Opportunity.ESC_Store__c, \n", "#     Account.Sub_Segment__c, \n", "#     Account.Vertical_Industry__c, \n", "#     Account.<PERSON>_as_Choice__c, \n", "#     Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c, \n", "#     Account.<PERSON>_as_Choice_start_time__c, \n", "#     Account.Top_Account_Deep_Dive__c, \n", "#     Account.FY21Q3_Large_Account__c, \n", "#     Account.FY21Q4_Large_Account__c, \n", "#     Account.FY22Q1__c, \n", "#     Account.FY22Q2_Top_Account__c, \n", "#     Account.FY22Q3_Top_Account__c, \n", "#     Account.FY22Q4_Top_Account__c, \n", "#     Account.FY23Q1_Top_Account__c, \n", "#     Account.FY23Q2_Top_Account__c, \n", "#     Account.FY23Q3_Top_Account__c, \n", "#     Account.FY23Q4_Top_Account__c, \n", "#     Account.FY24Q1_Top_Account__c, \n", "#     Account.FY24Q2_Top_Account__c, \n", "#     Account.FY24Q3_Top_Account__c, \n", "#     Account.FY21Q2_AE__c, \n", "#     Account.FY21Q2_AEM__c, \n", "#     Account.FY21Q3_AE__c, \n", "#     Account.FY21Q3_AEM__c, \n", "#     Account.FY21Q4_AE__c, \n", "#     Account.FY21Q4_AEM__c, \n", "#     Account.FY22Q1_AE__c, \n", "#     Account.FY22Q1_AEM__c, \n", "#     Account.FY22Q2_AE__c, \n", "#     Account.FY22Q2_AEM__c, \n", "#     Account.FY22Q3_AE__c, \n", "#     Account.FY22Q3_AEM__c, \n", "#     Account.FY22Q4_AE__c, \n", "#     Account.FY22Q4_AEM__c, \n", "#     Account.FY23Q1_AE__c, \n", "#     Account.FY23Q1_AEM__c, \n", "#     Account.FY23Q2_AE__c, \n", "#     Account.FY23Q2_AEM__c, \n", "#     Account.FY23Q3_AE__c, \n", "#     Account.FY23Q3_AEM__c, \n", "#     Account.FY23Q4_AE__c, \n", "#     Account.FY23Q4_AEM__c, \n", "#     Account.FY24Q1_AE__c, \n", "#     Account.FY24Q1_AEM__c, \n", "#     Account.FY24Q2_AE__c, \n", "#     Account.FY24Q2_AEM__c, \n", "#     Account.FY24Q3_AE__c, \n", "#     Account.FY24Q3_AEM__c, \n", "#     Opportunity.Opportunity_Type__c, \n", "#     Account.Segment__c, \n", "#     Account.Large_Account__c, \n", "#     Account.Total_Mac_Demand__c, \n", "#     Account.PC_Install_Base__c, \n", "#     Account.FY22_Fcst__c, \n", "#     Account.FY23_Fcst__c, \n", "#     Opportunity.leasingornot__c, \n", "#     Account.Industry_Target_Account__c, \n", "#     Opportunity.Penetrated_Account__c, \n", "#     Account.Source_Detail__c, \n", "#     Account.Sales_Region__c,\n", "#     (SELECT \n", "#         Revenue__c, \n", "#         FY__c, \n", "#         ST_FYQuarter__c, \n", "#         Product_Family__c, \n", "#         Quarter__c, \n", "#         Sell_Through_Week__c, \n", "#         Oppty_Line_Item_ID__c, \n", "#         Marketing_Part_Number_MPN__c \n", "#      FROM OpportunityLineItems)\n", "# FROM Opportunity\n", "# WHERE AccountId != NULL\n", "# LIMIT 10\n", "# \"\"\"\n", "\n", "# # Remove newlines and extra spaces from SQL statement\n", "# sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")\n", "\n", "# # Construct the full URL\n", "# url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# # Fetch initial data\n", "# data = get_salesforce_data(url)\n", "\n", "# # Collect all records\n", "# all_records = data['records']\n", "\n", "# # Check for more data\n", "# while not data['done']:\n", "#     next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "#     data = get_salesforce_data(next_url)\n", "#     print(next_url)\n", "#     all_records.extend(data['records'])\n", "\n", "# # Print all records\n", "# # print(json.dumps(all_records, indent=4))\n", "\n", "# # # Optionally, save to a file\n", "# # with open('salesforce_data.json', 'w') as f:\n", "# #     json.dump(all_records, f, indent=4)\n", "\n", "# # print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 281, "id": "10429a7b-5f43-423a-aab7-10d5ab7959ab", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "# for check keyong\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"YOUR_ACCESS_TOKEN\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "# SOQL query\n", "\n", "\n", "sql_statement = \"\"\"\n", "SELECT \n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "    Opportunity.Apple_Reseller__c, \n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__c, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "    Account.<PERSON>_as_Choice__c, \n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "    Account.Top_Account_Deep_Dive__c, \n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY22Q2_Top_Account__c, \n", "    Account.FY22Q3_Top_Account__c, \n", "    Account.FY22Q4_Top_Account__c, \n", "    Account.FY23Q1_Top_Account__c, \n", "    Account.FY23Q2_Top_Account__c, \n", "    Account.FY23Q3_Top_Account__c, \n", "    Account.FY23Q4_Top_Account__c, \n", "    Account.FY24Q1_Top_Account__c, \n", "    Account.FY24Q2_Top_Account__c, \n", "    Account.FY24Q3_Top_Account__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c, \n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Account.FY22Q2_AE__c, \n", "    Account.FY22Q2_AEM__c, \n", "    Account.FY22Q3_AE__c, \n", "    Account.FY22Q3_AEM__c, \n", "    Account.FY22Q4_AE__c, \n", "    Account.FY22Q4_AEM__c, \n", "    Account.FY23Q1_AE__c, \n", "    Account.FY23Q1_AEM__c, \n", "    Account.FY23Q2_AE__c, \n", "    Account.FY23Q2_AEM__c, \n", "    Account.FY23Q3_AE__c, \n", "    Account.FY23Q3_AEM__c, \n", "    Account.FY23Q4_AE__c, \n", "    Account.FY23Q4_AEM__c, \n", "    Account.FY24Q1_AE__c, \n", "    Account.FY24Q1_AEM__c, \n", "    Account.FY24Q2_AE__c, \n", "    Account.FY24Q2_AEM__c, \n", "    Account.FY24Q3_AE__c, \n", "    Account.FY24Q3_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__c, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Opportunity.Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.OPPORTUNITY_ID__c,\n", "    Opportunity.Name,\n", "    (SELECT \n", "        Revenue__c, \n", "        FY__c, \n", "        ST_FYQuarter__c, \n", "        Product_Family__c,\n", "        Quarter__c, \n", "        Sell_Through_Week__c, \n", "        Oppty_Line_Item_ID__c, \n", "        Marketing_Part_Number_MPN__c,\n", "        Quantity,\n", "        Line_of_business2__c\n", "     FROM OpportunityLineItems)\n", "FROM Opportunity\n", "WHERE Account.Name != NULL \n", "LIMIT 10\n", "\"\"\"\n", "\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 203, "id": "96c0b30a-6cc3-454f-ad1a-b5285bc51da1", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "# for check keyong\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"YOUR_ACCESS_TOKEN\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "# SOQL query\n", "\n", "\n", "sql_statement =\"\"\"\n", "SELECT \n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "    Opportunity.Apple_Reseller__c, \n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__c, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "    Account.<PERSON>_as_Choice__c, \n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "    Account.Top_Account_Deep_Dive__c, \n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY22Q2_Top_Account__c, \n", "    Account.FY22Q3_Top_Account__c, \n", "    Account.FY22Q4_Top_Account__c, \n", "    Account.FY23Q1_Top_Account__c, \n", "    Account.FY23Q2_Top_Account__c, \n", "    Account.FY23Q3_Top_Account__c, \n", "    Account.FY23Q4_Top_Account__c, \n", "    Account.FY24Q1_Top_Account__c, \n", "    Account.FY24Q2_Top_Account__c, \n", "    Account.FY24Q3_Top_Account__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c, \n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Account.FY22Q2_AE__c, \n", "    Account.FY22Q2_AEM__c, \n", "    Account.FY22Q3_AE__c, \n", "    Account.FY22Q3_AEM__c, \n", "    Account.FY22Q4_AE__c, \n", "    Account.FY22Q4_AEM__c, \n", "    Account.FY23Q1_AE__c, \n", "    Account.FY23Q1_AEM__c, \n", "    Account.FY23Q2_AE__c, \n", "    Account.FY23Q2_AEM__c, \n", "    Account.FY23Q3_AE__c, \n", "    Account.FY23Q3_AEM__c, \n", "    Account.FY23Q4_AE__c, \n", "    Account.FY23Q4_AEM__c, \n", "    Account.FY24Q1_AE__c, \n", "    Account.FY24Q1_AEM__c, \n", "    Account.FY24Q2_AE__c, \n", "    Account.FY24Q2_AEM__c, \n", "    Account.FY24Q3_AE__c, \n", "    Account.FY24Q3_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__c, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.Opportunity_ID__c, \n", "    Opportunity.Name,\n", "    Opportunity.Probability\n", "\n", "FROM Opportunity\n", "WHERE AccountId != NULL \n", "limit 10\n", "\"\"\"\n", "\n", "\n", "\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 269, "id": "5fc8a1bb-fb2c-4387-8f6e-de54838c6094", "metadata": {"tags": []}, "outputs": [], "source": ["# Normalize JSON data\n", "df_opportunity = pd.json_normalize(all_records)\n", "\n"]}, {"cell_type": "code", "execution_count": 270, "id": "974acaa1-4107-4772-a006-ce6f08cde908", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["['Project_Type__c',\n", " 'Apple_Reseller__c',\n", " 'Sold_To_ID__c',\n", " 'Opportunity_Reseller_Apple_ID__c',\n", " 'T2_Reseller__c',\n", " 'Apple_HQ_ID__c',\n", " 'Opportunity_Reseller_Track__c',\n", " 'ESC_Store__c',\n", " 'Opportunity_Type__c',\n", " 'leasingornot__c',\n", " 'Penetrated_Account__c',\n", " 'Opportunity_ID__c',\n", " 'Name',\n", " 'attributes.type',\n", " 'attributes.url',\n", " 'Account.attributes.type',\n", " 'Account.attributes.url',\n", " 'Account.Account_Group__c',\n", " 'Account.Group_Province__c',\n", " 'Account.Group_City__c',\n", " 'Account.Province__c',\n", " 'Account.City__c',\n", " 'Account.Sub_Segment__c',\n", " 'Account.Vertical_Industry__c',\n", " 'Account.<PERSON>_as_Choice__c',\n", " 'Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c',\n", " 'Account.<PERSON>_as_Choice_start_time__c',\n", " 'Account.Top_Account_Deep_Dive__c',\n", " 'Account.FY21Q3_Large_Account__c',\n", " 'Account.FY21Q4_Large_Account__c',\n", " 'Account.FY22Q1__c',\n", " 'Account.FY22Q2_Top_Account__c',\n", " 'Account.FY22Q3_Top_Account__c',\n", " 'Account.FY22Q4_Top_Account__c',\n", " 'Account.FY23Q1_Top_Account__c',\n", " 'Account.FY23Q2_Top_Account__c',\n", " 'Account.FY23Q3_Top_Account__c',\n", " 'Account.FY23Q4_Top_Account__c',\n", " 'Account.FY24Q1_Top_Account__c',\n", " 'Account.FY24Q2_Top_Account__c',\n", " 'Account.FY24Q3_Top_Account__c',\n", " 'Account.FY21Q2_AE__c',\n", " 'Account.FY21Q2_AEM__c',\n", " 'Account.FY21Q3_AE__c',\n", " 'Account.FY21Q3_AEM__c',\n", " 'Account.FY21Q4_AE__c',\n", " 'Account.FY21Q4_AEM__c',\n", " 'Account.FY22Q1_AE__c',\n", " 'Account.FY22Q1_AEM__c',\n", " 'Account.FY22Q2_AE__c',\n", " 'Account.FY22Q2_AEM__c',\n", " 'Account.FY22Q3_AE__c',\n", " 'Account.FY22Q3_AEM__c',\n", " 'Account.FY22Q4_AE__c',\n", " 'Account.FY22Q4_AEM__c',\n", " 'Account.FY23Q1_AE__c',\n", " 'Account.FY23Q1_AEM__c',\n", " 'Account.FY23Q2_AE__c',\n", " 'Account.FY23Q2_AEM__c',\n", " 'Account.FY23Q3_AE__c',\n", " 'Account.FY23Q3_AEM__c',\n", " 'Account.FY23Q4_AE__c',\n", " 'Account.FY23Q4_AEM__c',\n", " 'Account.FY24Q1_AE__c',\n", " 'Account.FY24Q1_AEM__c',\n", " 'Account.FY24Q2_AE__c',\n", " 'Account.FY24Q2_AEM__c',\n", " 'Account.FY24Q3_AE__c',\n", " 'Account.FY24Q3_AEM__c',\n", " 'Account.Segment__c',\n", " 'Account.Large_Account__c',\n", " 'Account.Total_Mac_Demand__c',\n", " 'Account.PC_Install_Base__c',\n", " 'Account.FY22_Fcst__c',\n", " 'Account.FY23_Fcst__c',\n", " 'Account.Industry_Target_Account__c',\n", " 'Account.Source_Detail__c',\n", " 'Account.Sales_Region__c',\n", " 'Account.Name',\n", " 'Account.Owner.attributes.type',\n", " 'Account.Owner.attributes.url',\n", " 'Account.Owner.Name',\n", " 'Account.Account_ID__c',\n", " 'OpportunityLineItems.totalSize',\n", " 'OpportunityLineItems.done',\n", " 'OpportunityLineItems.records']"]}, "execution_count": 270, "metadata": {}, "output_type": "execute_result"}], "source": ["list(df_opportunity)"]}, {"cell_type": "code", "execution_count": 185, "id": "063e834d-369d-49c0-a22e-906e8807dc73", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "\n", "# # Initial URL and headers\n", "# base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# # access_token = \"YOUR_ACCESS_TOKEN\"  # Replace with your actual access token\n", "# headers = {\n", "#     'Authorization': f'Bear<PERSON> {access_token}',\n", "#     'Content-Type': 'application/json'\n", "# }\n", "\n", "# def get_salesforce_data(url):\n", "#     response = requests.get(url, headers=headers)\n", "#     response.raise_for_status()  # Ensure we raise an error for bad responses\n", "#     data = response.json()\n", "#     return data\n", "\n", "# # SOQL query\n", "# sql_statement = \"\"\"\n", "# SELECT Account.FY21Q2_Identifier__c, Account.FY24Q3_Identifier__c, Account.FY22Q1__c,\n", "# Account.FY21Q3_Large_Account__c, Account.FY21Q4_Large_Account__c, Account.FY24Q2_Top_Account__c,\n", "# Account.FY22Q2_Top_Account__c, Account.FY22Q3_Top_Account__c, Account.FY22Q4_Top_Account__c,\n", "# Account.FY23Q1_Top_Account__c, Account.FY23Q2_Top_Account__c, Account.FY23Q3_Top_Account__c,\n", "# Account.FY23Q4_Top_Account__c, Account.FY24Q1_Top_Account__c, Account.FY24Q3_Top_Account__c,\n", "# Account.FY24Q2_AE__c, Account.FY24Q2_AEM__c, Account.FY24Q3_AE__c, Account.FY24Q3_AEM__c,\n", "# Account.<PERSON>_as_Choice__c, Account.Mac_as_Choice_start_time__c, Account.SEI_maC__c, ACCOUNT.NAME,\n", "# Account.SEI_Account_ID__c, Account.SEI_Cluster_ID__c, Account.Account_Cluster__c,\n", "# Account.Is_Group__c, Account_ID__c,Account.Account_Group__c, Account.SEI_Group_ID__c, Account.Account_Group_ID__c,\n", "# Account.Group_Sub_Segment__c, Account.Group_Vertical_Industry__c, Account.Province__c,\n", "# Account.City__c, Account.Group_Province__c, Account.Group_City__c, Owner.Name, Account.Sub_Segment__c,\n", "# Account.Vertical_Industry__c, Account.Top_Account_Deep_Dive__c FROM Account \n", "# \"\"\"\n", "\n", "# # Remove newlines and extra spaces from SQL statement\n", "# sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")\n", "\n", "# # Construct the full URL\n", "# url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# # Fetch initial data\n", "# data = get_salesforce_data(url)\n", "\n", "# # Collect all records\n", "# all_records = data['records']\n", "\n", "# # Check for more data\n", "# while not data['done']:\n", "#     next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "#     data = get_salesforce_data(next_url)\n", "#     print(next_url)\n", "#     all_records.extend(data['records'])\n", "\n", "# # Print all records\n", "# # print(json.dumps(all_records, indent=4))\n", "\n", "# # # Optionally, save to a file\n", "# # with open('salesforce_data.json', 'w') as f:\n", "# #     json.dump(all_records, f, indent=4)\n", "\n", "# # print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 282, "id": "bcf01baf-7d5c-4fa2-9642-c3295dd02081", "metadata": {}, "outputs": [], "source": ["# Convert to DataFrame\n", "df = pd.json_normalize(all_records)\n"]}, {"cell_type": "code", "execution_count": 283, "id": "d0c5274b-e782-49d1-bee3-d10fdb4cb69f", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Project_Type__c</th>\n", "      <th>Apple_Reseller__c</th>\n", "      <th>Sold_To_ID__c</th>\n", "      <th>Opportunity_Reseller_Apple_ID__c</th>\n", "      <th>T2_Reseller__c</th>\n", "      <th>Apple_HQ_ID__c</th>\n", "      <th>Opportunity_Reseller_Track__c</th>\n", "      <th>ESC_Store__c</th>\n", "      <th>Opportunity_Type__c</th>\n", "      <th>leasingornot__c</th>\n", "      <th>...</th>\n", "      <th>Account.Source_Detail__c</th>\n", "      <th>Account.Sales_Region__c</th>\n", "      <th>Account.Name</th>\n", "      <th>Account.Owner.attributes.type</th>\n", "      <th>Account.Owner.attributes.url</th>\n", "      <th>Account.Owner.Name</th>\n", "      <th>Account.Account_ID__c</th>\n", "      <th>OpportunityLineItems.totalSize</th>\n", "      <th>OpportunityLineItems.done</th>\n", "      <th>OpportunityLineItems.records</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Deal</td>\n", "      <td>00590000001ynuBAAQ</td>\n", "      <td>817727</td>\n", "      <td>757934</td>\n", "      <td>None</td>\n", "      <td>757934</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>北京爱奇艺科技有限公司上海分公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0016F00002KgHKI</td>\n", "      <td>9</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Deal</td>\n", "      <td>00590000001ynuBAAQ</td>\n", "      <td>817727</td>\n", "      <td>757934</td>\n", "      <td>None</td>\n", "      <td>757934</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>北京爱奇艺科技有限公司上海分公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0016F00002KgHKI</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Deal</td>\n", "      <td>***********lrkSAAQ</td>\n", "      <td>1015960</td>\n", "      <td>544132</td>\n", "      <td>None</td>\n", "      <td>544132</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>北京高思博乐教育科技股份有限公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>0016F00003lHScr</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Deal</td>\n", "      <td>***********lrkSAAQ</td>\n", "      <td>1015960</td>\n", "      <td>544132</td>\n", "      <td>None</td>\n", "      <td>544132</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>北京高思博乐教育科技股份有限公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>0016F00003lHScr</td>\n", "      <td>3</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Deal</td>\n", "      <td>***********3I3cAAE</td>\n", "      <td>552790</td>\n", "      <td>229187</td>\n", "      <td>None</td>\n", "      <td>229187</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>盛趣信息技术（上海）有限公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>00190000003r26q</td>\n", "      <td>3</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Deal</td>\n", "      <td>***********3I3cAAE</td>\n", "      <td>552790</td>\n", "      <td>229187</td>\n", "      <td>None</td>\n", "      <td>229187</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>盛趣信息技术（上海）有限公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>00190000003r26q</td>\n", "      <td>1</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Deal</td>\n", "      <td>***********3I3cAAE</td>\n", "      <td>552790</td>\n", "      <td>229187</td>\n", "      <td>None</td>\n", "      <td>229187</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>盛趣信息技术（上海）有限公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>00190000003r26q</td>\n", "      <td>3</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Deal</td>\n", "      <td>***********3I3cAAE</td>\n", "      <td>552790</td>\n", "      <td>229187</td>\n", "      <td>None</td>\n", "      <td>229187</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>Gifting</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>盛趣信息技术（上海）有限公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>00190000003r26q</td>\n", "      <td>4</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>1645656</td>\n", "      <td>a1R6F00000JxTNnUAN</td>\n", "      <td>805939</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>北京格物时代科技发展有限公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>0016F00003kdtLg</td>\n", "      <td>5</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>759884</td>\n", "      <td>a1R6F00000JxTNYUA3</td>\n", "      <td>805939</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Gifting</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>北京森隆科技有限公司</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>00190000004PT9s</td>\n", "      <td>1</td>\n", "      <td>True</td>\n", "      <td>[{'attributes': {'type': 'OpportunityLineItem'...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 86 columns</p>\n", "</div>"], "text/plain": ["  Project_Type__c   Apple_Reseller__c Sold_To_ID__c  \\\n", "0            Deal  00590000001ynuBAAQ        817727   \n", "1            Deal  00590000001ynuBAAQ        817727   \n", "2            Deal  ***********lrkSAAQ       1015960   \n", "3            Deal  ***********lrkSAAQ       1015960   \n", "4            Deal  ***********3I3cAAE        552790   \n", "5            Deal  ***********3I3cAAE        552790   \n", "6            Deal  ***********3I3cAAE        552790   \n", "7            Deal  ***********3I3cAAE        552790   \n", "8            Deal  ***********2au6AAA        829185   \n", "9            Deal  ***********2au6AAA        829185   \n", "\n", "  Opportunity_Reseller_Apple_ID__c      T2_Reseller__c Apple_HQ_ID__c  \\\n", "0                           757934                None         757934   \n", "1                           757934                None         757934   \n", "2                           544132                None         544132   \n", "3                           544132                None         544132   \n", "4                           229187                None         229187   \n", "5                           229187                None         229187   \n", "6                           229187                None         229187   \n", "7                           229187                None         229187   \n", "8                          1645656  a1R6F00000JxTNnUAN         805939   \n", "9                           759884  a1R6F00000JxTNYUA3         805939   \n", "\n", "  Opportunity_Reseller_Track__c        ESC_Store__c Opportunity_Type__c  \\\n", "0                           ENT  0016F0000237ljIQAQ            Solution   \n", "1                           ENT  0016F0000237ljIQAQ            Solution   \n", "2                           ENT                None          Office Use   \n", "3                           ENT                None          Office Use   \n", "4                           EDU                None         CEPP - BYOD   \n", "5                           EDU                None         CEPP - BYOD   \n", "6                           EDU                None            Solution   \n", "7                           EDU                None             Gifting   \n", "8                           ENT                None          Office Use   \n", "9                           ENT                None             Gifting   \n", "\n", "  leasingornot__c  ... Account.Source_Detail__c Account.Sales_Region__c  \\\n", "0            None  ...                     None                   China   \n", "1            None  ...                     None                   China   \n", "2            None  ...                     None                   China   \n", "3            None  ...                     None                   China   \n", "4            None  ...                     None                   China   \n", "5            None  ...                     None                   China   \n", "6            None  ...                     None                   China   \n", "7            None  ...                     None                   China   \n", "8            None  ...                     None                   China   \n", "9            None  ...                     None                   China   \n", "\n", "       Account.Name Account.Owner.attributes.type  \\\n", "0  北京爱奇艺科技有限公司上海分公司                          User   \n", "1  北京爱奇艺科技有限公司上海分公司                          User   \n", "2  北京高思博乐教育科技股份有限公司                          User   \n", "3  北京高思博乐教育科技股份有限公司                          User   \n", "4    盛趣信息技术（上海）有限公司                          User   \n", "5    盛趣信息技术（上海）有限公司                          User   \n", "6    盛趣信息技术（上海）有限公司                          User   \n", "7    盛趣信息技术（上海）有限公司                          User   \n", "8    北京格物时代科技发展有限公司                          User   \n", "9        北京森隆科技有限公司                          User   \n", "\n", "                        Account.Owner.attributes.url Account.Owner.Name  \\\n", "0  /services/data/v59.0/sobjects/User/***********...        <PERSON><PERSON>   \n", "1  /services/data/v59.0/sobjects/User/***********...        <PERSON><PERSON>   \n", "2  /services/data/v59.0/sobjects/User/0056F00000D...      General Owner   \n", "3  /services/data/v59.0/sobjects/User/0056F00000D...      General Owner   \n", "4  /services/data/v59.0/sobjects/User/0056F00000D...      General Owner   \n", "5  /services/data/v59.0/sobjects/User/0056F00000D...      General Owner   \n", "6  /services/data/v59.0/sobjects/User/0056F00000D...      General Owner   \n", "7  /services/data/v59.0/sobjects/User/0056F00000D...      General Owner   \n", "8  /services/data/v59.0/sobjects/User/0056F00000D...      General Owner   \n", "9  /services/data/v59.0/sobjects/User/0056F00000D...      General Owner   \n", "\n", "  Account.Account_ID__c OpportunityLineItems.totalSize  \\\n", "0       0016F00002KgHKI                              9   \n", "1       0016F00002KgHKI                              2   \n", "2       0016F00003lHScr                              2   \n", "3       0016F00003lHScr                              3   \n", "4       00190000003r26q                              3   \n", "5       00190000003r26q                              1   \n", "6       00190000003r26q                              3   \n", "7       00190000003r26q                              4   \n", "8       0016F00003kdtLg                              5   \n", "9       00190000004PT9s                              1   \n", "\n", "  OpportunityLineItems.done                       OpportunityLineItems.records  \n", "0                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "1                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "2                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "3                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "4                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "5                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "6                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "7                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "8                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "9                      True  [{'attributes': {'type': 'OpportunityLineItem'...  \n", "\n", "[10 rows x 86 columns]"]}, "execution_count": 283, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 284, "id": "29e49a5f-7f24-4dce-81e6-c85f2b947c37", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"data": {"text/plain": ["[{'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F0000168pJfQAI'},\n", "  'Revenue__c': 3191.75,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q2',\n", "  'Product_Family__c': 'MacBook Pro',\n", "  'Quarter__c': 'Q2',\n", "  'Sell_Through_Week__c': 'W08',\n", "  'Oppty_Line_Item_ID__c': '00k6F0000168pJf',\n", "  'Marketing_Part_Number_MPN__c': 'Z0V1',\n", "  'Quantity': 1.0,\n", "  'Line_of_business2__c': 'CPU'},\n", " {'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F0000169QKAQA2'},\n", "  'Revenue__c': 1082.0,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q3',\n", "  'Product_Family__c': 'MacBook Air',\n", "  'Quarter__c': 'Q3',\n", "  'Sell_Through_Week__c': 'W03',\n", "  'Oppty_Line_Item_ID__c': '00k6F0000169QKA',\n", "  'Marketing_Part_Number_MPN__c': 'MREA2CH/A',\n", "  'Quantity': 1.0,\n", "  'Line_of_business2__c': 'CPU'},\n", " {'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F0000179CplQAE'},\n", "  'Revenue__c': 1358.0,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q3',\n", "  'Product_Family__c': 'MacBook Pro',\n", "  'Quarter__c': 'Q3',\n", "  'Sell_Through_Week__c': 'W03',\n", "  'Oppty_Line_Item_ID__c': '00k6F0000179Cpl',\n", "  'Marketing_Part_Number_MPN__c': 'Z0UK',\n", "  'Quantity': 1.0,\n", "  'Line_of_business2__c': 'CPU'},\n", " {'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F000015do03QAA'},\n", "  'Revenue__c': 5180.0,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q1',\n", "  'Product_Family__c': 'Mac Mini',\n", "  'Quarter__c': 'Q1',\n", "  'Sell_Through_Week__c': 'W13',\n", "  'Oppty_Line_Item_ID__c': '00k6F000015do03',\n", "  'Marketing_Part_Number_MPN__c': 'Z0W1',\n", "  'Quantity': 7.0,\n", "  'Line_of_business2__c': 'CPU'},\n", " {'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F0000179CqPQAU'},\n", "  'Revenue__c': 5425.46,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q3',\n", "  'Product_Family__c': 'MacBook Pro',\n", "  'Quarter__c': 'Q3',\n", "  'Sell_Through_Week__c': 'W03',\n", "  'Oppty_Line_Item_ID__c': '00k6F0000179CqP',\n", "  'Marketing_Part_Number_MPN__c': 'Z0V0',\n", "  'Quantity': 2.0,\n", "  'Line_of_business2__c': 'CPU'},\n", " {'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F0000178yqQQAQ'},\n", "  'Revenue__c': 1358.0,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q3',\n", "  'Product_Family__c': 'MacBook Pro',\n", "  'Quarter__c': 'Q3',\n", "  'Sell_Through_Week__c': 'W02',\n", "  'Oppty_Line_Item_ID__c': '00k6F0000178yqQ',\n", "  'Marketing_Part_Number_MPN__c': 'Z0UK',\n", "  'Quantity': 1.0,\n", "  'Line_of_business2__c': 'CPU'},\n", " {'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F000015xT5HQAU'},\n", "  'Revenue__c': 2712.73,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q2',\n", "  'Product_Family__c': 'MacBook Pro',\n", "  'Quarter__c': 'Q2',\n", "  'Sell_Through_Week__c': 'W05',\n", "  'Oppty_Line_Item_ID__c': '00k6F000015xT5H',\n", "  'Marketing_Part_Number_MPN__c': 'Z0V0',\n", "  'Quantity': 1.0,\n", "  'Line_of_business2__c': 'CPU'},\n", " {'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F0000169l19QAA'},\n", "  'Revenue__c': 2059.52,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q2',\n", "  'Product_Family__c': 'MacBook Pro',\n", "  'Quarter__c': 'Q2',\n", "  'Sell_Through_Week__c': 'W13',\n", "  'Oppty_Line_Item_ID__c': '00k6F0000169l19',\n", "  'Marketing_Part_Number_MPN__c': 'Z0V7',\n", "  'Quantity': 1.0,\n", "  'Line_of_business2__c': 'CPU'},\n", " {'attributes': {'type': 'OpportunityLineItem',\n", "   'url': '/services/data/v59.0/sobjects/OpportunityLineItem/00k6F0000169aqOQAQ'},\n", "  'Revenue__c': 16296.0,\n", "  'FY__c': 'FY19',\n", "  'ST_FYQuarter__c': 'FY19Q2',\n", "  'Product_Family__c': 'MacBook Pro',\n", "  'Quarter__c': 'Q2',\n", "  'Sell_Through_Week__c': 'W12',\n", "  'Oppty_Line_Item_ID__c': '00k6F0000169aqO',\n", "  'Marketing_Part_Number_MPN__c': 'Z0UK',\n", "  'Quantity': 12.0,\n", "  'Line_of_business2__c': 'CPU'}]"]}, "execution_count": 284, "metadata": {}, "output_type": "execute_result"}], "source": ["df['OpportunityLineItems.records'][0]"]}, {"cell_type": "code", "execution_count": 151, "id": "b1c82f41-4e54-4ae3-a3a1-76b65f56fb1b", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["# 保留需要的列并按查询顺序排列\n", "required_columns = ['Account.Account_Group__c',\n", " 'OpportunityLineItem.Revenue__c',\n", " 'Account.Group_Province__c',\n", " 'Account.Group_City__c',\n", " 'ACCOUNT_NAME',\n", " 'Account.Province__c',\n", " 'Account.City__c',\n", " 'Opportunity.Project_Type__c',\n", " 'Opportunity.Apple_Reseller__c',\n", " 'Opportunity.Sold_To_ID__c',\n", " 'Opportunity.Opportunity_Reseller_Apple_ID__c',\n", " 'Opportunity.T2_Reseller__c',\n", " 'Opportunity.Apple_HQ_ID__c',\n", " 'Opportunity.Opportunity_Reseller_Track__c',\n", " 'Opportunity.ESC_Store__c',\n", " 'Account.Sub_Segment__c',\n", " 'Account.Vertical_Industry__c',\n", " 'Account.<PERSON>_as_Choice__c',\n", " 'Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c',\n", " 'Account.<PERSON>_as_Choice_start_time__c',\n", " 'Account.Top_Account_Deep_Dive__c',\n", " 'Account.FY21Q3_Large_Account__c',\n", " 'Account.FY21Q4_Large_Account__c',\n", " 'Account.FY22Q1__c',\n", " 'Account.FY22Q2_Top_Account__c',\n", " 'Account.FY22Q3_Top_Account__c',\n", " 'Account.FY22Q4_Top_Account__c',\n", " 'Account.FY23Q1_Top_Account__c',\n", " 'Account.FY23Q2_Top_Account__c',\n", " 'Account.FY23Q3_Top_Account__c',\n", " 'Account.FY23Q4_Top_Account__c',\n", " 'Account.FY24Q1_Top_Account__c',\n", " 'Account.FY24Q2_Top_Account__c',\n", " 'Account.FY24Q3_Top_Account__c',\n", " 'Account.FY21Q2_AE__c',\n", " 'Account.FY21Q2_AEM__c',\n", " 'Account.FY21Q3_AE__c',\n", " 'Account.FY21Q3_AEM__c',\n", " 'Account.FY21Q4_AE__c',\n", " 'Account.FY21Q4_AEM__c',\n", " 'Account.FY22Q1_AE__c',\n", " 'Account.FY22Q1_AEM__c',\n", " 'Account.FY22Q2_AE__c',\n", " 'Account.FY22Q2_AEM__c',\n", " 'Account.FY22Q3_AE__c',\n", " 'Account.FY22Q3_AEM__c',\n", " 'Account.FY22Q4_AE__c',\n", " 'Account.FY22Q4_AEM__c',\n", " 'Account.FY23Q1_AE__c',\n", " 'Account.FY23Q1_AEM__c',\n", " 'Account.FY23Q2_AE__c',\n", " 'Account.FY23Q2_AEM__c',\n", " 'Account.FY23Q3_AE__c',\n", " 'Account.FY23Q3_AEM__c',\n", " 'Account.FY23Q4_AE__c',\n", " 'Account.FY23Q4_AEM__c',\n", " 'Account.FY24Q1_AE__c',\n", " 'Account.FY24Q1_AEM__c',\n", " 'Account.FY24Q2_AE__c',\n", " 'Account.FY24Q2_AEM__c',\n", " 'Account.FY24Q3_AE__c',\n", " 'Account.FY24Q3_AEM__c',\n", " 'ACCOUNT_OWNER',\n", " 'QUANTITY',\n", " 'ACCOUNT_ID',\n", " 'FAMILY',\n", " 'OpportunityLineItem.FY__c',\n", " 'OpportunityLineItem.ST_FYQuarter__c',\n", " 'OpportunityLineItem.Product_Family__c',\n", " 'OpportunityLineItem.Quarter__c',\n", " 'OpportunityLineItem.Sell_Through_Week__c',\n", " 'Opportunity.Opportunity_Type__c',\n", " 'OPPORTUNITY_ID',\n", " 'OPPORTUNITY_NAME',\n", " 'OpportunityLineItem.Oppty_Line_Item_ID__c',\n", " 'PROBABILITY',\n", " 'Account.Segment__c',\n", " 'Account.Large_Account__c',\n", " 'OpportunityLineItem.Marketing_Part_Number_MPN__c',\n", " 'Account.Total_Mac_Demand__c',\n", " 'Account.PC_Install_Base__c',\n", " 'Account.FY22_Fcst__c',\n", " 'Account.FY23_Fcst__c',\n", " 'Opportunity.leasingornot__c',\n", " 'Account.Industry_Target_Account__c',\n", " 'Opportunity.Penetrated_Account__c',\n", " 'Account.Source_Detail__c',\n", " 'Account.Sales_Region__c']"]}, {"cell_type": "code", "execution_count": 290, "id": "101886de-68d0-4bb1-a07f-32a9d0fe9e92", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-548\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-1136\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-1739\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-2430\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-3147\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-3783\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-4322\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-4723\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-5125\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-5500\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-5903\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-6250\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-6669\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-7143\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-7536\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-7954\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-8378\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-8713\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-9060\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-9551\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-10178\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-10745\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-11550\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-12318\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-12973\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-13644\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-13952\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-14596\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-15153\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-15693\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-16176\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-16560\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-16989\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-17346\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-17910\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-18502\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-19195\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-19946\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-20777\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-21411\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-21832\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-22582\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-23117\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-23383\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-23842\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-24377\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-24888\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-25150\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-25667\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-25919\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-26325\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-26728\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-27075\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-27446\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-27647\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-27910\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-28284\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-28701\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-29026\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-29459\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-29967\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-30297\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-30663\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-31058\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-31421\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-31724\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-32035\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-32351\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-32627\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-32991\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-33429\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-33937\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-34303\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-34514\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-34836\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-35009\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-35422\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-35631\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-35768\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-35923\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-36049\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-36319\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-36512\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-36685\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-36962\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-37245\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-37474\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-37768\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-38044\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-38365\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-38630\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-38983\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-39215\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-39478\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-39646\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-39892\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-40113\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-40306\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-40573\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-40791\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-41025\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-41306\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-41597\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-41782\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-41832\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-41922\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-42116\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-42323\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-42621\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-42766\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-43052\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-43211\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-43404\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-43663\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-44012\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-44403\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-44650\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-44895\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-45381\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-46127\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-46559\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-46884\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-47433\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-47906\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-48552\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-48941\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-49138\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-49473\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-49726\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-49978\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-50264\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-50555\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-50824\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-51164\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-51406\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-51698\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-52039\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-52325\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-52572\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-53133\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-53548\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-54036\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-54309\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-54614\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-54882\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-55033\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-55289\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-55488\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-55661\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-55853\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-56130\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-56337\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-56513\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-56765\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-56923\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-57053\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-57169\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-57381\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-57594\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-57854\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-58026\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-58303\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-58588\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-58940\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-59160\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-59389\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-59798\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-60021\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-60321\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-60564\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-60809\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-60971\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-61176\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-61827\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-62055\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-62435\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-62827\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-63166\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-63449\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-63742\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-64075\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-64333\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-64607\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-64901\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-65298\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-65385\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-65649\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-65818\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-65894\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-65949\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-65994\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-66032\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-66048\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-66184\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-66318\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-66347\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-66393\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-66697\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-67062\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-67261\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-67426\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-67637\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-67875\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-68207\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-68580\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-68895\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-69304\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-69635\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-69772\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-69823\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-69898\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-70151\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-70517\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-70931\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-71342\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-71796\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-72187\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-72634\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-73046\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-73413\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-73785\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-74236\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-74593\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-74967\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-75284\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-75673\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-75972\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-76232\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx36M4G1YI6jAUG-76521\n"]}], "source": ["\n", "# for check keyong\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"YOUR_ACCESS_TOKEN\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "# SOQL query\n", "\n", "\n", "sql_statement = \"\"\"\n", "SELECT \n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "    Opportunity.Apple_Reseller__c, \n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__c, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "    Account.<PERSON>_as_Choice__c, \n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "    Account.Top_Account_Deep_Dive__c, \n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY22Q2_Top_Account__c, \n", "    Account.FY22Q3_Top_Account__c, \n", "    Account.FY22Q4_Top_Account__c, \n", "    Account.FY23Q1_Top_Account__c, \n", "    Account.FY23Q2_Top_Account__c, \n", "    Account.FY23Q3_Top_Account__c, \n", "    Account.FY23Q4_Top_Account__c, \n", "    Account.FY24Q1_Top_Account__c, \n", "    Account.FY24Q2_Top_Account__c, \n", "    Account.FY24Q3_Top_Account__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c, \n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Account.FY22Q2_AE__c, \n", "    Account.FY22Q2_AEM__c, \n", "    Account.FY22Q3_AE__c, \n", "    Account.FY22Q3_AEM__c, \n", "    Account.FY22Q4_AE__c, \n", "    Account.FY22Q4_AEM__c, \n", "    Account.FY23Q1_AE__c, \n", "    Account.FY23Q1_AEM__c, \n", "    Account.FY23Q2_AE__c, \n", "    Account.FY23Q2_AEM__c, \n", "    Account.FY23Q3_AE__c, \n", "    Account.FY23Q3_AEM__c, \n", "    Account.FY23Q4_AE__c, \n", "    Account.FY23Q4_AEM__c, \n", "    Account.FY24Q1_AE__c, \n", "    Account.FY24Q1_AEM__c, \n", "    Account.FY24Q2_AE__c, \n", "    Account.FY24Q2_AEM__c, \n", "    Account.FY24Q3_AE__c, \n", "    Account.FY24Q3_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__c, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Opportunity.Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.OPPORTUNITY_ID__c,\n", "    Opportunity.Name,\n", "    Opportunity.Probability,\n", "    (SELECT \n", "        Revenue__c, \n", "        FY__c, \n", "        ST_FYQuarter__c, \n", "        Product_Family__c,\n", "        Quarter__c, \n", "        Sell_Through_Week__c, \n", "        Oppty_Line_Item_ID__c, \n", "        Marketing_Part_Number_MPN__c,\n", "        Quantity,\n", "        Line_of_business2__c\n", "     FROM OpportunityLineItems)\n", "FROM Opportunity\n", "WHERE Account.Name != NULL \n", "\"\"\"\n", "\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data_fordebug.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 292, "id": "8ce9a234-2e63-437f-a134-040db1b17c18", "metadata": {"tags": []}, "outputs": [], "source": ["# 过滤掉没有 OpportunityLineItems 或 records 数据的记录\n", "filtered_records = [record for record in all_records if record.get('OpportunityLineItems') and record['OpportunityLineItems'].get('records')]\n"]}, {"cell_type": "code", "execution_count": 293, "id": "2d8d4f7e-8daa-4c2b-843f-364556f25b3d", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>T2_Reseller__c</th>\n", "      <th>Apple_HQ_ID__c</th>\n", "      <th>Opportunity_Reseller_Track__c</th>\n", "      <th>ESC_Store__c</th>\n", "      <th>Opportunity_Type__c</th>\n", "      <th>leasingornot__c</th>\n", "      <th>Penetrated_Account__c</th>\n", "      <th>Opportunity_ID__c</th>\n", "      <th>Name</th>\n", "      <th>Probability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11100.00</td>\n", "      <td>FY10</td>\n", "      <td>FY10Q1</td>\n", "      <td>iMac</td>\n", "      <td>Q1</td>\n", "      <td>W03</td>\n", "      <td>00k20000006H356</td>\n", "      <td>MB417CH/A</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>00620000009o6mq</td>\n", "      <td>苹果开发实验室</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>7920.00</td>\n", "      <td>FY10</td>\n", "      <td>FY10Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W03</td>\n", "      <td>00k20000006H35V</td>\n", "      <td>MB985CH/A</td>\n", "      <td>5.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>00620000009o6mq</td>\n", "      <td>苹果开发实验室</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>40704.00</td>\n", "      <td>FY10</td>\n", "      <td>FY10Q1</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q1</td>\n", "      <td>W03</td>\n", "      <td>00k20000006H357</td>\n", "      <td>MB991CH/A</td>\n", "      <td>32.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>00620000009o6o1</td>\n", "      <td>国世通地产内部采购</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>58023.00</td>\n", "      <td>FY10</td>\n", "      <td>FY10Q1</td>\n", "      <td>Displays</td>\n", "      <td>Q1</td>\n", "      <td>W05</td>\n", "      <td>00k20000006H39Z</td>\n", "      <td>M9179CH/A</td>\n", "      <td>27.0</td>\n", "      <td>Others</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>00620000009o6o2</td>\n", "      <td>空军雷达地面对抗研究所内部采购</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>82348.00</td>\n", "      <td>FY10</td>\n", "      <td>FY10Q1</td>\n", "      <td>Mac Pro</td>\n", "      <td>Q1</td>\n", "      <td>W04</td>\n", "      <td>00k20000006H3A8</td>\n", "      <td>MB535CH/A</td>\n", "      <td>28.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>00620000009o6o3</td>\n", "      <td>青岛劳动局内部采购</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376594</th>\n", "      <td>4031.21</td>\n", "      <td>FY18</td>\n", "      <td>FY18Q1</td>\n", "      <td>iMac</td>\n", "      <td>Q1</td>\n", "      <td>W05</td>\n", "      <td>00k6F00000UhvDG</td>\n", "      <td>MMQA2CH/A</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>404969</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>Others</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F00000iOqM3</td>\n", "      <td>扬子晚报-员工团购</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376595</th>\n", "      <td>7721.00</td>\n", "      <td>FY13</td>\n", "      <td>FY13Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00k90000007iKxk</td>\n", "      <td>MD231CH/A</td>\n", "      <td>7.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>404969</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0069000000A2dxp</td>\n", "      <td>扬子晚报-员工团购</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376596</th>\n", "      <td>13830.00</td>\n", "      <td>FY13</td>\n", "      <td>FY13Q3</td>\n", "      <td>iPad 4th Gen Wifi</td>\n", "      <td>Q3</td>\n", "      <td>W04</td>\n", "      <td>00k90000007iKxf</td>\n", "      <td>MD510CH/A</td>\n", "      <td>30.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>404969</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0069000000A2dxp</td>\n", "      <td>扬子晚报-员工团购</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376597</th>\n", "      <td>3100.00</td>\n", "      <td>FY13</td>\n", "      <td>FY13Q3</td>\n", "      <td>iPad Mini Wifi</td>\n", "      <td>Q3</td>\n", "      <td>W12</td>\n", "      <td>00k90000008OW0E</td>\n", "      <td>MD528CH/A</td>\n", "      <td>10.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>404969</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0069000000A2dxp</td>\n", "      <td>扬子晚报-员工团购</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376598</th>\n", "      <td>2800.00</td>\n", "      <td>FY13</td>\n", "      <td>FY13Q3</td>\n", "      <td>Mac Mini</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00k90000008dw0E</td>\n", "      <td>MD387CH/A</td>\n", "      <td>5.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>404969</td>\n", "      <td>EDU</td>\n", "      <td>None</td>\n", "      <td>CEPP - BYOD</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0069000000A2dxp</td>\n", "      <td>扬子晚报-员工团购</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>376599 rows × 90 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c  Product_Family__c Quarter__c  \\\n", "0         11100.00  FY10          FY10Q1               iMac         Q1   \n", "1          7920.00  FY10          FY10Q1        MacBook Pro         Q1   \n", "2         40704.00  FY10          FY10Q1        MacBook Pro         Q1   \n", "3         58023.00  FY10          FY10Q1           Displays         Q1   \n", "4         82348.00  FY10          FY10Q1            Mac Pro         Q1   \n", "...            ...   ...             ...                ...        ...   \n", "376594     4031.21  FY18          FY18Q1               iMac         Q1   \n", "376595     7721.00  FY13          FY13Q3        MacBook Air         Q3   \n", "376596    13830.00  FY13          FY13Q3  iPad 4th Gen Wifi         Q3   \n", "376597     3100.00  FY13          FY13Q3     iPad Mini Wifi         Q3   \n", "376598     2800.00  FY13          FY13Q3           Mac Mini         Q3   \n", "\n", "       Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0                       W03       00k20000006H356   \n", "1                       W03       00k20000006H35V   \n", "2                       W03       00k20000006H357   \n", "3                       W05       00k20000006H39Z   \n", "4                       W04       00k20000006H3A8   \n", "...                     ...                   ...   \n", "376594                  W05       00k6F00000UhvDG   \n", "376595                  W09       00k90000007iKxk   \n", "376596                  W04       00k90000007iKxf   \n", "376597                  W12       00k90000008OW0E   \n", "376598                  W09       00k90000008dw0E   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MB417CH/A      12.0                  CPU  ...   \n", "1                         MB985CH/A       5.0                  CPU  ...   \n", "2                         MB991CH/A      32.0                  CPU  ...   \n", "3                         M9179CH/A      27.0               Others  ...   \n", "4                         MB535CH/A      28.0                  CPU  ...   \n", "...                             ...       ...                  ...  ...   \n", "376594                    MMQA2CH/A       4.0                  CPU  ...   \n", "376595                    MD231CH/A       7.0                  CPU  ...   \n", "376596                    MD510CH/A      30.0                 iPad  ...   \n", "376597                    MD528CH/A      10.0                 iPad  ...   \n", "376598                    MD387CH/A       5.0                  CPU  ...   \n", "\n", "       T2_Reseller__c Apple_HQ_ID__c Opportunity_Reseller_Track__c  \\\n", "0                None           None                          None   \n", "1                None           None                          None   \n", "2                None           None                          None   \n", "3                None           None                          None   \n", "4                None           None                          None   \n", "...               ...            ...                           ...   \n", "376594           None         404969                           EDU   \n", "376595           None         404969                           EDU   \n", "376596           None         404969                           EDU   \n", "376597           None         404969                           EDU   \n", "376598           None         404969                           EDU   \n", "\n", "       ESC_Store__c Opportunity_Type__c leasingornot__c Penetrated_Account__c  \\\n", "0              None                None            None                  None   \n", "1              None                None            None                  None   \n", "2              None         CEPP - BYOD            None                  None   \n", "3              None         CEPP - BYOD            None                  None   \n", "4              None         CEPP - BYOD            None                  None   \n", "...             ...                 ...             ...                   ...   \n", "376594         None              Others            None                  None   \n", "376595         None         CEPP - BYOD            None                  None   \n", "376596         None         CEPP - BYOD            None                  None   \n", "376597         None         CEPP - BYOD            None                  None   \n", "376598         None         CEPP - BYOD            None                  None   \n", "\n", "       Opportunity_ID__c             Name Probability  \n", "0        00620000009o6mq          苹果开发实验室       100.0  \n", "1        00620000009o6mq          苹果开发实验室       100.0  \n", "2        00620000009o6o1        国世通地产内部采购       100.0  \n", "3        00620000009o6o2  空军雷达地面对抗研究所内部采购       100.0  \n", "4        00620000009o6o3        青岛劳动局内部采购       100.0  \n", "...                  ...              ...         ...  \n", "376594   0066F00000iOqM3        扬子晚报-员工团购       100.0  \n", "376595   0069000000A2dxp        扬子晚报-员工团购       100.0  \n", "376596   0069000000A2dxp        扬子晚报-员工团购       100.0  \n", "376597   0069000000A2dxp        扬子晚报-员工团购       100.0  \n", "376598   0069000000A2dxp        扬子晚报-员工团购       100.0  \n", "\n", "[376599 rows x 90 columns]"]}, "execution_count": 293, "metadata": {}, "output_type": "execute_result"}], "source": ["df_opportunity_line_items = pd.json_normalize(\n", "    filtered_records,\n", "    record_path=['OpportunityLineItems', 'records'],\n", "    meta=[\n", "\n", "        ['Account',\"Account_Group__c\"],\n", "['Account',\"Group_Province__c\"],\n", "['Account',\"Group_City__c\"],\n", "['Account',\"Province__c\"],\n", "['Account',\"City__c\"],\n", "['Account',\"Sub_Segment__c\"],\n", "['Account',\"Vertical_Industry__c\"],\n", "['Account',\"Mac_as_Choice__c\"],\n", "['Account',\"z<PERSON><PERSON><PERSON><PERSON><PERSON>__c\"],\n", "['Account',\"Mac_as_Choice_start_time__c\"],\n", "['Account',\"Top_Account_Deep_Dive__c\"],\n", "['Account',\"FY21Q3_Large_Account__c\"],\n", "['Account',\"FY21Q4_Large_Account__c\"],\n", "['Account',\"FY22Q1__c\"],\n", "['Account',\"FY22Q2_Top_Account__c\"],\n", "['Account',\"FY22Q3_Top_Account__c\"],\n", "['Account',\"FY22Q4_Top_Account__c\"],\n", "['Account',\"FY23Q1_Top_Account__c\"],\n", "['Account',\"FY23Q2_Top_Account__c\"],\n", "['Account',\"FY23Q3_Top_Account__c\"],\n", "['Account',\"FY23Q4_Top_Account__c\"],\n", "['Account',\"FY24Q1_Top_Account__c\"],\n", "['Account',\"FY24Q2_Top_Account__c\"],\n", "['Account',\"FY24Q3_Top_Account__c\"],\n", "['Account',\"FY21Q2_AE__c\"],\n", "['Account',\"FY21Q2_AEM__c\"],\n", "['Account',\"FY21Q3_AE__c\"],\n", "['Account',\"FY21Q3_AEM__c\"],\n", "['Account',\"FY21Q4_AE__c\"],\n", "['Account',\"FY21Q4_AEM__c\"],\n", "['Account',\"FY22Q1_AE__c\"],\n", "['Account',\"FY22Q1_AEM__c\"],\n", "['Account',\"FY22Q2_AE__c\"],\n", "['Account',\"FY22Q2_AEM__c\"],\n", "['Account',\"FY22Q3_AE__c\"],\n", "['Account',\"FY22Q3_AEM__c\"],\n", "['Account',\"FY22Q4_AE__c\"],\n", "['Account',\"FY22Q4_AEM__c\"],\n", "['Account',\"FY23Q1_AE__c\"],\n", "['Account',\"FY23Q1_AEM__c\"],\n", "['Account',\"FY23Q2_AE__c\"],\n", "['Account',\"FY23Q2_AEM__c\"],\n", "['Account',\"FY23Q3_AE__c\"],\n", "['Account',\"FY23Q3_AEM__c\"],\n", "['Account',\"FY23Q4_AE__c\"],\n", "['Account',\"FY23Q4_AEM__c\"],\n", "['Account',\"FY24Q1_AE__c\"],\n", "['Account',\"FY24Q1_AEM__c\"],\n", "['Account',\"FY24Q2_AE__c\"],\n", "['Account',\"FY24Q2_AEM__c\"],\n", "['Account',\"FY24Q3_AE__c\"],\n", "['Account',\"FY24Q3_AEM__c\"],\n", "['Account',\"Segment__c\"],\n", "['Account',\"Large_Account__c\"],\n", "['Account',\"Total_Mac_Demand__c\"],\n", "['Account',\"PC_Install_Base__c\"],\n", "['Account',\"FY22_Fcst__c\"],\n", "['Account',\"FY23_Fcst__c\"],\n", "['Account',\"Industry_Target_Account__c\"],\n", "['Account',\"Source_Detail__c\"],\n", "['Account',\"Sales_Region__c\"],\n", "['Account',\"Name\"],\n", "['Account',\"Owner\",\"Name\"],\n", "['Account',\"Account_ID__c\"],\n", "\n", "        'Project_Type__c',\n", "        'Apple_Reseller__c',\n", "        'Sold_To_ID__c',\n", "        'Opportunity_Reseller_Apple_ID__c',\n", "        'T2_Reseller__c',\n", "        'Apple_HQ_ID__c',\n", "        'Opportunity_Reseller_Track__c',\n", "        'ESC_Store__c',\n", "        'Opportunity_Type__c',\n", "        'leasingornot__c',\n", "        'Penetrated_Account__c',\n", "        'Opportunity_ID__c',\n", "        'Name',\n", "        'Probability'\n", "    ],\n", "    # meta_prefix='Opportunity.',\n", "    errors='ignore'\n", ")\n", "\n", "df_opportunity_line_items"]}, {"cell_type": "code", "execution_count": 21, "id": "766f7d2b-dc4a-48d5-a7f8-49d3e6a5eb94", "metadata": {"tags": []}, "outputs": [], "source": ["df_filtered = df[required_columns]"]}, {"cell_type": "code", "execution_count": 22, "id": "8c357997-fb77-40b8-a4bf-926843a70586", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Account.Account_Group__c</th>\n", "      <th>Account.Group_Province__c</th>\n", "      <th>Account.Group_City__c</th>\n", "      <th>Account.Province__c</th>\n", "      <th>Account.City__c</th>\n", "      <th>Project_Type__c</th>\n", "      <th>Apple_Reseller__c</th>\n", "      <th>Sold_To_ID__c</th>\n", "      <th>Opportunity_Reseller_Apple_ID__c</th>\n", "      <th>T2_Reseller__c</th>\n", "      <th>...</th>\n", "      <th>Account.Total_Mac_Demand__c</th>\n", "      <th>Account.PC_Install_Base__c</th>\n", "      <th>Account.FY22_Fcst__c</th>\n", "      <th>Account.FY23_Fcst__c</th>\n", "      <th>leasingornot__c</th>\n", "      <th>Account.Industry_Target_Account__c</th>\n", "      <th>Penetrated_Account__c</th>\n", "      <th>Account.Source_Detail__c</th>\n", "      <th>Account.Sales_Region__c</th>\n", "      <th>OpportunityLineItems</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>云南大学</td>\n", "      <td>Yunnan</td>\n", "      <td><PERSON>nming</td>\n", "      <td>Yunnan</td>\n", "      <td><PERSON>nming</td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQXUA3</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>昆明理工大学</td>\n", "      <td>Yunnan</td>\n", "      <td><PERSON>nming</td>\n", "      <td>Yunnan</td>\n", "      <td><PERSON>nming</td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQTUA3</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>昆明理工大学</td>\n", "      <td>Yunnan</td>\n", "      <td><PERSON>nming</td>\n", "      <td>Yunnan</td>\n", "      <td><PERSON>nming</td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>武汉大学</td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQeUAN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>武汉大学</td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQeUAN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>武汉大学</td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQeUAN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>武汉大学</td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQeUAN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>武汉大学</td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Runrate</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQeUAN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>武汉大学</td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQeUAN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>武汉大学</td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Hubei</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Deal</td>\n", "      <td>***********2au6AAA</td>\n", "      <td>829185</td>\n", "      <td>None</td>\n", "      <td>a1R6F00000JxTQeUAN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 73 columns</p>\n", "</div>"], "text/plain": ["  Account.Account_Group__c Account.Group_Province__c Account.Group_City__c  \\\n", "0                     云南大学                    Yunnan               Kunming   \n", "1                   昆明理工大学                    Yunnan               Kunming   \n", "2                   昆明理工大学                    Yunnan               Kunming   \n", "3                     武汉大学                     <PERSON><PERSON>   \n", "4                     武汉大学                     <PERSON><PERSON>   \n", "5                     武汉大学                     <PERSON><PERSON>   \n", "6                     武汉大学                     <PERSON><PERSON>   \n", "7                     武汉大学                     <PERSON><PERSON>   \n", "8                     武汉大学                     <PERSON><PERSON>   \n", "9                     武汉大学                     <PERSON><PERSON>   \n", "\n", "  Account.Province__c Account.City__c Project_Type__c   Apple_Reseller__c  \\\n", "0              Yunnan         Kunming            Deal  ***********2au6AAA   \n", "1              Yunnan         Kunming            Deal  ***********2au6AAA   \n", "2              Yunnan         Kunming            Deal  ***********2au6AAA   \n", "3               Hubei           Wuhan            Deal  ***********2au6AAA   \n", "4               Hubei           Wuhan            Deal  ***********2au6AAA   \n", "5               Hubei           Wuhan            Deal  ***********2au6AAA   \n", "6               Hubei           Wuhan            Deal  ***********2au6AAA   \n", "7               Hubei           Wuhan         Runrate  ***********2au6AAA   \n", "8               Hubei           Wuhan            Deal  ***********2au6AAA   \n", "9               Hubei           Wuhan            Deal  ***********2au6AAA   \n", "\n", "  Sold_To_ID__c Opportunity_Reseller_Apple_ID__c      T2_Reseller__c  ...  \\\n", "0        829185                             None  a1R6F00000JxTQXUA3  ...   \n", "1        829185                             None  a1R6F00000JxTQTUA3  ...   \n", "2        829185                             None                None  ...   \n", "3        829185                             None  a1R6F00000JxTQeUAN  ...   \n", "4        829185                             None  a1R6F00000JxTQeUAN  ...   \n", "5        829185                             None  a1R6F00000JxTQeUAN  ...   \n", "6        829185                             None  a1R6F00000JxTQeUAN  ...   \n", "7        829185                             None  a1R6F00000JxTQeUAN  ...   \n", "8        829185                             None  a1R6F00000JxTQeUAN  ...   \n", "9        829185                             None  a1R6F00000JxTQeUAN  ...   \n", "\n", "  Account.Total_Mac_Demand__c Account.PC_Install_Base__c Account.FY22_Fcst__c  \\\n", "0                        None                       None                 None   \n", "1                        None                       None                 None   \n", "2                        None                       None                 None   \n", "3                        None                       None                 None   \n", "4                        None                       None                 None   \n", "5                        None                       None                 None   \n", "6                        None                       None                 None   \n", "7                        None                       None                 None   \n", "8                        None                       None                 None   \n", "9                        None                       None                 None   \n", "\n", "  Account.FY23_Fcst__c leasingornot__c  Account.Industry_Target_Account__c  \\\n", "0                 None            None                               False   \n", "1                 None            None                               False   \n", "2                 None            None                               False   \n", "3                 None            None                               False   \n", "4                 None            None                               False   \n", "5                 None            None                               False   \n", "6                 None            None                               False   \n", "7                 None            None                               False   \n", "8                 None            None                               False   \n", "9                 None            None                               False   \n", "\n", "  Penetrated_Account__c Account.Source_Detail__c  Account.Sales_Region__c  \\\n", "0                  None                     None                    China   \n", "1                  None                     None                    China   \n", "2                  None                     None                    China   \n", "3                  None                     None                    China   \n", "4                  None                     None                    China   \n", "5                  None                     None                    China   \n", "6                  None                     None                    China   \n", "7                  None                     None                    China   \n", "8                  None                     None                    China   \n", "9                  None                     None                    China   \n", "\n", "   OpportunityLineItems  \n", "0                   NaN  \n", "1                   NaN  \n", "2                   NaN  \n", "3                   NaN  \n", "4                   NaN  \n", "5                   NaN  \n", "6                   NaN  \n", "7                   NaN  \n", "8                   NaN  \n", "9                   NaN  \n", "\n", "[10 rows x 73 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df_filtered"]}, {"cell_type": "code", "execution_count": null, "id": "42db02af-511c-44a5-bbe7-703236590f13", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "id": "c298688f-7094-46ca-af89-5b7027500f6b", "metadata": {}, "outputs": [], "source": ["df.drop(['attributes.type',\n", "       'attributes.url', 'Owner.attributes.type', 'Owner.attributes.url'],axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": 13, "id": "12ad3e57-a23f-479a-af3a-ca65e940ae38", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Project_Type__c', 'Apple_Reseller__c', 'Sold_To_ID__c',\n", "       'Opportunity_Reseller_Apple_ID__c', 'T2_Reseller__c', 'Apple_HQ_ID__c',\n", "       'Opportunity_Reseller_Track__c', 'ESC_Store__c', 'Opportunity_Type__c',\n", "       'leasingornot__c', 'Penetrated_Account__c', 'attributes.type',\n", "       'attributes.url', 'Account.attributes.type', 'Account.attributes.url',\n", "       'Account.Account_Group__c', 'Account.Group_Province__c',\n", "       'Account.Group_City__c', 'Account.Province__c', 'Account.City__c',\n", "       'Account.Sub_Segment__c', 'Account.Vertical_Industry__c',\n", "       'Account.<PERSON>_as_Choice__c', 'Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c',\n", "       'Account.<PERSON>_as_Choice_start_time__c',\n", "       'Account.Top_Account_Deep_Dive__c', 'Account.FY21Q3_Large_Account__c',\n", "       'Account.FY21Q4_Large_Account__c', 'Account.FY22Q1__c',\n", "       'Account.FY22Q2_Top_Account__c', 'Account.FY22Q3_Top_Account__c',\n", "       'Account.FY22Q4_Top_Account__c', 'Account.FY23Q1_Top_Account__c',\n", "       'Account.FY23Q2_Top_Account__c', 'Account.FY23Q3_Top_Account__c',\n", "       'Account.FY23Q4_Top_Account__c', 'Account.FY24Q1_Top_Account__c',\n", "       'Account.FY24Q2_Top_Account__c', 'Account.FY24Q3_Top_Account__c',\n", "       'Account.FY21Q2_AE__c', 'Account.FY21Q2_AEM__c', 'Account.FY21Q3_AE__c',\n", "       'Account.FY21Q3_AEM__c', 'Account.FY21Q4_AE__c',\n", "       'Account.FY21Q4_AEM__c', 'Account.FY22Q1_AE__c',\n", "       'Account.FY22Q1_AEM__c', 'Account.FY22Q2_AE__c',\n", "       'Account.FY22Q2_AEM__c', 'Account.FY22Q3_AE__c',\n", "       'Account.FY22Q3_AEM__c', 'Account.FY22Q4_AE__c',\n", "       'Account.FY22Q4_AEM__c', 'Account.FY23Q1_AE__c',\n", "       'Account.FY23Q1_AEM__c', 'Account.FY23Q2_AE__c',\n", "       'Account.FY23Q2_AEM__c', 'Account.FY23Q3_AE__c',\n", "       'Account.FY23Q3_AEM__c', 'Account.FY23Q4_AE__c',\n", "       'Account.FY23Q4_AEM__c', 'Account.FY24Q1_AE__c',\n", "       'Account.FY24Q1_AEM__c', 'Account.FY24Q2_AE__c',\n", "       'Account.FY24Q2_AEM__c', 'Account.FY24Q3_AE__c',\n", "       'Account.FY24Q3_AEM__c', 'Account.Segment__c',\n", "       'Account.Large_Account__c', 'Account.Total_Mac_Demand__c',\n", "       'Account.PC_Install_Base__c', 'Account.FY22_Fcst__c',\n", "       'Account.FY23_Fcst__c', 'Account.Industry_Target_Account__c',\n", "       'Account.Source_Detail__c', 'Account.Sales_Region__c',\n", "       'OpportunityLineItems.totalSize', 'OpportunityLineItems.done',\n", "       'OpportunityLineItems.records', 'OpportunityLineItems'],\n", "      dtype='object')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 10, "id": "02da4f2e-8731-4387-b61f-1e1ce4fbb57e", "metadata": {}, "outputs": [], "source": ["df = df[['FY21Q2_Identifier__c', 'FY24Q3_Identifier__c', 'FY22Q1__c',\n", "       'FY21Q3_Large_Account__c', 'FY21Q4_Large_Account__c',\n", "       'FY24Q2_Top_Account__c', 'FY22Q2_Top_Account__c',\n", "       'FY22Q3_Top_Account__c', 'FY22Q4_Top_Account__c',\n", "       'FY23Q1_Top_Account__c', 'FY23Q2_Top_Account__c',\n", "       'FY23Q3_Top_Account__c', 'FY23Q4_Top_Account__c',\n", "       'FY24Q1_Top_Account__c', 'FY24Q3_Top_Account__c', 'FY24Q2_AE__c',\n", "       'FY24Q2_AEM__c', 'FY24Q3_AE__c', 'FY24Q3_AEM__c', 'Mac_as_Choice__c',\n", "       'Mac_as_Choice_start_time__c', 'SEI_maC__c', 'Name',\n", "       'SEI_Account_ID__c', 'SEI_Cluster_ID__c', 'Account_Cluster__c',\n", "       'Is_Group__c', 'Account_ID__c', 'Account_Group__c', 'SEI_Group_ID__c',\n", "       'Account_Group_ID__c', 'Group_Sub_Segment__c',\n", "       'Group_Vertical_Industry__c', 'Province__c', 'City__c',\n", "       'Group_Province__c', 'Group_City__c', 'Owner.Name', 'Sub_Segment__c',\n", "       'Vertical_Industry__c', 'Top_Account_Deep_Dive__c']]"]}, {"cell_type": "code", "execution_count": 14, "id": "5f660387-6c4c-4e73-b55a-6ae3671f8602", "metadata": {}, "outputs": [], "source": ["df2 = pd.read_csv('header_PPL_Data_by_Account_Name_FY20_CQ_forapi.csv')"]}, {"cell_type": "code", "execution_count": 24, "id": "4aa40efe-78f7-4c77-8d53-773d8eaefd60", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["Index(['Account Group', 'ProductLineRevenue', 'Group Province', 'Group City',\n", "       'Account Name', 'Province/Region', 'City', 'Deal type',\n", "       'Disti/T1 Reseller', 'Sold To ID', 'Reseller Apple ID', 'T2 Reseller',\n", "       'Apple ID', 'Reseller Track', 'ESC Store', 'Sub Segment',\n", "       'Vertical Industry', 'Mac as Choice', 'Mac as Choice加入时间',\n", "       'Mac as Choice start time', 'Top Account Deep Dive',\n", "       'FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY22Q1 Large Account',\n", "       'FY22Q2 Top Account', 'FY22Q3 Top Account', 'FY22Q4 Top Account',\n", "       'FY23Q1 Top Account', 'FY23Q2 Top Account', 'FY23Q3 Top Account',\n", "       'FY23Q4 Top Account', 'FY24Q1 Top Account', 'FY24Q2 Top Account',\n", "       'FY24Q3 Top Account', 'FY21Q2 AE', 'FY21Q2 AEM', 'FY21Q3 AE',\n", "       'FY21Q3 AEM', 'FY21Q4 AE', 'FY21Q4 AEM', 'FY22Q1 AE', 'FY22Q1 AEM',\n", "       'FY22Q2 AE', 'FY22Q2 AEM', 'FY22Q3 AE', 'FY22Q3 AEM', 'FY22Q4 AE',\n", "       'FY22Q4 AEM', 'FY23Q1 AE', 'FY23Q1 AEM', 'FY23Q2 AE', 'FY23Q2 AEM',\n", "       'FY23Q3 AE', 'FY23Q3 AEM', 'FY23Q4 AE', 'FY23Q4 AEM', 'FY24Q1 AE',\n", "       'FY24Q1 AEM', 'FY24Q2 AE', 'FY24Q2 AEM', 'FY24Q3 AE', 'FY24Q3 AEM',\n", "       'Account Owner', 'Quantity', 'Account ID', 'Line of Business', 'FY',\n", "       'ST FYQuarter', 'Product Family', 'Product ST Quarter',\n", "       'Product ST Week', 'Opportunity Type', 'Opportunity ID',\n", "       'Opportunity Name', 'Oppty Line Item ID', 'Probability (%)', 'Segment',\n", "       'Large Account', 'Marketing Part Number (MPN)', 'Total Mac Demand',\n", "       'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Leasing or Not',\n", "       'Industry Target Account', 'Penetrated Account', 'Source Detail',\n", "       'Sales Region'],\n", "      dtype='object')"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df2.columns"]}, {"cell_type": "code", "execution_count": 12, "id": "5a7afe19-45fc-4965-aff6-f75c32cf61f2", "metadata": {}, "outputs": [], "source": ["df.columns = df2.columns"]}, {"cell_type": "code", "execution_count": 13, "id": "02186f27-65a3-4630-9b22-370cab62d0e3", "metadata": {"tags": []}, "outputs": [], "source": ["# 查找布尔类型的列并转换为 1 和 0\n", "for col in df.select_dtypes(include='bool').columns:\n", "    df[col] = df[col].astype(int)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "0e62dc76-7f74-4956-8ea4-03c2866ae5cb", "metadata": {"tags": []}, "outputs": [], "source": ["# df[df['Account Group']=='天津航空有限责任公司']['FY24Q3 Top Account']"]}, {"cell_type": "code", "execution_count": 15, "id": "0c1fe850-a4ff-4c95-ab79-ca55fe53a938", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data has been saved to All_Top_List.csv\n"]}], "source": ["\n", "\n", "# Save to CSV\n", "df.to_csv(f'{homepath}/Documents/ML/started/ying_support/MacTopAccount/All_Top_List.csv', index=False)\n", "\n", "print(\"Data has been saved to All_Top_List.csv\")"]}, {"cell_type": "code", "execution_count": 16, "id": "ac5f8803-f780-4a0b-8150-267ee017fef0", "metadata": {}, "outputs": [], "source": ["# df2 = pd.read_csv('report1716360761066.csv')\n", "\n", "\n", "# df11 = df[['Name','Owner.Name']].drop_duplicates()\n", "# df11.columns=['Account Name','Account Owner']\n", "\n", "# df11\n", "\n", "# df21 = df2[['Account Name','Account Owner']].drop_duplicates()\n", "\n", "# df21 = df21[~df21['Account Name'].isna()]\n", "# df21['Flag'] = 'Yes'\n", "\n", "# df12 = df11.merge(df21, on=['Account Name','Account Owner'], how='left')\n", "# # df12['flag']=np.where(df12['Account Owner_x']!=df12['Account Owner_y'], <PERSON><PERSON><PERSON>, True)\n", "\n", "\n", "# df12[df12['Flag']=='Yes']\n", "\n", "# df12[df12['Flag'].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "1c471ad4-c9d5-4e69-b7de-8df851f7d838", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}