{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bffc08c3-8517-491e-b2ee-97d97a688962", "metadata": {}, "outputs": [], "source": ["import os\n", "import datetime\n", "import configparser\n", "import requests\n", "import json\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "id": "bd75d82a-d8e0-453d-a822-aff1c3addcf0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "b00c9ef0-1f6d-4e7a-bf35-fe84bd6a9211", "metadata": {}, "outputs": [], "source": ["from acquire_mapping import Mapping"]}, {"cell_type": "code", "execution_count": 4, "id": "0257f3a9-3d08-44ca-bb34-94ad1e5ab353", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 5, "id": "ff3bfce2-8b17-4965-a068-f6be08355b51", "metadata": {}, "outputs": [], "source": ["fiscaldate_mapping = Mapping.fiscaldate\n", "fiscaldate_mapping2 = fiscaldate_mapping.copy()[['Date','week_in_fiscal_quarter','fiscal_qtr_year_name']]\n", "fiscaldate_mapping2['week_in_fiscal_quarter'] = fiscaldate_mapping2['week_in_fiscal_quarter'].str.slice(1)\n", "fiscaldate_mapping2.rename(columns={'Date':'fiscal_dt'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "56e1853b-0edf-474f-9860-3cb40474ff23", "metadata": {}, "outputs": [], "source": ["now = datetime.datetime.now().strftime('%Y-%m-%d')\n", "now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"]}, {"cell_type": "code", "execution_count": 7, "id": "2a5750fc-96d8-4091-afd5-d302a21c4e78", "metadata": {}, "outputs": [], "source": ["fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_qtr_year_name'].unique().tolist()[0]\n", "week_in_fiscal_quarter = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['week_in_fiscal_quarter'].unique().tolist()[0][1:]\n", "fiscal_week_year_name = fiscaldate_mapping[fiscaldate_mapping['Date']==now]['fiscal_week_year_name'].unique().tolist()[0]\n", "# 如果是单个数字，则在前面添加 '0'\n", "if week_in_fiscal_quarter.isdigit() and len(week_in_fiscal_quarter) == 1:\n", "    week_in_fiscal_quarter2 = '0' + week_in_fiscal_quarter\n", "else:\n", "    week_in_fiscal_quarter2 = week_in_fiscal_quarter\n", "\n", "yqw_addzero = fiscal_qtr_year_name + 'W' + week_in_fiscal_quarter2\n", "next_fiscal_qtr_year_name = fiscaldate_mapping[fiscaldate_mapping['Date'] >= now]['fiscal_qtr_year_name'].unique()[1]"]}, {"cell_type": "code", "execution_count": 8, "id": "330d730e-de9d-418a-8730-8017b8c19ca1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['FY24Q4', 'FY24Q3', 'FY24Q2', 'FY24Q1', 'FY23Q4', 'FY23Q3', 'FY23Q2', 'FY23Q1', 'FY22Q4', 'FY22Q3', 'FY22Q2']\n"]}], "source": ["def generate_fiscal_quarters(start_fiscal, end_fiscal):\n", "    start_year = int(start_fiscal[2:4])\n", "    start_qtr = int(start_fiscal[5])\n", "    end_year = int(end_fiscal[2:4])\n", "    end_qtr = int(end_fiscal[5])\n", "\n", "    fiscal_quarters = []\n", "    \n", "    current_year = start_year\n", "    current_qtr = start_qtr\n", "\n", "    while (current_year > end_year) or (current_year == end_year and current_qtr >= end_qtr):\n", "        fiscal_quarters.append(f\"FY{current_year:02d}Q{current_qtr}\")\n", "        if current_qtr == 1:\n", "            current_qtr = 4\n", "            current_year -= 1\n", "        else:\n", "            current_qtr -= 1\n", "\n", "    return fiscal_quarters\n", "\n", "# Example usage\n", "# fiscal_qtr_year_name = \"FY24Q4\"\n", "end_fiscal = \"FY22Q2\"\n", "\n", "fiscal_quarters_list = generate_fiscal_quarters(fiscal_qtr_year_name, end_fiscal)\n", "print(fiscal_quarters_list)"]}, {"cell_type": "code", "execution_count": 9, "id": "16949f44-14c1-4fe6-8777-8dee59ae5fe6", "metadata": {}, "outputs": [], "source": ["# Generate fiscal_quarters_list_ta ae aem\n", "fiscal_quarters_list_ta = [f\"Account.{fq}_Top_Account__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_ae = [f\"Account.{fq}_AE__c\" for fq in fiscal_quarters_list]\n", "fiscal_quarters_list_aem = [f\"Account.{fq}_AEM__c\" for fq in fiscal_quarters_list]"]}, {"cell_type": "code", "execution_count": 10, "id": "14a241dd-de3c-4f66-a9c7-1d22af5e0331", "metadata": {}, "outputs": [], "source": ["# Combine all the lists into one\n", "all_fiscal_quarters_columns = fiscal_quarters_list_ta + fiscal_quarters_list_ae + fiscal_quarters_list_aem"]}, {"cell_type": "code", "execution_count": 11, "id": "fbebb1de-4648-4531-bec3-752b2ddc5ff7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQ4AQHuGRgiN_DiHYrV7668Mcp7EpYyXzZaqCCkn2qa0Kypag1.COSp_pHS741X7k0HCw6pwXxEI.omgViblrGfn5PvOUCIE'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 12, "id": "b3d3ee33-ab71-439c-a002-4adc12fbb0ae", "metadata": {}, "outputs": [], "source": ["# sql_statement = \"\"\"\n", "# SELECT \n", "#     Account.Account_Group__c, \n", "#     Account.Group_Province__c, \n", "#     Account.Group_City__c, \n", "#     Account.Province__c, \n", "#     Account.City__c, \n", "#     Opportunity.Project_Type__c, \n", "#     Account.Disti_T1_Reseller__c, \n", "#     Opportunity.Sold_To_ID__c, \n", "#     Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "#     Opportunity.T2_Reseller__c, \n", "#     Opportunity.Apple_HQ_ID__c, \n", "#     Opportunity.Opportunity_Reseller_Track__c, \n", "#     Opportunity.ESC_Store__c, \n", "#     Account.Sub_Segment__c, \n", "#     Account.Vertical_Industry__c, \n", "#     Account.<PERSON>_as_Choice__c, \n", "#     Account.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__c, \n", "#     Account.<PERSON>_as_Choice_start_time__c, \n", "#     Account.Top_Account_Deep_Dive__c, \n", "#     Account.FY21Q3_Large_Account__c, \n", "#     Account.FY21Q4_Large_Account__c, \n", "#     Account.FY22Q1__c, \n", "#     Account.FY21Q2_AE__c, \n", "#     Account.FY21Q2_AEM__c, \n", "#     Account.FY21Q3_AE__c, \n", "#     Account.FY21Q3_AEM__c, \n", "#     Account.FY21Q4_AE__c, \n", "#     Account.FY21Q4_AEM__c,\n", "#     Account.FY22Q1_AE__c, \n", "#     Account.FY22Q1_AEM__c, \n", "#     Opportunity.Opportunity_Type__c, \n", "#     Account.Segment__c, \n", "#     Account.Large_Account__c, \n", "#     Account.Total_Mac_Demand__c, \n", "#     Account.PC_Install_Base__c, \n", "#     Account.FY22_Fcst__c, \n", "#     Account.FY23_Fcst__c, \n", "#     Opportunity.leasingornot__c, \n", "#     Account.Industry_Target_Account__c, \n", "#     Opportunity.Penetrated_Account__c, \n", "#     Account.Source_Detail__c, \n", "#     Account.Sales_Region__c,\n", "#     Account.Name,\n", "#     Opportunity.Account.Owner.Name,\n", "#     Account.Account_ID__c,\n", "#     Opportunity.OPPORTUNITY_ID__c,\n", "#     Opportunity.Name,\n", "#     Opportunity.Probability,\n", "#     Opportunity.JD_Appended__c,\n", "#     (SELECT \n", "#         Revenue__c, \n", "#         FY__c, \n", "#         ST_FYQuarter__c, \n", "#         Product_Family__c,\n", "#         Quarter__c, \n", "#         Sell_Through_Week__c, \n", "#         Oppty_Line_Item_ID__c, \n", "#         Marketing_Part_Number_MPN__c,\n", "#         Quantity,\n", "#         Line_of_business2__c\n", "#      FROM OpportunityLineItems)\n", "# FROM Opportunity\n", "# WHERE  Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT'\n", "# and Id IN (\n", "#         SELECT OpportunityId \n", "#         FROM OpportunityLineItem \n", "#         WHERE FY__c > 'FY23' \n", "#     )\n", "#     limit 100\n", "\n", "\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "371c91af-eb1e-48ed-ba9d-06f07d1548d2", "metadata": {}, "outputs": [], "source": ["sql_statement = \"\"\"\n", "SELECT \n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "\n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__c, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "\n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "\n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c,\n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__c, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Opportunity.Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.OPPORTUNITY_ID__c,\n", "    Opportunity.Name,\n", "    Opportunity.Probability,\n", "    Opportunity.JD_Appended__c,\n", "    Opportunity.Account.Mac_as_Choice__c,\n", "    Opportunity.Account.Top_Account_Deep_Dive__c,\n", "    Opportunity.Apple_Reseller__r.Name,\n", "    (SELECT \n", "        Revenue__c, \n", "        FY__c, \n", "        ST_FYQuarter__c, \n", "        Product_Family__c,\n", "        Quarter__c, \n", "        Sell_Through_Week__c, \n", "        Oppty_Line_Item_ID__c, \n", "        Marketing_Part_Number_MPN__c,\n", "        Quantity,\n", "        Line_of_business2__c\n", "     FROM OpportunityLineItems)\n", "FROM Opportunity\n", "WHERE  Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT'\n", "and Id IN (\n", "        SELECT OpportunityId \n", "        FROM OpportunityLineItem \n", "        WHERE FY__c > 'FY20' \n", "    )\n", "    \n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 14, "id": "ffcfbc02-4cc9-4b1b-8091-b4a2a1119e7e", "metadata": {}, "outputs": [], "source": ["# Insert the fiscal quarters columns after 'Account.FY22Q1__c,'\n", "insert_point = sql_statement.find(\"Account.FY22Q1__c,\") + len(\"Account.FY22Q1__c,\")\n", "sql_statement = sql_statement[:insert_point] + \"\\n    \" + \",\\n    \".join(all_fiscal_quarters_columns) + \",\" + sql_statement[insert_point:]"]}, {"cell_type": "code", "execution_count": 15, "id": "9dadc1c4-2006-49f0-995e-18c65bffdc8b", "metadata": {}, "outputs": [], "source": ["\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")"]}, {"cell_type": "code", "execution_count": 16, "id": "58f58cec-f923-4c69-a5d4-f840a6ae25c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"SELECT Account.Account_Group__c, Account.Group_Province__c, Account.Group_City__c, Account.Province__c, Account.City__c, Opportunity.Project_Type__c, Opportunity.Sold_To_ID__c, Opportunity.Opportunity_Reseller_Apple_ID__c, Opportunity.T2_Reseller__c, Opportunity.Apple_HQ_ID__c, Opportunity.Opportunity_Reseller_Track__c, Opportunity.ESC_Store__c, Account.Sub_Segment__c, Account.Vertical_Industry__c, Account.zhanbaoshijian__c, Account.Mac_as_Choice_start_time__c, Account.FY21Q3_Large_Account__c, Account.FY21Q4_Large_Account__c, Account.FY22Q1__c, Account.FY24Q4_Top_Account__c, Account.FY24Q3_Top_Account__c, Account.FY24Q2_Top_Account__c, Account.FY24Q1_Top_Account__c, Account.FY23Q4_Top_Account__c, Account.FY23Q3_Top_Account__c, Account.FY23Q2_Top_Account__c, Account.FY23Q1_Top_Account__c, Account.FY22Q4_Top_Account__c, Account.FY22Q3_Top_Account__c, Account.FY22Q2_Top_Account__c, Account.FY24Q4_AE__c, Account.FY24Q3_AE__c, Account.FY24Q2_AE__c, Account.FY24Q1_AE__c, Account.FY23Q4_AE__c, Account.FY23Q3_AE__c, Account.FY23Q2_AE__c, Account.FY23Q1_AE__c, Account.FY22Q4_AE__c, Account.FY22Q3_AE__c, Account.FY22Q2_AE__c, Account.FY24Q4_AEM__c, Account.FY24Q3_AEM__c, Account.FY24Q2_AEM__c, Account.FY24Q1_AEM__c, Account.FY23Q4_AEM__c, Account.FY23Q3_AEM__c, Account.FY23Q2_AEM__c, Account.FY23Q1_AEM__c, Account.FY22Q4_AEM__c, Account.FY22Q3_AEM__c, Account.FY22Q2_AEM__c, Account.FY21Q2_AE__c, Account.FY21Q2_AEM__c, Account.FY21Q3_AE__c, Account.FY21Q3_AEM__c, Account.FY21Q4_AE__c, Account.FY21Q4_AEM__c, Account.FY22Q1_AE__c, Account.FY22Q1_AEM__c, Opportunity.Opportunity_Type__c, Account.Segment__c, Account.Large_Account__c, Account.Total_Mac_Demand__c, Account.PC_Install_Base__c, Account.FY22_Fcst__c, Account.FY23_Fcst__c, Opportunity.leasingornot__c, Account.Industry_Target_Account__c, Opportunity.Penetrated_Account__c, Account.Source_Detail__c, Account.Sales_Region__c, Account.Name, Opportunity.Account.Owner.Name, Account.Account_ID__c, Opportunity.OPPORTUNITY_ID__c, Opportunity.Name, Opportunity.Probability, Opportunity.JD_Appended__c, Opportunity.Account.Mac_as_Choice__c, Opportunity.Account.Top_Account_Deep_Dive__c, Opportunity.Apple_Reseller__r.Name, (SELECT Revenue__c, FY__c, ST_FYQuarter__c, Product_Family__c, Quarter__c, Sell_Through_Week__c, Oppty_Line_Item_ID__c, Marketing_Part_Number_MPN__c, Quantity, Line_of_business2__c FROM OpportunityLineItems) FROM Opportunity WHERE Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT' and Id IN ( SELECT OpportunityId FROM OpportunityLineItem WHERE FY__c > 'FY20' )\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["sql_statement_single_line"]}, {"cell_type": "code", "execution_count": null, "id": "546ccdf1-4de3-4e4a-9a42-587939b6201a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "30fd21f7-86e6-4fff-8fb2-406e48aabfe1", "metadata": {}, "outputs": [], "source": ["\n", "# # for check keyong\n", "# # Initial URL and headers\n", "# base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# # access_token = \"00D20000000JPf6!AQ4AQDJU3_dhyicWbI4oh7DhiRhiAGomTWxU5SPWEc.RW2p1LaiZUlKCMx68rVWu1gSvVcFfbH3gEPUBrFLup0rQw576pYml\"  # Replace with your actual access token\n", "# headers = {\n", "#     'Authorization': f'Bear<PERSON> {access_token}',\n", "#     'Content-Type': 'application/json'\n", "# }\n", "\n", "# def get_salesforce_data(url):\n", "#     response = requests.get(url, headers=headers)\n", "#     response.raise_for_status()  # Ensure we raise an error for bad responses\n", "#     data = response.json()\n", "#     return data\n", "\n", "\n", "\n", "\n", "\n", "# # Construct the full URL\n", "# url = f\"{base_url}{sql_statement_single_line}\"\n", "# url"]}, {"cell_type": "code", "execution_count": 18, "id": "cd52a5b1-40f5-4b20-8507-506dba08b0b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-487\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-729\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-1144\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-1471\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-1793\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-2226\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-2540\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-2821\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-3068\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-3316\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-3562\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-3874\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-4276\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-4594\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-4639\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-4642\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-4862\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-5190\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-5515\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-5858\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-6113\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-6375\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-6792\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-7181\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-7506\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-7744\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-8014\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-8299\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-8620\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-8838\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-8979\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-9134\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-9468\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-9882\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-10311\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-10676\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-11112\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-11498\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-11579\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-11869\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-12285\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-12677\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-12870\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-13046\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-13339\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-13703\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-14028\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-14292\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-14574\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-14883\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-15276\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-15683\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-16079\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-16397\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-16745\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-17056\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-17208\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-17434\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-17625\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-17643\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-18038\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-18413\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-18559\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-18773\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-18980\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-19317\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-19672\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-20132\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-20357\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-20600\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-20922\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-21263\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-21573\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-21941\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-22454\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-22799\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-23128\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-23325\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-23596\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-23832\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24315\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24739\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24867\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24875\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24878\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24881\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24883\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24888\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24891\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-24894\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-25015\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-25293\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-25410\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-25700\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-25980\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-26410\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-26732\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-26976\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-27143\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-27540\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-27827\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-28166\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-28526\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-28762\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-29089\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-29397\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-29675\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-29791\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-30036\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-30309\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-30520\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-30779\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-31065\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-31209\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-31435\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-31732\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-32051\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-32320\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-32573\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-33063\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx3CErjf0M3pAGE-33697\n"]}], "source": ["\n", "# for check keyong\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"00D20000000JPf6!AQ4AQDJU3_dhyicWbI4oh7DhiRhiAGomTWxU5SPWEc.RW2p1LaiZUlKCMx68rVWu1gSvVcFfbH3gEPUBrFLup0rQw576pYml\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "\n", "\n", "\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# # Optionally, save to a file\n", "# with open('salesforce_data_fordebug.json', 'w') as f:\n", "#     json.dump(all_records, f, indent=4)\n", "\n", "# print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "142c6793-a21a-4ad0-a91e-409875ef95a5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "77b2d27f-a102-487e-baad-e9cd24c3406b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f384cda-79bd-4c8b-ab51-7efefce2b707", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 19, "id": "28cd0594-e510-4874-9091-dc7cdb4dbe90", "metadata": {}, "outputs": [], "source": ["\n", "# # 将 JSON 数据导出并美化\n", "# with open('ppl.json', 'w', encoding='utf-8') as json_file:\n", "#     json.dump(all_records, json_file, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": 20, "id": "5c640dcf-a3d9-41d5-bbc9-fa4cb4661e8c", "metadata": {}, "outputs": [{"data": {"text/plain": ["['attributes',\n", " 'Account',\n", " 'Project_Type__c',\n", " 'Sold_To_ID__c',\n", " 'Opportunity_Reseller_Apple_ID__c',\n", " 'T2_Reseller__c',\n", " 'Apple_HQ_ID__c',\n", " 'Opportunity_Reseller_Track__c',\n", " 'ESC_Store__c',\n", " 'Opportunity_Type__c',\n", " 'leasingornot__c',\n", " 'Penetrated_Account__c',\n", " 'Opportunity_ID__c',\n", " 'Name',\n", " 'Probability',\n", " 'JD_Appended__c',\n", " 'Apple_Reseller__r']"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["meta = list(all_records[0].keys())\n", "# 移除 'OpportunityLineItems' 元素\n", "if 'OpportunityLineItems' in meta:\n", "    meta.remove('OpportunityLineItems')\n", "\n", "# 打印结果\n", "meta"]}, {"cell_type": "code", "execution_count": 21, "id": "c36d5345-d8a9-4302-ae28-b15d33dd1679", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Opportunity_Reseller_Track__c</th>\n", "      <th>ESC_Store__c</th>\n", "      <th>Opportunity_Type__c</th>\n", "      <th>leasingornot__c</th>\n", "      <th>Penetrated_Account__c</th>\n", "      <th>Opportunity_ID__c</th>\n", "      <th>Name</th>\n", "      <th>Probability</th>\n", "      <th>JD_Appended__c</th>\n", "      <th>Apple_Reseller__r</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>79755.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12 Pro Max</td>\n", "      <td>Q1</td>\n", "      <td>W10</td>\n", "      <td>00k6F00001BdbfX</td>\n", "      <td>MGC73CH/A</td>\n", "      <td>65.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F000016A2Zj</td>\n", "      <td>新品采购-ESC</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3340.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12 mini</td>\n", "      <td>Q1</td>\n", "      <td>W07</td>\n", "      <td>00k6F00001BcOIy</td>\n", "      <td>MG7Y3CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F0000237ljIQAQ</td>\n", "      <td>Solution</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F000016A2Zj</td>\n", "      <td>新品采购-ESC</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27690.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12</td>\n", "      <td>Q1</td>\n", "      <td>W09</td>\n", "      <td>00k6F00001BcpjM</td>\n", "      <td>MGH13CH/A</td>\n", "      <td>30.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F0000169zuz</td>\n", "      <td>2020年度教学设备采购项目</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15300.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12</td>\n", "      <td>Q1</td>\n", "      <td>W08</td>\n", "      <td>00k6F00001BPk3s</td>\n", "      <td>MGGM3CH/A</td>\n", "      <td>20.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F0000169zuz</td>\n", "      <td>2020年度教学设备采购项目</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5760.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q3</td>\n", "      <td>iPad Pro Wifi 11 2nd Gen</td>\n", "      <td>Q3</td>\n", "      <td>W05</td>\n", "      <td>00k6F00001C59fA</td>\n", "      <td>MXDG2CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>None</td>\n", "      <td>Office Use</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0066F000018Skpd</td>\n", "      <td>2021年度教学设备采购项目</td>\n", "      <td>100.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203526</th>\n", "      <td>1475.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS0000074ueg</td>\n", "      <td>Z1BU</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F000048n9ECQAY</td>\n", "      <td>Employee Choice</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003giCc</td>\n", "      <td>日常办公</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203527</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F00004Fk41zQAB</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003giIP</td>\n", "      <td>办公自用</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203528</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F00004A7QLBQA3</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003giI0</td>\n", "      <td>办公用机</td>\n", "      <td>75.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203529</th>\n", "      <td>3661.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F000048n9GDQAY</td>\n", "      <td>Gifting</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS0000044cJh</td>\n", "      <td>零售</td>\n", "      <td>25.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203530</th>\n", "      <td>2403.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>ENT</td>\n", "      <td>0016F00004Fk41zQAB</td>\n", "      <td>Solution</td>\n", "      <td>No</td>\n", "      <td>None</td>\n", "      <td>006IS000003gqRq</td>\n", "      <td>办公自用</td>\n", "      <td>25.0</td>\n", "      <td>False</td>\n", "      <td>{'attributes': {'type': 'User', 'url': '/servi...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>203531 rows × 29 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c         Product_Family__c Quarter__c  \\\n", "0          79755.0  FY21          FY21Q1         iPhone 12 Pro Max         Q1   \n", "1           3340.0  FY21          FY21Q1            iPhone 12 mini         Q1   \n", "2          27690.0  FY21          FY21Q1                 iPhone 12         Q1   \n", "3          15300.0  FY21          FY21Q1                 iPhone 12         Q1   \n", "4           5760.0  FY21          FY21Q3  iPad Pro Wifi 11 2nd Gen         Q3   \n", "...            ...   ...             ...                       ...        ...   \n", "203526      1475.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203527      1291.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203528      1291.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203529      3661.0  FY24          FY24Q4                Vision Pro         Q4   \n", "203530      2403.0  FY24          FY24Q4                      iMac         Q4   \n", "\n", "       Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0                       W10       00k6F00001BdbfX   \n", "1                       W07       00k6F00001BcOIy   \n", "2                       W09       00k6F00001BcpjM   \n", "3                       W08       00k6F00001BPk3s   \n", "4                       W05       00k6F00001C59fA   \n", "...                     ...                   ...   \n", "203526                  W10       00kIS0000074ueg   \n", "203527                  W10       00kIS00000757ZB   \n", "203528                  W09       00kIS0000074ual   \n", "203529                  W02       00kIS0000077qYX   \n", "203530                  W12       00kIS00000757Un   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MGC73CH/A      65.0               iPhone  ...   \n", "1                         MG7Y3CH/A       5.0               iPhone  ...   \n", "2                         MGH13CH/A      30.0               iPhone  ...   \n", "3                         MGGM3CH/A      20.0               iPhone  ...   \n", "4                         MXDG2CH/A       5.0                 iPad  ...   \n", "...                             ...       ...                  ...  ...   \n", "203526                         Z1BU       1.0                  CPU  ...   \n", "203527                         Z1BB       1.0                  CPU  ...   \n", "203528                         Z1BP       1.0                  CPU  ...   \n", "203529                    MW8X3CH/A       1.0               Vision  ...   \n", "203530                         Z19Q       1.0                  CPU  ...   \n", "\n", "       Opportunity_Reseller_Track__c        ESC_Store__c Opportunity_Type__c  \\\n", "0                                ENT  0016F0000237ljIQAQ            Solution   \n", "1                                ENT  0016F0000237ljIQAQ            Solution   \n", "2                                ENT                None          Office Use   \n", "3                                ENT                None          Office Use   \n", "4                                ENT                None          Office Use   \n", "...                              ...                 ...                 ...   \n", "203526                           ENT  0016F000048n9ECQAY     Employee Choice   \n", "203527                           ENT  0016F00004Fk41zQAB            Solution   \n", "203528                           ENT  0016F00004A7QLBQA3            Solution   \n", "203529                           ENT  0016F000048n9GDQAY             Gifting   \n", "203530                           ENT  0016F00004Fk41zQAB            Solution   \n", "\n", "       leasingornot__c Penetrated_Account__c Opportunity_ID__c  \\\n", "0                 None                  None   0066F000016A2Zj   \n", "1                 None                  None   0066F000016A2Zj   \n", "2                 None                  None   0066F0000169zuz   \n", "3                 None                  None   0066F0000169zuz   \n", "4                 None                  None   0066F000018Skpd   \n", "...                ...                   ...               ...   \n", "203526              No                  None   006IS000003giCc   \n", "203527              No                  None   006IS000003giIP   \n", "203528              No                  None   006IS000003giI0   \n", "203529              No                  None   006IS0000044cJh   \n", "203530              No                  None   006IS000003gqRq   \n", "\n", "                  Name Probability JD_Appended__c  \\\n", "0             新品采购-ESC       100.0          False   \n", "1             新品采购-ESC       100.0          False   \n", "2       2020年度教学设备采购项目       100.0          False   \n", "3       2020年度教学设备采购项目       100.0          False   \n", "4       2021年度教学设备采购项目       100.0          False   \n", "...                ...         ...            ...   \n", "203526            日常办公        75.0          False   \n", "203527            办公自用        75.0          False   \n", "203528            办公用机        75.0          False   \n", "203529              零售        25.0          False   \n", "203530            办公自用        25.0          False   \n", "\n", "                                        Apple_Reseller__r  \n", "0       {'attributes': {'type': 'User', 'url': '/servi...  \n", "1       {'attributes': {'type': 'User', 'url': '/servi...  \n", "2       {'attributes': {'type': 'User', 'url': '/servi...  \n", "3       {'attributes': {'type': 'User', 'url': '/servi...  \n", "4       {'attributes': {'type': 'User', 'url': '/servi...  \n", "...                                                   ...  \n", "203526  {'attributes': {'type': 'User', 'url': '/servi...  \n", "203527  {'attributes': {'type': 'User', 'url': '/servi...  \n", "203528  {'attributes': {'type': 'User', 'url': '/servi...  \n", "203529  {'attributes': {'type': 'User', 'url': '/servi...  \n", "203530  {'attributes': {'type': 'User', 'url': '/servi...  \n", "\n", "[203531 rows x 29 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 将嵌套的 OpportunityLineItems 展开到 DataFrame\n", "df_opportunity_line_items = pd.json_normalize(\n", "    all_records,\n", "    record_path=['OpportunityLineItems', 'records'],\n", "    meta=meta\n", "    \n", ")\n", "\n", "# 打印或保存 DataFrame\n", "df_opportunity_line_items\n", "# df_opportunity_line_items.to_csv('salesforce_data.csv', index=False)  # 可选：保存到 CSV 文件"]}, {"cell_type": "code", "execution_count": 22, "id": "895932f2-2be4-4168-ba88-229794eed97e", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Sales_Region__c</th>\n", "      <th>Name_account</th>\n", "      <th>Account_ID__c</th>\n", "      <th><PERSON>_as_Choice__c</th>\n", "      <th>Top_Account_Deep_Dive__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>79755.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12 Pro Max</td>\n", "      <td>Q1</td>\n", "      <td>W10</td>\n", "      <td>00k6F00001BdbfX</td>\n", "      <td>MGC73CH/A</td>\n", "      <td>65.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>北京爱奇艺科技有限公司上海分公司</td>\n", "      <td>0016F00002KgHKI</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3340.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12 mini</td>\n", "      <td>Q1</td>\n", "      <td>W07</td>\n", "      <td>00k6F00001BcOIy</td>\n", "      <td>MG7Y3CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>北京爱奇艺科技有限公司上海分公司</td>\n", "      <td>0016F00002KgHKI</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27690.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12</td>\n", "      <td>Q1</td>\n", "      <td>W09</td>\n", "      <td>00k6F00001BcpjM</td>\n", "      <td>MGH13CH/A</td>\n", "      <td>30.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>北京高思博乐教育科技股份有限公司</td>\n", "      <td>0016F00003lHScr</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15300.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12</td>\n", "      <td>Q1</td>\n", "      <td>W08</td>\n", "      <td>00k6F00001BPk3s</td>\n", "      <td>MGGM3CH/A</td>\n", "      <td>20.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>北京高思博乐教育科技股份有限公司</td>\n", "      <td>0016F00003lHScr</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5760.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q3</td>\n", "      <td>iPad Pro Wifi 11 2nd Gen</td>\n", "      <td>Q3</td>\n", "      <td>W05</td>\n", "      <td>00k6F00001C59fA</td>\n", "      <td>MXDG2CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>北京高思博乐教育科技股份有限公司</td>\n", "      <td>0016F00003lHScr</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203526</th>\n", "      <td>1475.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS0000074ueg</td>\n", "      <td>Z1BU</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>浙江大运盈通数据信息服务有限公司</td>\n", "      <td>001IS000005rVUN</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203527</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>上海奶叔健康科技集团有限公司</td>\n", "      <td>001IS000005rVZy</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203528</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>无锡市富邦包装材料有限公司</td>\n", "      <td>001IS000005rVZZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203529</th>\n", "      <td>3661.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>青州泰欣工贸有限公司</td>\n", "      <td>001IS000006XRVj</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203530</th>\n", "      <td>2403.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>China</td>\n", "      <td>上海富安娜家纺有限公司</td>\n", "      <td>001IS000005ri5d</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>203531 rows × 99 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c         Product_Family__c Quarter__c  \\\n", "0          79755.0  FY21          FY21Q1         iPhone 12 Pro Max         Q1   \n", "1           3340.0  FY21          FY21Q1            iPhone 12 mini         Q1   \n", "2          27690.0  FY21          FY21Q1                 iPhone 12         Q1   \n", "3          15300.0  FY21          FY21Q1                 iPhone 12         Q1   \n", "4           5760.0  FY21          FY21Q3  iPad Pro Wifi 11 2nd Gen         Q3   \n", "...            ...   ...             ...                       ...        ...   \n", "203526      1475.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203527      1291.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203528      1291.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203529      3661.0  FY24          FY24Q4                Vision Pro         Q4   \n", "203530      2403.0  FY24          FY24Q4                      iMac         Q4   \n", "\n", "       Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0                       W10       00k6F00001BdbfX   \n", "1                       W07       00k6F00001BcOIy   \n", "2                       W09       00k6F00001BcpjM   \n", "3                       W08       00k6F00001BPk3s   \n", "4                       W05       00k6F00001C59fA   \n", "...                     ...                   ...   \n", "203526                  W10       00kIS0000074ueg   \n", "203527                  W10       00kIS00000757ZB   \n", "203528                  W09       00kIS0000074ual   \n", "203529                  W02       00kIS0000077qYX   \n", "203530                  W12       00kIS00000757Un   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MGC73CH/A      65.0               iPhone  ...   \n", "1                         MG7Y3CH/A       5.0               iPhone  ...   \n", "2                         MGH13CH/A      30.0               iPhone  ...   \n", "3                         MGGM3CH/A      20.0               iPhone  ...   \n", "4                         MXDG2CH/A       5.0                 iPad  ...   \n", "...                             ...       ...                  ...  ...   \n", "203526                         Z1BU       1.0                  CPU  ...   \n", "203527                         Z1BB       1.0                  CPU  ...   \n", "203528                         Z1BP       1.0                  CPU  ...   \n", "203529                    MW8X3CH/A       1.0               Vision  ...   \n", "203530                         Z19Q       1.0                  CPU  ...   \n", "\n", "       Sales_Region__c      Name_account    Account_ID__c Mac_as_Choice__c  \\\n", "0                China  北京爱奇艺科技有限公司上海分公司  0016F00002KgHKI            False   \n", "1                China  北京爱奇艺科技有限公司上海分公司  0016F00002KgHKI            False   \n", "2                China  北京高思博乐教育科技股份有限公司  0016F00003lHScr            False   \n", "3                China  北京高思博乐教育科技股份有限公司  0016F00003lHScr            False   \n", "4                China  北京高思博乐教育科技股份有限公司  0016F00003lHScr            False   \n", "...                ...               ...              ...              ...   \n", "203526           China  浙江大运盈通数据信息服务有限公司  001IS000005rVUN            False   \n", "203527           China    上海奶叔健康科技集团有限公司  001IS000005rVZy            False   \n", "203528           China     无锡市富邦包装材料有限公司  001IS000005rVZZ            False   \n", "203529           China        青州泰欣工贸有限公司  001IS000006XRVj            False   \n", "203530           China       上海富安娜家纺有限公司  001IS000005ri5d            False   \n", "\n", "       Top_Account_Deep_Dive__c attributes.type_account  \\\n", "0                         False                 Account   \n", "1                         False                 Account   \n", "2                         False                 Account   \n", "3                         False                 Account   \n", "4                         False                 Account   \n", "...                         ...                     ...   \n", "203526                    False                 Account   \n", "203527                    False                 Account   \n", "203528                    False                 Account   \n", "203529                    False                 Account   \n", "203530                    False                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/0016F000...   \n", "1       /services/data/v59.0/sobjects/Account/0016F000...   \n", "2       /services/data/v59.0/sobjects/Account/0016F000...   \n", "3       /services/data/v59.0/sobjects/Account/0016F000...   \n", "4       /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                   ...   \n", "203526  /services/data/v59.0/sobjects/Account/001IS000...   \n", "203527  /services/data/v59.0/sobjects/Account/001IS000...   \n", "203528  /services/data/v59.0/sobjects/Account/001IS000...   \n", "203529  /services/data/v59.0/sobjects/Account/001IS000...   \n", "203530  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "203526                  User   \n", "203527                  User   \n", "203528                  User   \n", "203529                  User   \n", "203530                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \n", "0       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>  \n", "1       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>  \n", "2       /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "3       /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "4       /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "...                                                   ...            ...  \n", "203526  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "203527  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "203528  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "203529  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "203530  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner  \n", "\n", "[203531 rows x 99 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Account'])\n", "\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Account']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_account'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items"]}, {"cell_type": "code", "execution_count": 23, "id": "b01d3147-de43-43ea-b000-89df8296b3a4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Revenue__c</th>\n", "      <th>FY__c</th>\n", "      <th>ST_FYQuarter__c</th>\n", "      <th>Product_Family__c</th>\n", "      <th>Quarter__c</th>\n", "      <th>Sell_Through_Week__c</th>\n", "      <th>Oppty_Line_Item_ID__c</th>\n", "      <th>Marketing_Part_Number_MPN__c</th>\n", "      <th>Quantity</th>\n", "      <th>Line_of_business2__c</th>\n", "      <th>...</th>\n", "      <th>Name_account</th>\n", "      <th>Account_ID__c</th>\n", "      <th><PERSON>_as_Choice__c</th>\n", "      <th>Top_Account_Deep_Dive__c</th>\n", "      <th>attributes.type_account</th>\n", "      <th>attributes.url_account</th>\n", "      <th>Owner.attributes.type</th>\n", "      <th>Owner.attributes.url</th>\n", "      <th>Owner.Name</th>\n", "      <th>Name_disti/t1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>79755.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12 Pro Max</td>\n", "      <td>Q1</td>\n", "      <td>W10</td>\n", "      <td>00k6F00001BdbfX</td>\n", "      <td>MGC73CH/A</td>\n", "      <td>65.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>北京爱奇艺科技有限公司上海分公司</td>\n", "      <td>0016F00002KgHKI</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3340.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12 mini</td>\n", "      <td>Q1</td>\n", "      <td>W07</td>\n", "      <td>00k6F00001BcOIy</td>\n", "      <td>MG7Y3CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>北京爱奇艺科技有限公司上海分公司</td>\n", "      <td>0016F00002KgHKI</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/***********...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27690.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12</td>\n", "      <td>Q1</td>\n", "      <td>W09</td>\n", "      <td>00k6F00001BcpjM</td>\n", "      <td>MGH13CH/A</td>\n", "      <td>30.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>北京高思博乐教育科技股份有限公司</td>\n", "      <td>0016F00003lHScr</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>易信通联（天津）信息技术有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15300.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q1</td>\n", "      <td>iPhone 12</td>\n", "      <td>Q1</td>\n", "      <td>W08</td>\n", "      <td>00k6F00001BPk3s</td>\n", "      <td>MGGM3CH/A</td>\n", "      <td>20.0</td>\n", "      <td>iPhone</td>\n", "      <td>...</td>\n", "      <td>北京高思博乐教育科技股份有限公司</td>\n", "      <td>0016F00003lHScr</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>易信通联（天津）信息技术有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5760.0</td>\n", "      <td>FY21</td>\n", "      <td>FY21Q3</td>\n", "      <td>iPad Pro Wifi 11 2nd Gen</td>\n", "      <td>Q3</td>\n", "      <td>W05</td>\n", "      <td>00k6F00001C59fA</td>\n", "      <td>MXDG2CH/A</td>\n", "      <td>5.0</td>\n", "      <td>iPad</td>\n", "      <td>...</td>\n", "      <td>北京高思博乐教育科技股份有限公司</td>\n", "      <td>0016F00003lHScr</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/0016F000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>易信通联（天津）信息技术有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203526</th>\n", "      <td>1475.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS0000074ueg</td>\n", "      <td>Z1BU</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>浙江大运盈通数据信息服务有限公司</td>\n", "      <td>001IS000005rVUN</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203527</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>上海奶叔健康科技集团有限公司</td>\n", "      <td>001IS000005rVZy</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203528</th>\n", "      <td>1291.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>无锡市富邦包装材料有限公司</td>\n", "      <td>001IS000005rVZZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203529</th>\n", "      <td>3661.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>青州泰欣工贸有限公司</td>\n", "      <td>001IS000006XRVj</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203530</th>\n", "      <td>2403.0</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>上海富安娜家纺有限公司</td>\n", "      <td>001IS000005ri5d</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>Account</td>\n", "      <td>/services/data/v59.0/sobjects/Account/001IS000...</td>\n", "      <td>User</td>\n", "      <td>/services/data/v59.0/sobjects/User/0056F00000D...</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>203531 rows × 99 columns</p>\n", "</div>"], "text/plain": ["        Revenue__c FY__c ST_FYQuarter__c         Product_Family__c Quarter__c  \\\n", "0          79755.0  FY21          FY21Q1         iPhone 12 Pro Max         Q1   \n", "1           3340.0  FY21          FY21Q1            iPhone 12 mini         Q1   \n", "2          27690.0  FY21          FY21Q1                 iPhone 12         Q1   \n", "3          15300.0  FY21          FY21Q1                 iPhone 12         Q1   \n", "4           5760.0  FY21          FY21Q3  iPad Pro Wifi 11 2nd Gen         Q3   \n", "...            ...   ...             ...                       ...        ...   \n", "203526      1475.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203527      1291.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203528      1291.0  FY24          FY24Q3               MacBook Air         Q3   \n", "203529      3661.0  FY24          FY24Q4                Vision Pro         Q4   \n", "203530      2403.0  FY24          FY24Q4                      iMac         Q4   \n", "\n", "       Sell_Through_Week__c Oppty_Line_Item_ID__c  \\\n", "0                       W10       00k6F00001BdbfX   \n", "1                       W07       00k6F00001BcOIy   \n", "2                       W09       00k6F00001BcpjM   \n", "3                       W08       00k6F00001BPk3s   \n", "4                       W05       00k6F00001C59fA   \n", "...                     ...                   ...   \n", "203526                  W10       00kIS0000074ueg   \n", "203527                  W10       00kIS00000757ZB   \n", "203528                  W09       00kIS0000074ual   \n", "203529                  W02       00kIS0000077qYX   \n", "203530                  W12       00kIS00000757Un   \n", "\n", "       Marketing_Part_Number_MPN__c  Quantity Line_of_business2__c  ...  \\\n", "0                         MGC73CH/A      65.0               iPhone  ...   \n", "1                         MG7Y3CH/A       5.0               iPhone  ...   \n", "2                         MGH13CH/A      30.0               iPhone  ...   \n", "3                         MGGM3CH/A      20.0               iPhone  ...   \n", "4                         MXDG2CH/A       5.0                 iPad  ...   \n", "...                             ...       ...                  ...  ...   \n", "203526                         Z1BU       1.0                  CPU  ...   \n", "203527                         Z1BB       1.0                  CPU  ...   \n", "203528                         Z1BP       1.0                  CPU  ...   \n", "203529                    MW8X3CH/A       1.0               Vision  ...   \n", "203530                         Z19Q       1.0                  CPU  ...   \n", "\n", "            Name_account    Account_ID__c Mac_as_Choice__c  \\\n", "0       北京爱奇艺科技有限公司上海分公司  0016F00002KgHKI            False   \n", "1       北京爱奇艺科技有限公司上海分公司  0016F00002KgHKI            False   \n", "2       北京高思博乐教育科技股份有限公司  0016F00003lHScr            False   \n", "3       北京高思博乐教育科技股份有限公司  0016F00003lHScr            False   \n", "4       北京高思博乐教育科技股份有限公司  0016F00003lHScr            False   \n", "...                  ...              ...              ...   \n", "203526  浙江大运盈通数据信息服务有限公司  001IS000005rVUN            False   \n", "203527    上海奶叔健康科技集团有限公司  001IS000005rVZy            False   \n", "203528     无锡市富邦包装材料有限公司  001IS000005rVZZ            False   \n", "203529        青州泰欣工贸有限公司  001IS000006XRVj            False   \n", "203530       上海富安娜家纺有限公司  001IS000005ri5d            False   \n", "\n", "       Top_Account_Deep_Dive__c attributes.type_account  \\\n", "0                         False                 Account   \n", "1                         False                 Account   \n", "2                         False                 Account   \n", "3                         False                 Account   \n", "4                         False                 Account   \n", "...                         ...                     ...   \n", "203526                    False                 Account   \n", "203527                    False                 Account   \n", "203528                    False                 Account   \n", "203529                    False                 Account   \n", "203530                    False                 Account   \n", "\n", "                                   attributes.url_account  \\\n", "0       /services/data/v59.0/sobjects/Account/0016F000...   \n", "1       /services/data/v59.0/sobjects/Account/0016F000...   \n", "2       /services/data/v59.0/sobjects/Account/0016F000...   \n", "3       /services/data/v59.0/sobjects/Account/0016F000...   \n", "4       /services/data/v59.0/sobjects/Account/0016F000...   \n", "...                                                   ...   \n", "203526  /services/data/v59.0/sobjects/Account/001IS000...   \n", "203527  /services/data/v59.0/sobjects/Account/001IS000...   \n", "203528  /services/data/v59.0/sobjects/Account/001IS000...   \n", "203529  /services/data/v59.0/sobjects/Account/001IS000...   \n", "203530  /services/data/v59.0/sobjects/Account/001IS000...   \n", "\n", "       Owner.attributes.type  \\\n", "0                       User   \n", "1                       User   \n", "2                       User   \n", "3                       User   \n", "4                       User   \n", "...                      ...   \n", "203526                  User   \n", "203527                  User   \n", "203528                  User   \n", "203529                  User   \n", "203530                  User   \n", "\n", "                                     Owner.attributes.url     Owner.Name  \\\n", "0       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "1       /services/data/v59.0/sobjects/User/***********...    <PERSON><PERSON>   \n", "2       /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "3       /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "4       /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "...                                                   ...            ...   \n", "203526  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "203527  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "203528  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "203529  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "203530  /services/data/v59.0/sobjects/User/0056F00000D...  General Owner   \n", "\n", "           Name_disti/t1  \n", "0         倍升互联（北京）科技有限公司  \n", "1         倍升互联（北京）科技有限公司  \n", "2       易信通联（天津）信息技术有限公司  \n", "3       易信通联（天津）信息技术有限公司  \n", "4       易信通联（天津）信息技术有限公司  \n", "...                  ...  \n", "203526    伟仕佳杰（重庆）科技有限公司  \n", "203527    伟仕佳杰（重庆）科技有限公司  \n", "203528    伟仕佳杰（重庆）科技有限公司  \n", "203529    伟仕佳杰（重庆）科技有限公司  \n", "203530    伟仕佳杰（重庆）科技有限公司  \n", "\n", "[203531 rows x 99 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 Account 列中的 JSON 数据进一步展开\n", "df_account_details = pd.json_normalize(df_opportunity_line_items['Apple_Reseller__r'])\n", "df_account_details = df_account_details[['Name']]\n", "df_account_details.rename(columns={'Name':'Name_disti/t1'}, inplace=True)\n", "# 将展开后的 Account 数据合并回原 DataFrame 中，使用 suffixes 参数避免重叠列冲突\n", "df_opportunity_line_items = df_opportunity_line_items.drop(columns=['Apple_Reseller__r']).join(\n", "    df_account_details, lsuffix='_original', rsuffix='_disti/t1'\n", ")\n", "\n", "# 展示结果\n", "df_opportunity_line_items\n"]}, {"cell_type": "code", "execution_count": null, "id": "a3f69758-3c6b-4e2f-b3e6-1587fd7b9e04", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "id": "dbf7958d-5833-4b75-bb44-bb99fff2169d", "metadata": {}, "outputs": [], "source": ["# 删除列名中的 '__c' 并替换 '_' 为 ' '\n", "df_opportunity_line_items.columns = df_opportunity_line_items.columns.str.replace('__c$', '', regex=True).str.replace('_', ' ')"]}, {"cell_type": "code", "execution_count": 25, "id": "6548de6a-ff70-4439-ab02-d40aaf5e5805", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Revenue', 'FY', 'ST FYQuarter', 'Product Family', 'Quarter', 'Sell Through Week', 'Oppty Line Item ID', 'Marketing Part Number MPN', 'Quantity', 'Line of business2', 'attributes.type original', 'attributes.url original', 'attributes', 'Project Type', 'Sold To ID', 'Opportunity Reseller Apple ID', 'T2 Reseller', 'Apple HQ ID', 'Opportunity Reseller Track', 'ESC Store', 'Opportunity Type', 'leasingornot', 'Penetrated Account', 'Opportunity ID', 'Name original', 'Probability', 'JD Appended', 'Account Group', 'Group Province', 'Group City', 'Province', 'City', 'Sub Segment', 'Vertical Industry', 'zhan<PERSON><PERSON>jian', 'Mac as Choice start time', 'FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY22Q1', 'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account', 'FY24Q4 AE', 'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE', 'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE', 'FY24Q4 AEM', 'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM', 'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM', 'FY21Q2 AE', 'FY21Q2 AEM', 'FY21Q3 AE', 'FY21Q3 AEM', 'FY21Q4 AE', 'FY21Q4 AEM', 'FY22Q1 AE', 'FY22Q1 AEM', 'Segment', 'Large Account', 'Total Mac Demand', 'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Industry Target Account', 'Source Detail', 'Sales Region', 'Name account', 'Account ID', 'Mac as Choice', 'Top Account Deep Dive', 'attributes.type account', 'attributes.url account', 'Owner.attributes.type', 'Owner.attributes.url', 'Owner.Name', 'Name disti/t1']\n"]}], "source": ["print(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 26, "id": "f2534630-014b-4769-bb99-248ef0f71315", "metadata": {}, "outputs": [], "source": ["df_opportunity_line_items.rename(columns={'Name account':'Account Name','Owner.Name':'Account Owner','Apple HQ ID':'Apple ID','Project Type':'Deal type',\n", "    'Name disti/t1':'Disti/T1 Reseller','FY22Q1':'FY22Q1 Large Account',\n", "                                          'leasingornot':'Leasing or Not','Line of business2':'Line of Business','zhanbaoshijian':'Mac as Choice加入时间',\n", "                                         'Mac as Choice start time':'Mac as Choice start time','Marketing Part Number MPN':'Marketing Part Number (MPN)','Probability':'Probability (%)',\n", "                                         'Quarter':'Product ST Quarter','Sell Through Week':'Product ST Week' ,'Revenue':'ProductLineRevenue','Province':'Province/Region',\n", "                                         'Opportunity Reseller Apple ID':'Reseller Apple ID','Opportunity Reseller Track':'Reseller Track'\n", "                                         }, inplace=True)"]}, {"cell_type": "code", "execution_count": 27, "id": "bbb2ad26-b36f-4faa-9438-a5e2f22f8c36", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["df_header_filepath = (f'{homepath}/Library/CloudStorage/Box-Box/Planning\\ Team/Tableau\\ Auto-Refresh\\ Raw\\ Data/19\\ SalesForce\\ PPL/PPL_by_Accountname_FY21_CQ_byapi.csv').replace('\\\\','')\n", "# 对比header\n", "# df_header = pd.read_csv(df_header_filepath).head(10)\n", "# set(list(df_header)) - set(list(df_opportunity_line_items))"]}, {"cell_type": "code", "execution_count": 28, "id": "3938cf4a-3206-4c99-afcb-3ccfbe405f95", "metadata": {}, "outputs": [], "source": ["# 将布尔列转换为 0 和 1\n", "bool_columns = df_opportunity_line_items.select_dtypes(include='bool').columns\n", "df_opportunity_line_items[bool_columns] = df_opportunity_line_items[bool_columns].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "9be1793a-e2f4-4247-a587-a326f4e5ccfa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 34, "id": "05a4bb15-dfa0-4a6c-8b8c-a6097ccebf58", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns removed successfully.\n"]}], "source": ["# 要删除的列列表\n", "columns_to_remove = ['JD Appended','Name original','Owner.attributes.type','Owner.attributes.url',\n", "                     'attributes','attributes.type account','attributes.type original','attributes.url account','attributes.url original'\n", "]\n", "\n", "# 尝试删除列，并在失败时捕获异常\n", "try:\n", "    df_opportunity_line_items.drop(columns=columns_to_remove, inplace=True)\n", "    print(\"Columns removed successfully.\")\n", "except KeyError as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": 35, "id": "3e337560-e2bf-42d2-94ac-a245a9192702", "metadata": {}, "outputs": [], "source": ["df_opportunity_line_items.to_csv(df_header_filepath, index=False)"]}, {"cell_type": "code", "execution_count": 31, "id": "4ade7c72-b559-4e31-91ca-dd55827d25fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['FY21Q3 Large Account', 'FY21Q4 Large Account', 'FY22Q1 Large Account',\n", "       'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account',\n", "       'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account',\n", "       'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account',\n", "       'FY22Q3 Top Account', 'FY22Q2 Top Account', 'Large Account',\n", "       'Industry Target Account', 'Mac as Choice', 'Top Account Deep Dive'],\n", "      dtype='object')"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["bool_columns"]}, {"cell_type": "code", "execution_count": 32, "id": "bfdf5c15-18d6-4b02-9deb-6b2b6141d75b", "metadata": {}, "outputs": [], "source": ["sql_statement = \"\"\"\n", "SELECT \n", "    Account.Account_Group__c, \n", "    Account.Group_Province__c, \n", "    Account.Group_City__c, \n", "    Account.Province__c, \n", "    Account.City__c, \n", "    Opportunity.Project_Type__c, \n", "    Account.Disti_T1_Reseller__c, \n", "    Opportunity.Sold_To_ID__c, \n", "    Opportunity.Opportunity_Reseller_Apple_ID__c, \n", "    Opportunity.T2_Reseller__c, \n", "    Opportunity.Apple_HQ_ID__c, \n", "    Opportunity.Opportunity_Reseller_Track__c, \n", "    Opportunity.ESC_Store__c, \n", "    Account.Sub_Segment__c, \n", "    Account.Vertical_Industry__c, \n", "    Account.<PERSON>_as_Choice__c, \n", "    Account.<PERSON><PERSON><PERSON><PERSON>jian__c, \n", "    Account.<PERSON>_as_Choice_start_time__c, \n", "    Account.Top_Account_Deep_Dive__c, \n", "    Account.FY21Q3_Large_Account__c, \n", "    Account.FY21Q4_Large_Account__c, \n", "    Account.FY22Q1__c, \n", "    Account.FY22Q2_Top_Account__c, \n", "    Account.FY22Q3_Top_Account__c, \n", "    Account.FY22Q4_Top_Account__c, \n", "    Account.FY23Q1_Top_Account__c, \n", "    Account.FY23Q2_Top_Account__c, \n", "    Account.FY23Q3_Top_Account__c, \n", "    Account.FY23Q4_Top_Account__c, \n", "    Account.FY24Q1_Top_Account__c, \n", "    Account.FY24Q2_Top_Account__c, \n", "    Account.FY24Q3_Top_Account__c, \n", "    Account.FY21Q2_AE__c, \n", "    Account.FY21Q2_AEM__c, \n", "    Account.FY21Q3_AE__c, \n", "    Account.FY21Q3_AEM__c, \n", "    Account.FY21Q4_AE__c, \n", "    Account.FY21Q4_AEM__c, \n", "    Account.FY22Q1_AE__c, \n", "    Account.FY22Q1_AEM__c, \n", "    Account.FY22Q2_AE__c, \n", "    Account.FY22Q2_AEM__c, \n", "    Account.FY22Q3_AE__c, \n", "    Account.FY22Q3_AEM__c, \n", "    Account.FY22Q4_AE__c, \n", "    Account.FY22Q4_AEM__c, \n", "    Account.FY23Q1_AE__c, \n", "    Account.FY23Q1_AEM__c, \n", "    Account.FY23Q2_AE__c, \n", "    Account.FY23Q2_AEM__c, \n", "    Account.FY23Q3_AE__c, \n", "    Account.FY23Q3_AEM__c, \n", "    Account.FY23Q4_AE__c, \n", "    Account.FY23Q4_AEM__c, \n", "    Account.FY24Q1_AE__c, \n", "    Account.FY24Q1_AEM__c, \n", "    Account.FY24Q2_AE__c, \n", "    Account.FY24Q2_AEM__c, \n", "    Account.FY24Q3_AE__c, \n", "    Account.FY24Q3_AEM__c, \n", "    Opportunity.Opportunity_Type__c, \n", "    Account.Segment__c, \n", "    Account.Large_Account__c, \n", "    Account.Total_Mac_Demand__c, \n", "    Account.PC_Install_Base__c, \n", "    Account.FY22_Fcst__c, \n", "    Account.FY23_Fcst__c, \n", "    Opportunity.leasingornot__c, \n", "    Account.Industry_Target_Account__c, \n", "    Opportunity.Penetrated_Account__c, \n", "    Account.Source_Detail__c, \n", "    Account.Sales_Region__c,\n", "    Account.Name,\n", "    Opportunity.Account.Owner.Name,\n", "    Account.Account_ID__c,\n", "    Opportunity.OPPORTUNITY_ID__c,\n", "    Opportunity.Name,\n", "    Opportunity.Probability,\n", "    Opportunity.JD_Appended__c,\n", "    (SELECT \n", "        Revenue__c, \n", "        FY__c, \n", "        ST_FYQuarter__c, \n", "        Product_Family__c,\n", "        Quarter__c, \n", "        Sell_Through_Week__c, \n", "        Oppty_Line_Item_ID__c, \n", "        Marketing_Part_Number_MPN__c,\n", "        Quantity,\n", "        Line_of_business2__c\n", "     FROM OpportunityLineItems)\n", "FROM Opportunity\n", "WHERE  Account.Name != NULL and Opportunity.JD_Appended__c = False and Opportunity.Owner.Sales_Region__c = 'China' and Opportunity_Reseller_Track__c = 'ENT'\n", "and Id IN (\n", "        SELECT OpportunityId \n", "        FROM OpportunityLineItem \n", "        WHERE FY__c > 'FY23' \n", "    )\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 33, "id": "8738d5fd-1d1e-4c24-b1f7-04dd2e63eda3", "metadata": {}, "outputs": [], "source": ["\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")"]}, {"cell_type": "code", "execution_count": null, "id": "3e81c82b-6dec-435f-bd56-4cfbbfd270a2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}