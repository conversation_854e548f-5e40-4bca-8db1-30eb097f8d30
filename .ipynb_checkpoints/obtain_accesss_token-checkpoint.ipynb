{"cells": [{"cell_type": "code", "execution_count": 1, "id": "54130d08-b216-43ea-9696-99e14835160e", "metadata": {}, "outputs": [], "source": ["import os\n", "import configparser\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "7c3e0e8d-140b-480a-bc82-caffb7989812", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 10, "id": "1f786a97-2fe9-4979-9abf-4f75d06cedb1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully obtained access token.\n", "Access token saved to config.ini file.\n"]}], "source": ["\n", "\n", "# 读取 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "client_id = config['salesforce']['CLIENT_ID']\n", "client_secret = config['salesforce']['CLIENT_SECRET']\n", "refresh_token = config['salesforce']['REFRESH_TOKEN']\n", "\n", "# Salesforce 实例的令牌 URL\n", "token_url = \"https://login.salesforce.com/services/oauth2/token\"\n", "\n", "# 使用刷新令牌获取新的访问令牌\n", "payload = {\n", "    'grant_type': 'refresh_token',\n", "    'client_id': client_id,\n", "    'client_secret': client_secret,\n", "    'refresh_token': refresh_token\n", "}\n", "headers = {\n", "    'Content-Type': 'application/x-www-form-urlencoded'\n", "}\n", "\n", "response = requests.post(token_url, headers=headers, data=payload)\n", "if response.status_code == 200:\n", "    data = response.json()\n", "    access_token = data['access_token']\n", "    print(\"Successfully obtained access token.\")\n", "    \n", "    # 更新 config.ini 文件\n", "    config['salesforce']['SALESFORCE_ACCESS_TOKEN'] = access_token\n", "    \n", "    with open(f'{homepath}/Documents/myapp/config.ini', 'w') as configfile:\n", "        config.write(configfile)\n", "    print(\"Access token saved to config.ini file.\")\n", "else:\n", "    print(f\"Failed to obtain access token. Status code: {response.status_code}\")\n", "    print(response.text)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "cfb128b5-89c9-4045-a9c3-49f8829282ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQ4AQL1Y_26BKg0zrkr1HQJL5wvHU2pQrWb6lglzl5NONiyVblbGYwi0SKD3ynLkpjt0ttztCTaPuB550n7nrfuL48geu8KP'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["access_token"]}, {"cell_type": "code", "execution_count": null, "id": "98959dbf-8f74-4ec8-81f8-4d6dcad99571", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}