{"cells": [{"cell_type": "code", "execution_count": 94, "id": "9fe2d7fc-5aae-474d-89e5-b6d4a7b9cbb9", "metadata": {}, "outputs": [], "source": ["import os\n", "import configparser\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 95, "id": "9c2b9cbb-425e-4d44-bb97-f58149959be8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 96, "id": "1012df55-d7a4-4175-a52b-f1ee872255da", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 97, "id": "14da3bbd-dd89-4ed9-a158-53b1d557162e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'00D20000000JPf6!AQ4AQL1Y_26BKg0zrkr1HQJL5wvHU2pQrWb6lglzl5NONiyVblbGYwi0SKD3ynLkpjt0ttztCTaPuB550n7nrfuL48geu8KP'"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "# 读取现有的 config.ini 文件\n", "config = configparser.ConfigParser()\n", "config.optionxform = str  # 保持键的大小写\n", "config.read(f'{homepath}/Documents/myapp/config.ini')\n", "\n", "# 获取 Salesforce 配置信息\n", "\n", "access_token = config['salesforce']['SALESFORCE_ACCESS_TOKEN']\n", "access_token"]}, {"cell_type": "code", "execution_count": 12, "id": "1dbb7d8c-2856-48f1-8f66-a804b92432ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Objects used in the report: {'Account', 'ACCOUNT', 'USERS'}\n", "Fields used in the report: ['Account.FY21Q2_Identifier__c', 'Account.FY24Q3_Identifier__c', 'Account.FY22Q1__c', 'Account.FY21Q3_Large_Account__c', 'Account.FY21Q4_Large_Account__c', 'Account.FY24Q2_Top_Account__c', 'Account.FY22Q2_Top_Account__c', 'Account.FY22Q3_Top_Account__c', 'Account.FY22Q4_Top_Account__c', 'Account.FY23Q1_Top_Account__c', 'Account.FY23Q2_Top_Account__c', 'Account.FY23Q3_Top_Account__c', 'Account.FY23Q4_Top_Account__c', 'Account.FY24Q1_Top_Account__c', 'Account.FY24Q3_Top_Account__c', 'Account.FY24Q2_AE__c', 'Account.FY24Q2_AEM__c', 'Account.FY24Q3_AE__c', 'Account.FY24Q3_AEM__c', 'Account.Mac_as_Choice__c', 'Account.Mac_as_Choice_start_time__c', 'Account.SEI_maC__c', 'ACCOUNT.NAME', 'Account.SEI_Account_ID__c', 'Account.SEI_Cluster_ID__c', 'Account.Account_Cluster__c', 'Account.Is_Group__c', 'Account.Account_Group__c', 'Account.SEI_Group_ID__c', 'Account.Account_Group_ID__c', 'Account.Group_Sub_Segment__c', 'Account.Group_Vertical_Industry__c', 'Account.Province__c', 'Account.City__c', 'Account.Group_Province__c', 'Account.Group_City__c', 'USERS.NAME', 'Account.Sub_Segment__c', 'Account.Vertical_Industry__c', 'Account.Top_Account_Deep_Dive__c']\n"]}], "source": ["\n", "\n", "# def get_report_metadata(base_url, report_id, access_token):\n", "#     url = f\"{base_url}/services/data/v59.0/analytics/reports/{report_id}/describe\"\n", "#     headers = {\n", "#         'Authorization': f'<PERSON><PERSON> {access_token}'\n", "#     }\n", "\n", "#     response = requests.get(url, headers=headers)\n", "#     if response.status_code == 200:\n", "#         report_metadata = response.json()\n", "#         return report_metadata\n", "#     else:\n", "#         print(f\"Failed to get report metadata. Status code: {response.status_code}\")\n", "#         return None\n", "\n", "# def extract_objects_and_fields(report_metadata):\n", "#     objects = set()\n", "#     fields = []\n", "\n", "#     # Extract detail columns\n", "#     detail_columns = report_metadata.get('reportMetadata', {}).get('detailColumns', [])\n", "#     for column in detail_columns:\n", "#         # Split the field name to get the object and field\n", "#         parts = column.split('.')\n", "#         if len(parts) == 2:\n", "#             objects.add(parts[0])\n", "#             fields.append(column)\n", "\n", "#     return objects, fields\n", "\n", "# # Replace these values with your actual Salesforce instance URL, report ID, and access token\n", "# base_url = \"https://d20000000jpf6eag.my.salesforce.com\"\n", "# report_id = \"00OIS000001PrpR\"\n", "# # access_token = access_token\n", "\n", "# report_metadata = get_report_metadata(base_url, report_id, access_token)\n", "# if report_metadata:\n", "#     objects, fields = extract_objects_and_fields(report_metadata)\n", "#     print(f\"Objects used in the report: {objects}\")\n", "#     print(f\"Fields used in the report: {fields}\")\n", "# else:\n", "#     print(\"Failed to get report metadata.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "dc63d2af-304c-433d-870e-f95c1669fe51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 101, "id": "063e834d-369d-49c0-a22e-906e8807dc73", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-2000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-4000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-6000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-8000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-10000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-12000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-14000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-16000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-18000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-20000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-22000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-24000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-26000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-28000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-30000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-32000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-34000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-36000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-38000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-40000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-42000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-44000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-46000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-48000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-50000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-52000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-54000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-56000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-58000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-60000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-62000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-64000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-66000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-68000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-70000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-72000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-74000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-76000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-78000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-80000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-82000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-84000\n", "https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query/0r8xx2zKO0UsABQAM3-86000\n", "Data saved to salesforce_data.json\n"]}], "source": ["\n", "\n", "# Initial URL and headers\n", "base_url = \"https://d20000000jpf6eag.my.salesforce.com/services/data/v59.0/query?q=\"\n", "# access_token = \"YOUR_ACCESS_TOKEN\"  # Replace with your actual access token\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "def get_salesforce_data(url):\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()  # Ensure we raise an error for bad responses\n", "    data = response.json()\n", "    return data\n", "\n", "# SOQL query\n", "sql_statement = \"\"\"\n", "SELECT Account.FY21Q2_Identifier__c, Account.FY24Q3_Identifier__c, Account.FY22Q1__c,\n", "Account.FY21Q3_Large_Account__c, Account.FY21Q4_Large_Account__c, Account.FY24Q2_Top_Account__c,\n", "Account.FY22Q2_Top_Account__c, Account.FY22Q3_Top_Account__c, Account.FY22Q4_Top_Account__c,\n", "Account.FY23Q1_Top_Account__c, Account.FY23Q2_Top_Account__c, Account.FY23Q3_Top_Account__c,\n", "Account.FY23Q4_Top_Account__c, Account.FY24Q1_Top_Account__c, Account.FY24Q3_Top_Account__c,\n", "Account.FY24Q2_AE__c, Account.FY24Q2_AEM__c, Account.FY24Q3_AE__c, Account.FY24Q3_AEM__c,\n", "Account.<PERSON>_as_Choice__c, Account.Mac_as_Choice_start_time__c, Account.SEI_maC__c, ACCOUNT.NAME,\n", "Account.SEI_Account_ID__c, Account.SEI_Cluster_ID__c, Account.Account_Cluster__c,\n", "Account.Is_Group__c, Account_ID__c,Account.Account_Group__c, Account.SEI_Group_ID__c, Account.Account_Group_ID__c,\n", "Account.Group_Sub_Segment__c, Account.Group_Vertical_Industry__c, Account.Province__c,\n", "Account.City__c, Account.Group_Province__c, Account.Group_City__c, Owner.Name, Account.Sub_Segment__c,\n", "Account.Vertical_Industry__c, Account.Top_Account_Deep_Dive__c FROM Account \n", "\"\"\"\n", "\n", "# Remove newlines and extra spaces from SQL statement\n", "sql_statement_single_line = \" \".join(sql_statement.split()).replace(\"\\n\", \" \")\n", "\n", "# Construct the full URL\n", "url = f\"{base_url}{sql_statement_single_line}\"\n", "\n", "# Fetch initial data\n", "data = get_salesforce_data(url)\n", "\n", "# Collect all records\n", "all_records = data['records']\n", "\n", "# Check for more data\n", "while not data['done']:\n", "    next_url = \"https://d20000000jpf6eag.my.salesforce.com\" + data['nextRecordsUrl']\n", "    data = get_salesforce_data(next_url)\n", "    print(next_url)\n", "    all_records.extend(data['records'])\n", "\n", "# Print all records\n", "# print(json.dumps(all_records, indent=4))\n", "\n", "# Optionally, save to a file\n", "with open('salesforce_data.json', 'w') as f:\n", "    json.dump(all_records, f, indent=4)\n", "\n", "print(\"Data saved to salesforce_data.json\")\n"]}, {"cell_type": "code", "execution_count": 108, "id": "bcf01baf-7d5c-4fa2-9642-c3295dd02081", "metadata": {}, "outputs": [], "source": ["# Convert to DataFrame\n", "df = pd.json_normalize(all_records)\n"]}, {"cell_type": "code", "execution_count": 112, "id": "c298688f-7094-46ca-af89-5b7027500f6b", "metadata": {}, "outputs": [], "source": ["df.drop(['attributes.type',\n", "       'attributes.url', 'Owner.attributes.type', 'Owner.attributes.url'],axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": 114, "id": "12ad3e57-a23f-479a-af3a-ca65e940ae38", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['FY21Q2_Identifier__c', 'FY24Q3_Identifier__c', 'FY22Q1__c',\n", "       'FY21Q3_Large_Account__c', 'FY21Q4_Large_Account__c',\n", "       'FY24Q2_Top_Account__c', 'FY22Q2_Top_Account__c',\n", "       'FY22Q3_Top_Account__c', 'FY22Q4_Top_Account__c',\n", "       'FY23Q1_Top_Account__c', 'FY23Q2_Top_Account__c',\n", "       'FY23Q3_Top_Account__c', 'FY23Q4_Top_Account__c',\n", "       'FY24Q1_Top_Account__c', 'FY24Q3_Top_Account__c', 'FY24Q2_AE__c',\n", "       'FY24Q2_AEM__c', 'FY24Q3_AE__c', 'FY24Q3_AEM__c', 'Mac_as_Choice__c',\n", "       'Mac_as_Choice_start_time__c', 'SEI_maC__c', 'Name',\n", "       'SEI_Account_ID__c', 'SEI_Cluster_ID__c', 'Account_Cluster__c',\n", "       'Is_Group__c', 'Account_ID__c', 'Account_Group__c', 'SEI_Group_ID__c',\n", "       'Account_Group_ID__c', 'Group_Sub_Segment__c',\n", "       'Group_Vertical_Industry__c', 'Province__c', 'City__c',\n", "       'Group_Province__c', 'Group_City__c', 'Sub_Segment__c',\n", "       'Vertical_Industry__c', 'Top_Account_Deep_Dive__c', 'Owner.Name'],\n", "      dtype='object')"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 115, "id": "02da4f2e-8731-4387-b61f-1e1ce4fbb57e", "metadata": {}, "outputs": [], "source": ["df = df[['FY21Q2_Identifier__c', 'FY24Q3_Identifier__c', 'FY22Q1__c',\n", "       'FY21Q3_Large_Account__c', 'FY21Q4_Large_Account__c',\n", "       'FY24Q2_Top_Account__c', 'FY22Q2_Top_Account__c',\n", "       'FY22Q3_Top_Account__c', 'FY22Q4_Top_Account__c',\n", "       'FY23Q1_Top_Account__c', 'FY23Q2_Top_Account__c',\n", "       'FY23Q3_Top_Account__c', 'FY23Q4_Top_Account__c',\n", "       'FY24Q1_Top_Account__c', 'FY24Q3_Top_Account__c', 'FY24Q2_AE__c',\n", "       'FY24Q2_AEM__c', 'FY24Q3_AE__c', 'FY24Q3_AEM__c', 'Mac_as_Choice__c',\n", "       'Mac_as_Choice_start_time__c', 'SEI_maC__c', 'Name',\n", "       'SEI_Account_ID__c', 'SEI_Cluster_ID__c', 'Account_Cluster__c',\n", "       'Is_Group__c', 'Account_ID__c', 'Account_Group__c', 'SEI_Group_ID__c',\n", "       'Account_Group_ID__c', 'Group_Sub_Segment__c',\n", "       'Group_Vertical_Industry__c', 'Province__c', 'City__c',\n", "       'Group_Province__c', 'Group_City__c', 'Owner.Name', 'Sub_Segment__c',\n", "       'Vertical_Industry__c', 'Top_Account_Deep_Dive__c']]"]}, {"cell_type": "code", "execution_count": 120, "id": "5f660387-6c4c-4e73-b55a-6ae3671f8602", "metadata": {}, "outputs": [], "source": ["df2 = pd.read_csv('report1716360761066.csv')"]}, {"cell_type": "code", "execution_count": 117, "id": "5a7afe19-45fc-4965-aff6-f75c32cf61f2", "metadata": {}, "outputs": [], "source": ["df.columns = df2.columns"]}, {"cell_type": "code", "execution_count": 119, "id": "0c1fe850-a4ff-4c95-ab79-ca55fe53a938", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data has been saved to salesforce_data.csv\n"]}], "source": ["\n", "\n", "# Save to CSV\n", "df.to_csv('salesforce_data.csv', index=False)\n", "\n", "print(\"Data has been saved to salesforce_data.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "ac5f8803-f780-4a0b-8150-267ee017fef0", "metadata": {}, "outputs": [], "source": ["# df2 = pd.read_csv('report1716360761066.csv')\n", "\n", "\n", "# df11 = df[['Name','Owner.Name']].drop_duplicates()\n", "# df11.columns=['Account Name','Account Owner']\n", "\n", "# df11\n", "\n", "# df21 = df2[['Account Name','Account Owner']].drop_duplicates()\n", "\n", "# df21 = df21[~df21['Account Name'].isna()]\n", "# df21['Flag'] = 'Yes'\n", "\n", "# df12 = df11.merge(df21, on=['Account Name','Account Owner'], how='left')\n", "# # df12['flag']=np.where(df12['Account Owner_x']!=df12['Account Owner_y'], <PERSON><PERSON><PERSON>, True)\n", "\n", "\n", "# df12[df12['Flag']=='Yes']\n", "\n", "# df12[df12['Flag'].isna()]"]}, {"cell_type": "code", "execution_count": null, "id": "1c471ad4-c9d5-4e69-b7de-8df851f7d838", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}