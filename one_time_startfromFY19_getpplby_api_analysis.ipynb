{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5e037e7a-9d05-42a9-a862-7046cce37f59", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import datetime\n", "import os\n", "import json\n", "from string import Template\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "id": "e374c60b-90c0-4723-8b65-162d14c914b3", "metadata": {}, "outputs": [], "source": ["from acquire_mapping import Mapping\n", "\n", "from rolling import addrollingday, addrolling\n", "from DB import DBPG"]}, {"cell_type": "code", "execution_count": 3, "id": "064792e5-0a9c-4303-9d04-cee1631a5704", "metadata": {}, "outputs": [], "source": ["currentpath = os.getcwd()\n", "homepath = os.environ['HOME']"]}, {"cell_type": "code", "execution_count": 4, "id": "440330ef-9417-4d75-9b71-f3c547762fd1", "metadata": {}, "outputs": [], "source": ["fiscaldate_mapping = Mapping.fiscaldate\n", "fiscaldate_mapping2 = fiscaldate_mapping.copy()[['Date','week_in_fiscal_quarter','fiscal_qtr_year_name']]\n", "fiscaldate_mapping2['week_in_fiscal_quarter'] = fiscaldate_mapping2['week_in_fiscal_quarter'].str.slice(1)\n", "fiscaldate_mapping2.rename(columns={'Date':'fiscal_dt'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 61, "id": "a97265f9-a13a-4afd-9f7f-a98007ea4e38", "metadata": {}, "outputs": [], "source": ["top_account_ticked = Mapping.top_account_ticked\n", "top_account_ticked = top_account_ticked[['Account Name','ST FYQuarter','Top Tag']].drop_duplicates()\n", "top_account_ticked.rename(columns={'ST FYQuarter':'fiscal_qtr_year_name'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 67, "id": "aa5adc0e-f4f7-4faa-8cc9-d0cfc58d7266", "metadata": {}, "outputs": [], "source": ["top_account_ticked2 = top_account_ticked[top_account_ticked['fiscal_qtr_year_name']=='FY24Q4']\n", "top_account_ticked2.drop('fiscal_qtr_year_name', axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": 8, "id": "b1ad6265-b4b7-49fb-bee5-07054030cd2e", "metadata": {}, "outputs": [], "source": ["query_sql = \"\"\"\n", "SELECT mpn_id \"MPN_ID\", project_short_desc \"PROJECT_short_DESC\", hier_node_level_1_name \"FPH1\", hier_node_level_2_name \"FPH2\", hier_node_level_3_name \"FPH3\", hier_node_level_4_name \"FPH4\", color_short_desc,\n", "       storsize_long_desc \"storesize_long_desc\", CASE WHEN type_short_desc = 'CTO' THEN 'Y' ELSE 'N' END AS \"CTO_IND\", wireless_long_desc \"WIRELESS_LONG_DESC\"\n", "FROM $arg1\n", "\n", "      \n", "      \"\"\"\n", "#sql语句\n", "query_sql = Template(query_sql)\n", "mpn_mappng3 = pd.read_sql_query(query_sql.substitute(arg1='\"sandbox\".\"app_ent_mpn_mapping_info_da\"'),DBPG.engine)\n", "mpn_mappng3 = mpn_mappng3.drop_duplicates()\n", "query_sql = \"\"\"\n", "SELECT mpn_id \"MPN_ID\", project_short_desc \"PROJECT_short_DESC\", hier_node_level_1_name \"FPH1\", hier_node_level_2_name \"FPH2\", hier_node_level_3_name \"FPH3\", hier_node_level_4_name \"FPH4\", color_short_desc,\n", "       storsize_long_desc \"storesize_long_desc\", cto_flag \"CTO_IND\", wireless_long_desc \"WIRELESS_LONG_DESC\"\n", "FROM $arg1\n", "\n", "      \n", "      \"\"\"\n", "#sql语句\n", "query_sql = Template(query_sql)\n", "mpn_mappng4 = pd.read_sql_query(query_sql.substitute(arg1='\"sandbox\".\"mpn_mapping_s\"'),DBPG.engine)\n", "mpn_mappng4 = mpn_mappng4.drop_duplicates()\n", "mpn_mappng31 = pd.concat([mpn_mappng3, mpn_mappng4], axis=0).drop_duplicates(subset=['MPN_ID'])\n", "mpn_mappng1 = mpn_mappng31.copy()"]}, {"cell_type": "code", "execution_count": 78, "id": "75ab4d48-c324-4d51-bf30-2f68a767b492", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>数据来源</th>\n", "      <th>数据来源-SFDC</th>\n", "      <th>数据来源-QCC</th>\n", "      <th>数据来源-JD</th>\n", "      <th>公司名称</th>\n", "      <th>法定代表人姓名</th>\n", "      <th>注册资本</th>\n", "      <th>注册资本币种</th>\n", "      <th>注册资本数值</th>\n", "      <th>实缴资本</th>\n", "      <th>...</th>\n", "      <th>FY21 iPhone</th>\n", "      <th>FY22 CPU</th>\n", "      <th>FY22 iPad</th>\n", "      <th>FY22 iPhone</th>\n", "      <th>FY23 CPU</th>\n", "      <th>FY23 iPad</th>\n", "      <th>FY23 iPhone</th>\n", "      <th>FY24 CPU</th>\n", "      <th>FY24 iPad</th>\n", "      <th>FY24 iPhone</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>QCC</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>西平芦庙浩旺医院（普通合伙）</td>\n", "      <td>赵富圈、张一杰、王若冰、王浩楠</td>\n", "      <td>90万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>90.0</td>\n", "      <td>90万元人民币</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>QCC</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>常州金坛万邦工程仪器厂</td>\n", "      <td>秦利明</td>\n", "      <td>8万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>8.0</td>\n", "      <td>8万元人民币</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>QCC</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>鄱阳县莲花山林场</td>\n", "      <td>苏章德</td>\n", "      <td>86万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>86.0</td>\n", "      <td>86万元人民币</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>QCC</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>茂名市海邦涂料化工有限公司</td>\n", "      <td>林天俊</td>\n", "      <td>83万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>83.0</td>\n", "      <td>83万元人民币</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>QCC</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>甘肃丝路驼商商贸有限公司</td>\n", "      <td>高亮</td>\n", "      <td>80万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>80.0</td>\n", "      <td>80万元人民币</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170695</th>\n", "      <td>SFDC</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>苏州文商壹号商贸有限公司</td>\n", "      <td>高松</td>\n", "      <td>100万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>100.0</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170696</th>\n", "      <td>SFDC</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>浙江枫叶建设有限公司</td>\n", "      <td>张文龙</td>\n", "      <td>10800万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>10800.0</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170697</th>\n", "      <td>SFDC</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>山东工程职业技术大学</td>\n", "      <td>吴梦军</td>\n", "      <td>5000万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>5000.0</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170698</th>\n", "      <td>SFDC</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>苏州星轮旅游产业有限公司金鸡湖英迪格分公司</td>\n", "      <td>栾兰</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170699</th>\n", "      <td>SFDC</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>山东明德私域管理服务有限公司</td>\n", "      <td>隋国栋</td>\n", "      <td>300万元人民币</td>\n", "      <td>万元人民币</td>\n", "      <td>300.0</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>170700 rows × 80 columns</p>\n", "</div>"], "text/plain": ["        数据来源  数据来源-SFDC  数据来源-QCC  数据来源-JD                   公司名称  \\\n", "0        QCC          0         1        0         西平芦庙浩旺医院（普通合伙）   \n", "1        QCC          0         1        0            常州金坛万邦工程仪器厂   \n", "2        QCC          0         1        0               鄱阳县莲花山林场   \n", "3        QCC          0         1        0          茂名市海邦涂料化工有限公司   \n", "4        QCC          0         1        0           甘肃丝路驼商商贸有限公司   \n", "...      ...        ...       ...      ...                    ...   \n", "170695  SFDC          1         0        0           苏州文商壹号商贸有限公司   \n", "170696  SFDC          1         0        0             浙江枫叶建设有限公司   \n", "170697  SFDC          1         0        0             山东工程职业技术大学   \n", "170698  SFDC          1         0        0  苏州星轮旅游产业有限公司金鸡湖英迪格分公司   \n", "170699  SFDC          1         0        0         山东明德私域管理服务有限公司   \n", "\n", "                法定代表人姓名        注册资本 注册资本币种   注册资本数值     实缴资本  ... FY21 iPhone  \\\n", "0       赵富圈、张一杰、王若冰、王浩楠     90万元人民币  万元人民币     90.0  90万元人民币  ...         NaN   \n", "1                   秦利明      8万元人民币  万元人民币      8.0   8万元人民币  ...         NaN   \n", "2                   苏章德     86万元人民币  万元人民币     86.0  86万元人民币  ...         NaN   \n", "3                   林天俊     83万元人民币  万元人民币     83.0  83万元人民币  ...         NaN   \n", "4                    高亮     80万元人民币  万元人民币     80.0  80万元人民币  ...         NaN   \n", "...                 ...         ...    ...      ...      ...  ...         ...   \n", "170695               高松    100万元人民币  万元人民币    100.0      NaN  ...         NaN   \n", "170696              张文龙  10800万元人民币  万元人民币  10800.0      NaN  ...         NaN   \n", "170697              吴梦军   5000万元人民币  万元人民币   5000.0      NaN  ...         NaN   \n", "170698               栾兰         NaN    NaN      NaN      NaN  ...         NaN   \n", "170699              隋国栋    300万元人民币  万元人民币    300.0      NaN  ...         NaN   \n", "\n", "        FY22 CPU FY22 iPad FY22 iPhone FY23 CPU FY23 iPad FY23 iPhone  \\\n", "0            NaN       NaN         NaN      NaN       NaN         NaN   \n", "1            NaN       NaN         NaN      NaN       NaN         NaN   \n", "2            NaN       NaN         NaN      NaN       NaN         NaN   \n", "3            NaN       NaN         NaN      NaN       NaN         NaN   \n", "4            NaN       NaN         NaN      NaN       NaN         NaN   \n", "...          ...       ...         ...      ...       ...         ...   \n", "170695       NaN       NaN         NaN      NaN       NaN         NaN   \n", "170696       NaN       NaN         NaN      NaN       NaN         NaN   \n", "170697       NaN       NaN         NaN      NaN       NaN         NaN   \n", "170698       NaN       NaN         NaN      NaN       NaN         NaN   \n", "170699       NaN       NaN         NaN      NaN       NaN         NaN   \n", "\n", "       FY24 CPU FY24 iPad FY24 iPhone  \n", "0           NaN       NaN         NaN  \n", "1           NaN       NaN         NaN  \n", "2           NaN       NaN         NaN  \n", "3           NaN       NaN         NaN  \n", "4           NaN       NaN         NaN  \n", "...         ...       ...         ...  \n", "170695      1.0       NaN         NaN  \n", "170696      1.0       NaN         NaN  \n", "170697      1.0       NaN         NaN  \n", "170698      1.0       NaN         NaN  \n", "170699      NaN       1.0         NaN  \n", "\n", "[170700 rows x 80 columns]"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["qccv24 = pd.read_excel(f'{homepath}/Library/CloudStorage/Box-Box/0-Co-work for Label/数据库-GR/QCC+SFD+JD全部用户名单 V24.xlsx', sheet_name=1)\n"]}, {"cell_type": "code", "execution_count": 100, "id": "68121e8f-97c9-4602-94e3-5f18f2618849", "metadata": {}, "outputs": [], "source": ["industry_mapping = pd.read_excel('Mac 销售目标_v3.xlsx', sheet_name=3)\n", "industry_mapping = industry_mapping[['Industry Category 行业门类', 'QCC 行业整合', 'SF Industry']]"]}, {"cell_type": "code", "execution_count": null, "id": "b29e4adb-4833-4ff0-bed6-811bae3f8324", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 80, "id": "0fdf27ec-06c3-41ff-ada7-5a3a79b76035", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['数据来源', '数据来源-SFDC', '数据来源-QCC', '数据来源-JD', '公司名称', '法定代表人姓名', '注册资本', '注册资本币种', '注册资本数值', '实缴资本', '参保人数范围', '参保人数', '经营状态', '成立日期', '吊销日期', '统一社会信用代码', '注册号', '公司类型', '公司类型编码集', '核准日期', '登记机关', '省份代码', '省份', '城市', '区县', '营业期限开始', '营业期限结束', '企业地址', '经营范围', '行业门类', '行业大类', '行业中类', '行业小类', '纬度', '经度', '实缴资本范围', '企业规模', '企业规模-Large', '企业规模-Medium', '企业规模-Small', '企业规模-Runrate', '关键字-软件开发', '关键字-创意设计', '关键字-产品展示', '关键字-广告', '关键字-多媒体', '关键字-游戏', '关键字-新能源', '关键字-供应链', '关键字-机器学习', '关键字-人工智能', '关键字-元宇宙', '关键字-智能', '关键字-咨询', '关键字-传媒', '关键字-教育', 'FY17 CPU', 'FY17 iPad', 'FY17 iPhone', 'FY18 CPU', 'FY18 iPad', 'FY18 iPhone', 'FY19 CPU', 'FY19 iPad', 'FY19 iPhone', 'FY20 CPU', 'FY20 iPad', 'FY20 iPhone', 'FY21 CPU', 'FY21 iPad', 'FY21 iPhone', 'FY22 CPU', 'FY22 iPad', 'FY22 iPhone', 'FY23 CPU', 'FY23 iPad', 'FY23 iPhone', 'FY24 CPU', 'FY24 iPad', 'FY24 iPhone']\n"]}], "source": ["print(list(qccv24))"]}, {"cell_type": "code", "execution_count": 112, "id": "fe8e463b-7b79-434c-adb7-fca95e465bfc", "metadata": {}, "outputs": [], "source": ["qccv24_2 = qccv24[['公司名称','行业门类','参保人数范围', '参保人数']]\n", "# qccv24_2 = qccv24_2[qccv24_2['参保人数范围'].isin(['>=2000人', '1000-1999人'])]\n", "qccv24_2.rename(columns={'公司名称':'Account Name','行业门类':'Industry Category 行业门类'}, inplace=True)\n", "qccv24_2['qccv24_tag'] = True"]}, {"cell_type": "code", "execution_count": null, "id": "a1c2de31-edb7-442e-8449-9ef26a429348", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "4afcb738-6cfb-4975-8f8e-a792c6fd7af2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ProductLineRevenue</th>\n", "      <th>FY</th>\n", "      <th>ST FYQuarter</th>\n", "      <th>Product Family</th>\n", "      <th>Product ST Quarter</th>\n", "      <th>Product ST Week</th>\n", "      <th>Oppty Line Item ID</th>\n", "      <th>Marketing Part Number (MPN)</th>\n", "      <th>Quantity</th>\n", "      <th>Line of Business</th>\n", "      <th>...</th>\n", "      <th>Mac as Choice</th>\n", "      <th>Top Account Deep Dive</th>\n", "      <th>NCR Enroll Date</th>\n", "      <th>NCR Group</th>\n", "      <th>NCR Program</th>\n", "      <th>NCR Reseller</th>\n", "      <th>NCR Status</th>\n", "      <th>Account Owner</th>\n", "      <th>Disti/T1 Reseller</th>\n", "      <th>T2 Reseller</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 99 columns</p>\n", "</div>"], "text/plain": ["        ProductLineRevenue    FY ST FYQuarter Product Family  \\\n", "0                  3191.75  FY19       FY19Q2    MacBook Pro   \n", "1                  1082.00  FY19       FY19Q3    MacBook Air   \n", "2                 16296.00  FY19       FY19Q2    MacBook Pro   \n", "3                  2059.52  FY19       FY19Q2    MacBook Pro   \n", "4                  2712.73  FY19       FY19Q2    MacBook Pro   \n", "...                    ...   ...          ...            ...   \n", "264218             1291.00  FY24       FY24Q3    MacBook Air   \n", "264219             1291.00  FY24       FY24Q3    MacBook Air   \n", "264220             9676.00  FY24       FY24Q4    MacBook Pro   \n", "264221             3661.00  FY24       FY24Q4     Vision Pro   \n", "264222             2403.00  FY24       FY24Q4           iMac   \n", "\n", "       Product ST Quarter Product ST Week Oppty Line Item ID  \\\n", "0                      Q2             W08    00k6F0000168pJf   \n", "1                      Q3             W03    00k6F0000169QKA   \n", "2                      Q2             W12    00k6F0000169aqO   \n", "3                      Q2             W13    00k6F0000169l19   \n", "4                      Q2             W05    00k6F000015xT5H   \n", "...                   ...             ...                ...   \n", "264218                 Q3             W10    00kIS00000757ZB   \n", "264219                 Q3             W09    00kIS0000074ual   \n", "264220                 Q4             W07    00kIS000007lKUp   \n", "264221                 Q4             W02    00kIS0000077qYX   \n", "264222                 Q4             W12    00kIS00000757Un   \n", "\n", "       Marketing Part Number (MPN)  Quantity Line of Business  ...  \\\n", "0                             Z0V1       1.0              CPU  ...   \n", "1                        MREA2CH/A       1.0              CPU  ...   \n", "2                             Z0UK      12.0              CPU  ...   \n", "3                             Z0V7       1.0              CPU  ...   \n", "4                             Z0V0       1.0              CPU  ...   \n", "...                            ...       ...              ...  ...   \n", "264218                        Z1BB       1.0              CPU  ...   \n", "264219                        Z1BP       1.0              CPU  ...   \n", "264220                        Z1AY       4.0              CPU  ...   \n", "264221                   MW8X3CH/A       1.0           Vision  ...   \n", "264222                        Z19Q       1.0              CPU  ...   \n", "\n", "       Mac as Choice  Top Account Deep Dive  NCR Enroll Date  NCR Group  \\\n", "0                  0                      0              NaN        NaN   \n", "1                  0                      0              NaN        NaN   \n", "2                  0                      0              NaN        NaN   \n", "3                  0                      0              NaN        NaN   \n", "4                  0                      0              NaN        NaN   \n", "...              ...                    ...              ...        ...   \n", "264218             0                      0              NaN        NaN   \n", "264219             0                      0              NaN        NaN   \n", "264220             0                      0              NaN        NaN   \n", "264221             0                      0              NaN        NaN   \n", "264222             0                      0              NaN        NaN   \n", "\n", "       NCR Program NCR Reseller NCR Status  Account Owner Disti/T1 Reseller  \\\n", "0                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "1                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "2                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "3                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "4                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "...            ...          ...        ...            ...               ...   \n", "264218           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "264219           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "264220           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "264221           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "264222           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "\n", "         T2 Reseller  \n", "0                NaN  \n", "1                NaN  \n", "2                NaN  \n", "3                NaN  \n", "4                NaN  \n", "...              ...  \n", "264218  上海麦连网络科技有限公司  \n", "264219  上海麦连网络科技有限公司  \n", "264220  上海麦连网络科技有限公司  \n", "264221    山东亿达数码有限公司  \n", "264222  上海麦连网络科技有限公司  \n", "\n", "[264223 rows x 99 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pplall = pd.read_csv('one_time_startfromFY19_getpplby_api_data.csv')\n", "pplall"]}, {"cell_type": "code", "execution_count": 11, "id": "9398a138-7f70-4c23-8472-3f9986fdc697", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ProductLineRevenue', 'FY', 'ST FYQuarter', 'Product Family', 'Product ST Quarter', 'Product ST Week', 'Oppty Line Item ID', 'Marketing Part Number (MPN)', 'Quantity', 'Line of Business', 'Deal type', 'Sold To ID', 'Reseller Apple ID', 'Apple ID', 'Reseller Track', 'ESC Store', 'Opportunity Type', 'Leasing or Not', 'Penetrated Account', 'Opportunity ID', 'Opportunity Name', 'Probability (%)', 'Account Group', 'Group Province', 'Group City', 'Province/Region', 'City', 'Sub Segment', 'Vertical Industry', 'Mac as Choice加入时间', 'Mac as Choice start time', 'FY21Q3 Top Account', 'FY21Q4 Top Account', 'FY22Q1 Top Account', 'FY25Q1 Top Account', 'FY24Q4 Top Account', 'FY24Q3 Top Account', 'FY24Q2 Top Account', 'FY24Q1 Top Account', 'FY23Q4 Top Account', 'FY23Q3 Top Account', 'FY23Q2 Top Account', 'FY23Q1 Top Account', 'FY22Q4 Top Account', 'FY22Q3 Top Account', 'FY22Q2 Top Account', 'FY25Q1 AE', 'FY24Q4 AE', 'FY24Q3 AE', 'FY24Q2 AE', 'FY24Q1 AE', 'FY23Q4 AE', 'FY23Q3 AE', 'FY23Q2 AE', 'FY23Q1 AE', 'FY22Q4 AE', 'FY22Q3 AE', 'FY22Q2 AE', 'FY25Q1 AEM', 'FY24Q4 AEM', 'FY24Q3 AEM', 'FY24Q2 AEM', 'FY24Q1 AEM', 'FY23Q4 AEM', 'FY23Q3 AEM', 'FY23Q2 AEM', 'FY23Q1 AEM', 'FY22Q4 AEM', 'FY22Q3 AEM', 'FY22Q2 AEM', 'FY21Q2 AE', 'FY21Q2 AEM', 'FY21Q3 AE', 'FY21Q3 AEM', 'FY21Q4 AE', 'FY21Q4 AEM', 'FY22Q1 AE', 'FY22Q1 AEM', 'Segment', 'Top Account', 'Total Mac Demand', 'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Industry Target Account', 'Source Detail', 'Sales Region', 'Account Name', 'Account ID', 'Mac as Choice', 'Top Account Deep Dive', 'NCR Enroll Date', 'NCR Group', 'NCR Program', 'NCR Reseller', 'NCR Status', 'Account Owner', 'Disti/T1 Reseller', 'T2 Reseller']\n"]}], "source": ["pplall = pplall[~pplall['ST FYQuarter'].isna()]\n", "pplall\n", "\n", "columns_list = pplall.columns.tolist()\n", "\n", "# 更新列表中的列名\n", "updated_list = [col.replace(\"Large Account\", \"Top Account\") if \"Large Account\" in col else col for col in columns_list]\n", "\n", "print(updated_list)\n", "\n", "pplall.columns = updated_list"]}, {"cell_type": "code", "execution_count": 12, "id": "4cb3d887-0a45-45aa-a530-23bc6a328de0", "metadata": {"tags": []}, "outputs": [], "source": ["pplall.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": 13, "id": "37a55500-f78d-4d6a-98cb-7310eeaf1bae", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ProductLineRevenue</th>\n", "      <th>FY</th>\n", "      <th>ST FYQuarter</th>\n", "      <th>Product Family</th>\n", "      <th>Product ST Quarter</th>\n", "      <th>Product ST Week</th>\n", "      <th>Oppty Line Item ID</th>\n", "      <th>Marketing Part Number (MPN)</th>\n", "      <th>Quantity</th>\n", "      <th>Line of Business</th>\n", "      <th>...</th>\n", "      <th>Mac as Choice</th>\n", "      <th>Top Account Deep Dive</th>\n", "      <th>NCR Enroll Date</th>\n", "      <th>NCR Group</th>\n", "      <th>NCR Program</th>\n", "      <th>NCR Reseller</th>\n", "      <th>NCR Status</th>\n", "      <th>Account Owner</th>\n", "      <th>Disti/T1 Reseller</th>\n", "      <th>T2 Reseller</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 99 columns</p>\n", "</div>"], "text/plain": ["        ProductLineRevenue    FY ST FYQuarter Product Family  \\\n", "0                  3191.75  FY19       FY19Q2    MacBook Pro   \n", "1                  1082.00  FY19       FY19Q3    MacBook Air   \n", "2                 16296.00  FY19       FY19Q2    MacBook Pro   \n", "3                  2059.52  FY19       FY19Q2    MacBook Pro   \n", "4                  2712.73  FY19       FY19Q2    MacBook Pro   \n", "...                    ...   ...          ...            ...   \n", "264218             1291.00  FY24       FY24Q3    MacBook Air   \n", "264219             1291.00  FY24       FY24Q3    MacBook Air   \n", "264220             9676.00  FY24       FY24Q4    MacBook Pro   \n", "264221             3661.00  FY24       FY24Q4     Vision Pro   \n", "264222             2403.00  FY24       FY24Q4           iMac   \n", "\n", "       Product ST Quarter Product ST Week Oppty Line Item ID  \\\n", "0                      Q2             W08    00k6F0000168pJf   \n", "1                      Q3             W03    00k6F0000169QKA   \n", "2                      Q2             W12    00k6F0000169aqO   \n", "3                      Q2             W13    00k6F0000169l19   \n", "4                      Q2             W05    00k6F000015xT5H   \n", "...                   ...             ...                ...   \n", "264218                 Q3             W10    00kIS00000757ZB   \n", "264219                 Q3             W09    00kIS0000074ual   \n", "264220                 Q4             W07    00kIS000007lKUp   \n", "264221                 Q4             W02    00kIS0000077qYX   \n", "264222                 Q4             W12    00kIS00000757Un   \n", "\n", "       Marketing Part Number (MPN)  Quantity Line of Business  ...  \\\n", "0                             Z0V1       1.0              CPU  ...   \n", "1                        MREA2CH/A       1.0              CPU  ...   \n", "2                             Z0UK      12.0              CPU  ...   \n", "3                             Z0V7       1.0              CPU  ...   \n", "4                             Z0V0       1.0              CPU  ...   \n", "...                            ...       ...              ...  ...   \n", "264218                        Z1BB       1.0              CPU  ...   \n", "264219                        Z1BP       1.0              CPU  ...   \n", "264220                        Z1AY       4.0              CPU  ...   \n", "264221                   MW8X3CH/A       1.0           Vision  ...   \n", "264222                        Z19Q       1.0              CPU  ...   \n", "\n", "       Mac as Choice  Top Account Deep Dive  NCR Enroll Date  NCR Group  \\\n", "0                  0                      0              NaN        NaN   \n", "1                  0                      0              NaN        NaN   \n", "2                  0                      0              NaN        NaN   \n", "3                  0                      0              NaN        NaN   \n", "4                  0                      0              NaN        NaN   \n", "...              ...                    ...              ...        ...   \n", "264218             0                      0              NaN        NaN   \n", "264219             0                      0              NaN        NaN   \n", "264220             0                      0              NaN        NaN   \n", "264221             0                      0              NaN        NaN   \n", "264222             0                      0              NaN        NaN   \n", "\n", "       NCR Program NCR Reseller NCR Status  Account Owner Disti/T1 Reseller  \\\n", "0                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "1                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "2                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "3                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "4                0          NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司   \n", "...            ...          ...        ...            ...               ...   \n", "264218           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "264219           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "264220           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "264221           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "264222           0          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司   \n", "\n", "         T2 Reseller  \n", "0                NaN  \n", "1                NaN  \n", "2                NaN  \n", "3                NaN  \n", "4                NaN  \n", "...              ...  \n", "264218  上海麦连网络科技有限公司  \n", "264219  上海麦连网络科技有限公司  \n", "264220  上海麦连网络科技有限公司  \n", "264221    山东亿达数码有限公司  \n", "264222  上海麦连网络科技有限公司  \n", "\n", "[264223 rows x 99 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["pplall"]}, {"cell_type": "code", "execution_count": 14, "id": "6c301928-092c-4011-81a6-8f083b632852", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["array(['FY19Q2', 'FY19Q3', 'FY19Q1', 'FY21Q1', 'FY21Q3', 'FY23Q1',\n", "       'FY23Q2', 'FY23Q3', 'FY23Q4', 'FY24Q1', 'FY24Q4', 'FY24Q3',\n", "       'FY24Q2', 'FY25Q1', 'FY22Q1', 'FY21Q4', 'FY21Q2', 'FY22Q4',\n", "       'FY20Q1', 'FY20Q2', 'FY22Q3', 'FY22Q2', 'FY18Q4', 'FY20Q3',\n", "       'FY19Q4', 'FY20Q4', 'FY18Q3', 'FY25Q3', 'FY18Q2', 'FY18Q1',\n", "       'FY25Q2', 'FY25Q4', 'FY17Q4', 'FY17Q2', 'FY17Q3', 'FY17Q1'],\n", "      dtype=object)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["pplall['ST FYQuarter'].unique()"]}, {"cell_type": "code", "execution_count": 15, "id": "d6aeef14-5d4f-4291-86e1-866de711c35e", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ProductLineRevenue</th>\n", "      <th>FY</th>\n", "      <th>ST FYQuarter</th>\n", "      <th>Product Family</th>\n", "      <th>Product ST Quarter</th>\n", "      <th>Product ST Week</th>\n", "      <th>Oppty Line Item ID</th>\n", "      <th>Marketing Part Number (MPN)</th>\n", "      <th>Quantity</th>\n", "      <th>Line of Business</th>\n", "      <th>...</th>\n", "      <th>Top Account Deep Dive</th>\n", "      <th>NCR Enroll Date</th>\n", "      <th>NCR Group</th>\n", "      <th>NCR Program</th>\n", "      <th>NCR Reseller</th>\n", "      <th>NCR Status</th>\n", "      <th>Account Owner</th>\n", "      <th>Disti/T1 Reseller</th>\n", "      <th>T2 Reseller</th>\n", "      <th>Top Account1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 100 columns</p>\n", "</div>"], "text/plain": ["        ProductLineRevenue    FY ST FYQuarter Product Family  \\\n", "0                  3191.75  FY19       FY19Q2    MacBook Pro   \n", "1                  1082.00  FY19       FY19Q3    MacBook Air   \n", "2                 16296.00  FY19       FY19Q2    MacBook Pro   \n", "3                  2059.52  FY19       FY19Q2    MacBook Pro   \n", "4                  2712.73  FY19       FY19Q2    MacBook Pro   \n", "...                    ...   ...          ...            ...   \n", "264218             1291.00  FY24       FY24Q3    MacBook Air   \n", "264219             1291.00  FY24       FY24Q3    MacBook Air   \n", "264220             9676.00  FY24       FY24Q4    MacBook Pro   \n", "264221             3661.00  FY24       FY24Q4     Vision Pro   \n", "264222             2403.00  FY24       FY24Q4           iMac   \n", "\n", "       Product ST Quarter Product ST Week Oppty Line Item ID  \\\n", "0                      Q2             W08    00k6F0000168pJf   \n", "1                      Q3             W03    00k6F0000169QKA   \n", "2                      Q2             W12    00k6F0000169aqO   \n", "3                      Q2             W13    00k6F0000169l19   \n", "4                      Q2             W05    00k6F000015xT5H   \n", "...                   ...             ...                ...   \n", "264218                 Q3             W10    00kIS00000757ZB   \n", "264219                 Q3             W09    00kIS0000074ual   \n", "264220                 Q4             W07    00kIS000007lKUp   \n", "264221                 Q4             W02    00kIS0000077qYX   \n", "264222                 Q4             W12    00kIS00000757Un   \n", "\n", "       Marketing Part Number (MPN)  Quantity Line of Business  ...  \\\n", "0                             Z0V1       1.0              CPU  ...   \n", "1                        MREA2CH/A       1.0              CPU  ...   \n", "2                             Z0UK      12.0              CPU  ...   \n", "3                             Z0V7       1.0              CPU  ...   \n", "4                             Z0V0       1.0              CPU  ...   \n", "...                            ...       ...              ...  ...   \n", "264218                        Z1BB       1.0              CPU  ...   \n", "264219                        Z1BP       1.0              CPU  ...   \n", "264220                        Z1AY       4.0              CPU  ...   \n", "264221                   MW8X3CH/A       1.0           Vision  ...   \n", "264222                        Z19Q       1.0              CPU  ...   \n", "\n", "       Top Account Deep Dive  NCR Enroll Date  NCR Group  NCR Program  \\\n", "0                          0              NaN        NaN            0   \n", "1                          0              NaN        NaN            0   \n", "2                          0              NaN        NaN            0   \n", "3                          0              NaN        NaN            0   \n", "4                          0              NaN        NaN            0   \n", "...                      ...              ...        ...          ...   \n", "264218                     0              NaN        NaN            0   \n", "264219                     0              NaN        NaN            0   \n", "264220                     0              NaN        NaN            0   \n", "264221                     0              NaN        NaN            0   \n", "264222                     0              NaN        NaN            0   \n", "\n", "       NCR Reseller NCR Status  Account Owner Disti/T1 Reseller   T2 Reseller  \\\n", "0               NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司           NaN   \n", "1               NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司           NaN   \n", "2               NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司           NaN   \n", "3               NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司           NaN   \n", "4               NaN        NaN    Howells Hao    倍升互联（北京）科技有限公司           NaN   \n", "...             ...        ...            ...               ...           ...   \n", "264218          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司   \n", "264219          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司   \n", "264220          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司   \n", "264221          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司    山东亿达数码有限公司   \n", "264222          NaN        NaN  General Owner    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司   \n", "\n", "       Top Account1  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  \n", "...             ...  \n", "264218          0.0  \n", "264219          0.0  \n", "264220          0.0  \n", "264221          0.0  \n", "264222          0.0  \n", "\n", "[264223 rows x 100 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# 遍历每一行\n", "for index, row in pplall.iterrows():\n", "    # 更改topaccount 获取方法_231204\n", "    fy_quarter = row['ST FYQuarter']\n", "    if fy_quarter in ['FY21Q1', 'FY21Q2']:\n", "        fy_quarter = 'FY21Q3'\n", "    \n", "    # 构建列名\n", "    col_name = f\"{fy_quarter} Top Account\"\n", "    \n", "    # 检查这个列是否存在\n", "    if col_name in pplall.columns:\n", "        # 如果存在，则更新 'Top Account' 列\n", "        pplall.at[index, 'Top Account1'] = pplall.at[index, col_name]\n", "        # if row[col_name] == 1.0:\n", "        #     pplall.at[index, 'Top Account1'] = 1\n", "            # row['Top Account1'] = 1\n", "        \n", "        # 删除这个列\n", "        # pplall.drop(col_name, axis=1, inplace=True)\n", "\n", "# 显示更新后的 DataFrame\n", "pplall\n"]}, {"cell_type": "code", "execution_count": 16, "id": "ddac988f-f209-412c-8ef2-781a62fda0cb", "metadata": {"tags": []}, "outputs": [], "source": ["# # 验证\n", "# pplall[pplall['ST FYQuarter'].isin(['FY21Q1', 'FY21Q2'])]['Top Account1'].unique()"]}, {"cell_type": "code", "execution_count": 17, "id": "1a1cf7fa-11f6-4d89-ad0c-4dd1a84a41fb", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FY25Q1 AE\n"]}], "source": ["# 获取所有列名包含 'AE' 但不包含 'AEM' 的列\n", "ae_columns = [col for col in pplall.columns if 'AE' in col and 'AEM' not in col]\n", "\n", "# 对 ae_columns 进行排序\n", "sorted_ae_columns = sorted(ae_columns)\n", "\n", "# 获取排序后列表的最后一个值\n", "last_ae_column = sorted_ae_columns[-1] if sorted_ae_columns else None\n", "\n", "# 打印结果\n", "print(last_ae_column)"]}, {"cell_type": "code", "execution_count": 18, "id": "0a58f591-67e6-46a9-92eb-792119e8e89d", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FY25Q1 AEM\n"]}], "source": ["# 获取所有列名包含 'AE' 但不包含 'AEM' 的列\n", "aem_columns = [col for col in pplall.columns if 'AEM' in col]\n", "\n", "# 对 ae_columns 进行排序\n", "sorted_aem_columns = sorted(aem_columns)\n", "\n", "# 获取排序后列表的最后一个值\n", "last_aem_column = sorted_aem_columns[-1] if sorted_aem_columns else None\n", "\n", "# 打印结果\n", "print(last_aem_column)"]}, {"cell_type": "code", "execution_count": 19, "id": "aaaec767-6eef-421f-b962-7e7dbe491bbf", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ProductLineRevenue</th>\n", "      <th>FY</th>\n", "      <th>ST FYQuarter</th>\n", "      <th>Product Family</th>\n", "      <th>Product ST Quarter</th>\n", "      <th>Product ST Week</th>\n", "      <th>Oppty Line Item ID</th>\n", "      <th>Marketing Part Number (MPN)</th>\n", "      <th>Quantity</th>\n", "      <th>Line of Business</th>\n", "      <th>...</th>\n", "      <th>NCR Enroll Date</th>\n", "      <th>NCR Group</th>\n", "      <th>NCR Program</th>\n", "      <th>NCR Reseller</th>\n", "      <th>NCR Status</th>\n", "      <th>Account Owner</th>\n", "      <th>Disti/T1 Reseller</th>\n", "      <th>T2 Reseller</th>\n", "      <th>Top Account1</th>\n", "      <th>AE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 101 columns</p>\n", "</div>"], "text/plain": ["        ProductLineRevenue    FY ST FYQuarter Product Family  \\\n", "0                  3191.75  FY19       FY19Q2    MacBook Pro   \n", "1                  1082.00  FY19       FY19Q3    MacBook Air   \n", "2                 16296.00  FY19       FY19Q2    MacBook Pro   \n", "3                  2059.52  FY19       FY19Q2    MacBook Pro   \n", "4                  2712.73  FY19       FY19Q2    MacBook Pro   \n", "...                    ...   ...          ...            ...   \n", "264218             1291.00  FY24       FY24Q3    MacBook Air   \n", "264219             1291.00  FY24       FY24Q3    MacBook Air   \n", "264220             9676.00  FY24       FY24Q4    MacBook Pro   \n", "264221             3661.00  FY24       FY24Q4     Vision Pro   \n", "264222             2403.00  FY24       FY24Q4           iMac   \n", "\n", "       Product ST Quarter Product ST Week Oppty Line Item ID  \\\n", "0                      Q2             W08    00k6F0000168pJf   \n", "1                      Q3             W03    00k6F0000169QKA   \n", "2                      Q2             W12    00k6F0000169aqO   \n", "3                      Q2             W13    00k6F0000169l19   \n", "4                      Q2             W05    00k6F000015xT5H   \n", "...                   ...             ...                ...   \n", "264218                 Q3             W10    00kIS00000757ZB   \n", "264219                 Q3             W09    00kIS0000074ual   \n", "264220                 Q4             W07    00kIS000007lKUp   \n", "264221                 Q4             W02    00kIS0000077qYX   \n", "264222                 Q4             W12    00kIS00000757Un   \n", "\n", "       Marketing Part Number (MPN)  Quantity Line of Business  ...  \\\n", "0                             Z0V1       1.0              CPU  ...   \n", "1                        MREA2CH/A       1.0              CPU  ...   \n", "2                             Z0UK      12.0              CPU  ...   \n", "3                             Z0V7       1.0              CPU  ...   \n", "4                             Z0V0       1.0              CPU  ...   \n", "...                            ...       ...              ...  ...   \n", "264218                        Z1BB       1.0              CPU  ...   \n", "264219                        Z1BP       1.0              CPU  ...   \n", "264220                        Z1AY       4.0              CPU  ...   \n", "264221                   MW8X3CH/A       1.0           Vision  ...   \n", "264222                        Z19Q       1.0              CPU  ...   \n", "\n", "       NCR Enroll Date  NCR Group  NCR Program  NCR Reseller NCR Status  \\\n", "0                  NaN        NaN            0           NaN        NaN   \n", "1                  NaN        NaN            0           NaN        NaN   \n", "2                  NaN        NaN            0           NaN        NaN   \n", "3                  NaN        NaN            0           NaN        NaN   \n", "4                  NaN        NaN            0           NaN        NaN   \n", "...                ...        ...          ...           ...        ...   \n", "264218             NaN        NaN            0           NaN        NaN   \n", "264219             NaN        NaN            0           NaN        NaN   \n", "264220             NaN        NaN            0           NaN        NaN   \n", "264221             NaN        NaN            0           NaN        NaN   \n", "264222             NaN        NaN            0           NaN        NaN   \n", "\n", "        Account Owner Disti/T1 Reseller   T2 Reseller Top Account1   AE  \n", "0         Howells Hao    倍升互联（北京）科技有限公司           NaN          NaN  NaN  \n", "1         <PERSON><PERSON> Hao    倍升互联（北京）科技有限公司           NaN          NaN  NaN  \n", "2         <PERSON><PERSON> Hao    倍升互联（北京）科技有限公司           NaN          NaN  NaN  \n", "3         <PERSON><PERSON> Hao    倍升互联（北京）科技有限公司           NaN          NaN  NaN  \n", "4         <PERSON><PERSON> Hao    倍升互联（北京）科技有限公司           NaN          NaN  NaN  \n", "...               ...               ...           ...          ...  ...  \n", "264218  General Owner    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司          0.0  NaN  \n", "264219  General Owner    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司          0.0  NaN  \n", "264220  General Owner    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司          0.0  NaN  \n", "264221  General Owner    伟仕佳杰（重庆）科技有限公司    山东亿达数码有限公司          0.0  NaN  \n", "264222  General Owner    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司          0.0  NaN  \n", "\n", "[264223 rows x 101 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# 遍历每一行\n", "for index, row in pplall.iterrows():\n", "    # fy_quarter = row['ST FYQuarter']\n", "    \n", "    # 构建列名\n", "    col_name = f\"{last_ae_column}\"\n", "    \n", "    # 检查这个列是否存在\n", "    if col_name in pplall.columns:\n", "        # 如果存在，则更新 'AE' 列\n", "        pplall.at[index, 'AE'] = pplall.at[index, col_name]\n", "        \n", "        # 删除这个列\n", "        # pplall.drop(col_name, axis=1, inplace=True)\n", "\n", "# 显示更新后的 DataFrame\n", "pplall"]}, {"cell_type": "code", "execution_count": 20, "id": "3c9838e6-568d-4750-8d56-15ba429d42f3", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ProductLineRevenue</th>\n", "      <th>FY</th>\n", "      <th>ST FYQuarter</th>\n", "      <th>Product Family</th>\n", "      <th>Product ST Quarter</th>\n", "      <th>Product ST Week</th>\n", "      <th>Oppty Line Item ID</th>\n", "      <th>Marketing Part Number (MPN)</th>\n", "      <th>Quantity</th>\n", "      <th>Line of Business</th>\n", "      <th>...</th>\n", "      <th>NCR Group</th>\n", "      <th>NCR Program</th>\n", "      <th>NCR Reseller</th>\n", "      <th>NCR Status</th>\n", "      <th>Account Owner</th>\n", "      <th>Disti/T1 Reseller</th>\n", "      <th>T2 Reseller</th>\n", "      <th>Top Account1</th>\n", "      <th>AE</th>\n", "      <th>AEM</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3191.75</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W08</td>\n", "      <td>00k6F0000168pJf</td>\n", "      <td>Z0V1</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1082.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W03</td>\n", "      <td>00k6F0000169QKA</td>\n", "      <td>MREA2CH/A</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>16296.00</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W12</td>\n", "      <td>00k6F0000169aqO</td>\n", "      <td>Z0UK</td>\n", "      <td>12.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2059.52</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W13</td>\n", "      <td>00k6F0000169l19</td>\n", "      <td>Z0V7</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2712.73</td>\n", "      <td>FY19</td>\n", "      <td>FY19Q2</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q2</td>\n", "      <td>W05</td>\n", "      <td>00k6F000015xT5H</td>\n", "      <td>Z0V0</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>倍升互联（北京）科技有限公司</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264218</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W10</td>\n", "      <td>00kIS00000757ZB</td>\n", "      <td>Z1BB</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264219</th>\n", "      <td>1291.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q3</td>\n", "      <td>MacBook Air</td>\n", "      <td>Q3</td>\n", "      <td>W09</td>\n", "      <td>00kIS0000074ual</td>\n", "      <td>Z1BP</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264220</th>\n", "      <td>9676.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Q4</td>\n", "      <td>W07</td>\n", "      <td>00kIS000007lKUp</td>\n", "      <td>Z1AY</td>\n", "      <td>4.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264221</th>\n", "      <td>3661.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>Vision Pro</td>\n", "      <td>Q4</td>\n", "      <td>W02</td>\n", "      <td>00kIS0000077qYX</td>\n", "      <td>MW8X3CH/A</td>\n", "      <td>1.0</td>\n", "      <td>Vision</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>山东亿达数码有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264222</th>\n", "      <td>2403.00</td>\n", "      <td>FY24</td>\n", "      <td>FY24Q4</td>\n", "      <td>iMac</td>\n", "      <td>Q4</td>\n", "      <td>W12</td>\n", "      <td>00kIS00000757Un</td>\n", "      <td>Z19Q</td>\n", "      <td>1.0</td>\n", "      <td>CPU</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>General Owner</td>\n", "      <td>伟仕佳杰（重庆）科技有限公司</td>\n", "      <td>上海麦连网络科技有限公司</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264223 rows × 102 columns</p>\n", "</div>"], "text/plain": ["        ProductLineRevenue    FY ST FYQuarter Product Family  \\\n", "0                  3191.75  FY19       FY19Q2    MacBook Pro   \n", "1                  1082.00  FY19       FY19Q3    MacBook Air   \n", "2                 16296.00  FY19       FY19Q2    MacBook Pro   \n", "3                  2059.52  FY19       FY19Q2    MacBook Pro   \n", "4                  2712.73  FY19       FY19Q2    MacBook Pro   \n", "...                    ...   ...          ...            ...   \n", "264218             1291.00  FY24       FY24Q3    MacBook Air   \n", "264219             1291.00  FY24       FY24Q3    MacBook Air   \n", "264220             9676.00  FY24       FY24Q4    MacBook Pro   \n", "264221             3661.00  FY24       FY24Q4     Vision Pro   \n", "264222             2403.00  FY24       FY24Q4           iMac   \n", "\n", "       Product ST Quarter Product ST Week Oppty Line Item ID  \\\n", "0                      Q2             W08    00k6F0000168pJf   \n", "1                      Q3             W03    00k6F0000169QKA   \n", "2                      Q2             W12    00k6F0000169aqO   \n", "3                      Q2             W13    00k6F0000169l19   \n", "4                      Q2             W05    00k6F000015xT5H   \n", "...                   ...             ...                ...   \n", "264218                 Q3             W10    00kIS00000757ZB   \n", "264219                 Q3             W09    00kIS0000074ual   \n", "264220                 Q4             W07    00kIS000007lKUp   \n", "264221                 Q4             W02    00kIS0000077qYX   \n", "264222                 Q4             W12    00kIS00000757Un   \n", "\n", "       Marketing Part Number (MPN)  Quantity Line of Business  ... NCR Group  \\\n", "0                             Z0V1       1.0              CPU  ...       NaN   \n", "1                        MREA2CH/A       1.0              CPU  ...       NaN   \n", "2                             Z0UK      12.0              CPU  ...       NaN   \n", "3                             Z0V7       1.0              CPU  ...       NaN   \n", "4                             Z0V0       1.0              CPU  ...       NaN   \n", "...                            ...       ...              ...  ...       ...   \n", "264218                        Z1BB       1.0              CPU  ...       NaN   \n", "264219                        Z1BP       1.0              CPU  ...       NaN   \n", "264220                        Z1AY       4.0              CPU  ...       NaN   \n", "264221                   MW8X3CH/A       1.0           Vision  ...       NaN   \n", "264222                        Z19Q       1.0              CPU  ...       NaN   \n", "\n", "        NCR Program  NCR Reseller  NCR Status  Account Owner  \\\n", "0                 0           NaN         NaN    Howells Hao   \n", "1                 0           NaN         NaN    Howells Hao   \n", "2                 0           NaN         NaN    Howells Hao   \n", "3                 0           NaN         NaN    Howells Hao   \n", "4                 0           NaN         NaN    Howells Hao   \n", "...             ...           ...         ...            ...   \n", "264218            0           NaN         NaN  General Owner   \n", "264219            0           NaN         NaN  General Owner   \n", "264220            0           NaN         NaN  General Owner   \n", "264221            0           NaN         NaN  General Owner   \n", "264222            0           NaN         NaN  General Owner   \n", "\n", "       Disti/T1 Reseller   T2 Reseller Top Account1   AE  AEM  \n", "0         倍升互联（北京）科技有限公司           NaN          NaN  NaN  NaN  \n", "1         倍升互联（北京）科技有限公司           NaN          NaN  NaN  NaN  \n", "2         倍升互联（北京）科技有限公司           NaN          NaN  NaN  NaN  \n", "3         倍升互联（北京）科技有限公司           NaN          NaN  NaN  NaN  \n", "4         倍升互联（北京）科技有限公司           NaN          NaN  NaN  NaN  \n", "...                  ...           ...          ...  ...  ...  \n", "264218    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司          0.0  NaN  NaN  \n", "264219    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司          0.0  NaN  NaN  \n", "264220    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司          0.0  NaN  NaN  \n", "264221    伟仕佳杰（重庆）科技有限公司    山东亿达数码有限公司          0.0  NaN  NaN  \n", "264222    伟仕佳杰（重庆）科技有限公司  上海麦连网络科技有限公司          0.0  NaN  NaN  \n", "\n", "[264223 rows x 102 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# 遍历每一行\n", "for index, row in pplall.iterrows():\n", "    # fy_quarter = row['ST FYQuarter']\n", "    \n", "    # 构建列名\n", "    col_name = f\"{last_aem_column}\"\n", "    \n", "    # 检查这个列是否存在\n", "    if col_name in pplall.columns:\n", "        # 如果存在，则更新 'AE' 列\n", "        pplall.at[index, 'AEM'] = pplall.at[index, col_name]\n", "        \n", "        # 删除这个列\n", "        # pplall.drop(col_name, axis=1, inplace=True)\n", "\n", "# 显示更新后的 DataFrame\n", "pplall"]}, {"cell_type": "code", "execution_count": 21, "id": "0befec03-79e0-4c0e-9cc4-4c9b38df1813", "metadata": {"tags": []}, "outputs": [], "source": ["# 删除所有以 \"FY\" 开头且以 \"Top Account\" 结尾的列\n", "cols_to_drop = [col for col in pplall.columns if col.startswith('FY') and col.endswith('Top Account')]\n", "\n", "# 追加所有以 \"AE\" 或 \"AEM\" 结尾的列\n", "cols_to_drop += [col for col in pplall.columns if col.startswith('FY') and col.endswith('AE')]\n", "\n", "cols_to_drop += [col for col in pplall.columns if col.startswith('FY') and col.endswith('AEM')]\n", "\n", "\n", "# 删除这些列\n", "pplall.drop(columns=cols_to_drop, inplace=True)\n"]}, {"cell_type": "code", "execution_count": 22, "id": "e8e21c47-36c5-4e18-b8e9-20682a61d0ac", "metadata": {"tags": []}, "outputs": [], "source": ["# 遍历所有需要替换的星期编号\n", "for week in range(1, 10):\n", "    # 构建要替换的字符串（例如 'W03'）和替换后的字符串（例如 'W3'）\n", "    old_str = f'W0{week}'\n", "    new_str = f'W{week}'\n", "\n", "    # 替换字符串\n", "    pplall['Product ST Week'] = pplall['Product ST Week'].str.replace(old_str, new_str)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "e8c705ec-dabb-44da-94b6-4bf76c015a83", "metadata": {"tags": []}, "outputs": [], "source": ["pplall.rename(columns={'Product ST Week':'fiscal_week', 'Product ST Quarter':'fiscal_quarter', 'FY':'fiscal_year', 'Top Account':'Large Account'}, inplace=True)\n", "pplall.rename(columns={'Top Account1':'Top Account'}, inplace=True)\n"]}, {"cell_type": "code", "execution_count": 24, "id": "97ed3fbc-2609-488c-bcf3-d0ed675a9627", "metadata": {"tags": []}, "outputs": [], "source": ["pplall.rename(columns={'ST FYQuarter':'fiscal_qtr_year_name','Marketing Part Number (MPN)':'MPN_ID',\n", "                       'Sub Segment':'Group Sub Segment','Vertical Industry':'Group Vertical Industry'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 25, "id": "f0427923-6535-4b4a-a60b-3d93eca2c28a", "metadata": {"tags": []}, "outputs": [], "source": ["pplall['week_in_fiscal_quarter'] = pplall['fiscal_week'].str[1:]"]}, {"cell_type": "code", "execution_count": 27, "id": "41d94381-d9fb-439e-b825-95c34bbc8f83", "metadata": {"tags": []}, "outputs": [], "source": ["pplall = pd.merge(pplall, mpn_mappng31, on=['MPN_ID'], how='left')"]}, {"cell_type": "code", "execution_count": 30, "id": "944fc045-be7a-459c-b349-3c623c4c2e50", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ProductLineRevenue', 'fiscal_year', 'fiscal_qtr_year_name', 'Product Family', 'fiscal_quarter', 'fiscal_week', 'Oppty Line Item ID', 'MPN_ID', 'Quantity', 'Line of Business', 'Deal type', 'Sold To ID', 'Reseller Apple ID', 'Apple ID', 'Reseller Track', 'ESC Store', 'Opportunity Type', 'Leasing or Not', 'Penetrated Account', 'Opportunity ID', 'Opportunity Name', 'Probability (%)', 'Account Group', 'Group Province', 'Group City', 'Province/Region', 'City', 'Group Sub Segment', 'Group Vertical Industry', 'Mac as Choice加入时间', 'Mac as Choice start time', 'Segment', 'Large Account', 'Total Mac Demand', 'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Industry Target Account', 'Source Detail', 'Sales Region', 'Account Name', 'Account ID', 'Mac as Choice', 'Top Account Deep Dive', 'NCR Enroll Date', 'NCR Group', 'NCR Program', 'NCR Reseller', 'NCR Status', 'Account Owner', 'Disti/T1 Reseller', 'T2 Reseller', 'Top Account', 'AE', 'AEM', 'week_in_fiscal_quarter', 'PROJECT_short_DESC', 'FPH1', 'FPH2', 'FPH3', 'FPH4', 'color_short_desc', 'storesize_long_desc', 'CTO_IND', 'WIRELESS_LONG_DESC']\n"]}], "source": ["print(list(pplall))"]}, {"cell_type": "code", "execution_count": null, "id": "7b164ec5-00b8-4d49-86a2-5687748c5735", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 243, "id": "9bfa3d5e-59dc-4f13-96fd-6ffd66954e74", "metadata": {}, "outputs": [], "source": ["# pplall2 = pplall[(pplall['FPH1'].isin(['Mac','iPad','iPhone','Watch'])&(pplall['Probability (%)'].isin([75.,100.]))]\n", "pplall2 = pplall[(pplall['FPH1'].isin(['Mac', 'iPad', 'iPhone', 'Watch'])) & (pplall['Probability (%)'].isin([75., 100.]))]"]}, {"cell_type": "code", "execution_count": 244, "id": "a5fba404-80a4-4b9d-9c10-a214076be829", "metadata": {}, "outputs": [], "source": ["# 仅保留 FY19 到 FY24 的数据\n", "pplall2 = pplall2[pplall2['fiscal_year'].isin(['FY19','FY20','FY21','FY22','FY23','FY24'])]"]}, {"cell_type": "code", "execution_count": 245, "id": "f661c47b-a4f8-45c6-99fa-fdd9acab3884", "metadata": {}, "outputs": [], "source": ["part1_an = pplall2[pplall2['fiscal_year'].isin(['FY19','FY20','FY21'])]['Account Name'].unique().tolist()\n", "\n", "part2_an = pplall2[pplall2['fiscal_year'].isin(['FY22','FY23','FY24'])]['Account Name'].unique().tolist()\n", "\n", "# 转换为集合并求差集\n", "unique_in_part2_an = list(set(part2_an) - set(part1_an))\n", "\n", "# 假设 unique_in_part2_an 已经定义\n", "pplall2['custom_new'] = pplall2['Account Name'].isin(unique_in_part2_an)"]}, {"cell_type": "code", "execution_count": 246, "id": "2961a20a-abe7-4e56-8da6-9eec8d119fae", "metadata": {}, "outputs": [], "source": ["pplall2 = pd.merge(pplall2, top_account_ticked2, on=['Account Name'], how='left')"]}, {"cell_type": "code", "execution_count": 247, "id": "8af7d767-7ee8-431a-a9c8-587b3a985364", "metadata": {}, "outputs": [], "source": ["pplall2 = pd.merge(pplall2, qccv24_2, on=['Account Name'], how='left')\n", "pplall2 = pd.merge(pplall2, industry_mapping, on=['Industry Category 行业门类'], how='left')"]}, {"cell_type": "code", "execution_count": 248, "id": "fd0de3f1-e814-4631-b577-f72204cdcabe", "metadata": {}, "outputs": [], "source": ["pplall2['Industry_Final'] = np.nan"]}, {"cell_type": "code", "execution_count": 249, "id": "b2c1f82c-e93f-4357-9643-4e715528f2ab", "metadata": {}, "outputs": [], "source": ["pplall2['Industry_Final'] = pplall2['QCC 行业整合']\n"]}, {"cell_type": "code", "execution_count": 250, "id": "b78a4fc8-a06d-48d6-8925-74da629e2e1e", "metadata": {}, "outputs": [], "source": ["pplall2['Industry_Final'] = np.where(pplall2['Industry_Final'] == 'Other', pplall2['Group Sub Segment'], pplall2['Industry_Final'])\n", "pplall2['Industry_Final'] = pplall2['Industry_Final'].fillna('Other')"]}, {"cell_type": "code", "execution_count": 251, "id": "ef148a14-139e-4d8c-82d5-a9bcf04f3ab3", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Leasing', 'Technology', 'Manufacture', 'Retail', 'Finance',\n", "       'Service', 'Utilities', 'E-Learning', 'Others', 'Health Care',\n", "       'Manufacturing', 'Financial Services', 'Transportation', 'Media',\n", "       'University', 'Healthcare'], dtype=object)"]}, "execution_count": 251, "metadata": {}, "output_type": "execute_result"}], "source": ["pplall2['Industry_Final'].unique()"]}, {"cell_type": "code", "execution_count": 252, "id": "be323585-ddea-4857-827d-79b54ca461b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ProductLineRevenue', 'fiscal_year', 'fiscal_qtr_year_name', 'Product Family', 'fiscal_quarter', 'fiscal_week', 'Oppty Line Item ID', 'MPN_ID', 'Quantity', 'Line of Business', 'Deal type', 'Sold To ID', 'Reseller Apple ID', 'Apple ID', 'Reseller Track', 'ESC Store', 'Opportunity Type', 'Leasing or Not', 'Penetrated Account', 'Opportunity ID', 'Opportunity Name', 'Probability (%)', 'Account Group', 'Group Province', 'Group City', 'Province/Region', 'City', 'Group Sub Segment', 'Group Vertical Industry', 'Mac as Choice加入时间', 'Mac as Choice start time', 'Segment', 'Large Account', 'Total Mac Demand', 'PC Install Base', 'FY22 Fcst', 'FY23 Fcst', 'Industry Target Account', 'Source Detail', 'Sales Region', 'Account Name', 'Account ID', 'Mac as Choice', 'Top Account Deep Dive', 'NCR Enroll Date', 'NCR Group', 'NCR Program', 'NCR Reseller', 'NCR Status', 'Account Owner', 'Disti/T1 Reseller', 'T2 Reseller', 'Top Account', 'AE', 'AEM', 'week_in_fiscal_quarter', 'PROJECT_short_DESC', 'FPH1', 'FPH2', 'FPH3', 'FPH4', 'color_short_desc', 'storesize_long_desc', 'CTO_IND', 'WIRELESS_LONG_DESC', 'custom_new', 'Top Tag', 'Industry Category 行业门类', '参保人数范围', '参保人数', 'qccv24_tag', 'QCC 行业整合', 'SF Industry', 'Industry_Final']\n"]}], "source": ["print(list(pplall2))"]}, {"cell_type": "code", "execution_count": null, "id": "7d8a95c6-8c06-4f1c-8c3b-c17092c191ca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 253, "id": "68e2e767-3949-4a75-b856-bd25f4aab890", "metadata": {}, "outputs": [], "source": ["# pplall2['Industry_Final'] = np.where(~pplall2['QCC 行业整合'].isna(), pplall2['QCC 行业整合'], pplall2['Industry_Final'])\n", "# pplall2['Industry_Final'] = np.where(pplall2['Industry_Final'].isna(), pplall2['Group Sub Segment'], pplall2['Industry_Final'])\n", "# # 将 'QCC 行业整合' 列中值为 'Other' 的行替换为 'Group Sub Segment' 列中的值\n", "# pplall2['QCC 行业整合'] = np.where(pplall2['QCC 行业整合'] == 'Other', pplall2['Group Sub Segment'], pplall2['QCC 行业整合'])\n", "# pplall2['Industry_Final'] = pplall2['Industry_Final'].fillna('Other')"]}, {"cell_type": "code", "execution_count": 240, "id": "d3a40f22-3911-4e96-9aa9-6aead62b0bc6", "metadata": {}, "outputs": [], "source": ["pplall2.loc[pplall2['Account Name'] == '海南新博航数码科技有限公司', 'Quantity'] = 0"]}, {"cell_type": "code", "execution_count": 254, "id": "dbf5e5d7-adff-46d9-b36a-0dcd3a67739a", "metadata": {}, "outputs": [], "source": ["pplall2['update_time'] = datetime.datetime.now()"]}, {"cell_type": "code", "execution_count": 255, "id": "0ddfadc6-720a-4f71-a614-03e130d0e926", "metadata": {}, "outputs": [], "source": ["pplall2.to_csv('one_time_startfromFY19_getpplby_api_data_for_tableau.csv', index=False)"]}, {"cell_type": "code", "execution_count": 222, "id": "45cc0d76-e9f2-4460-9e39-3181147053f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Leasing', 'Technology', 'Manufacture', 'Retail', 'Finance',\n", "       'Service', 'Utilities', 'E-Learning', 'Other'], dtype=object)"]}, "execution_count": 222, "metadata": {}, "output_type": "execute_result"}], "source": ["pplall2['QCC 行业整合'].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "1b606b82-62d3-4565-81f3-6742f3b1edb5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "65e97ce3-7f3c-42bc-9026-935395679ecf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ML", "language": "python", "name": "ml"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}